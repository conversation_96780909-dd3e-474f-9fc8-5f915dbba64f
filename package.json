{"name": "fspro-react", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "deploy:build": "node deploy.js", "deploy:hostinger": "npm run build && echo 'Build complete! Upload the dist folder contents to your Hostinger public_html directory.'"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "firebase": "^10.7.1", "lucide-react": "^0.294.0", "date-fns": "^2.30.0", "react-hot-toast": "^2.4.1"}, "devDependencies": {"@vitejs/plugin-react": "^4.2.1", "vite": "^5.0.8"}}