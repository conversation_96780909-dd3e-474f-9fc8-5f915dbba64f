# Force correct MIME types for JavaScript and CSS files
AddType application/javascript .js
AddType text/css .css
AddType application/json .json

# Handle React Router routing
RewriteEngine On

# Don't rewrite files that exist
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d

# Don't rewrite asset files
RewriteCond %{REQUEST_URI} !^/assets/
RewriteCond %{REQUEST_URI} !^/images/
RewriteCond %{REQUEST_URI} !\.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot|json)$

# Redirect everything else to index.html
RewriteRule ^(.*)$ /index.html [L,QSA]
