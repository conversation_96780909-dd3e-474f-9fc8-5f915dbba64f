#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

console.log('🚀 Starting FSPro Hostinger Deployment Build...\n');

try {
  // Clean previous build
  console.log('🧹 Cleaning previous build...');
  if (fs.existsSync('dist')) {
    fs.rmSync('dist', { recursive: true, force: true });
  }
  if (fs.existsSync('fspro-hostinger-deployment.zip')) {
    fs.unlinkSync('fspro-hostinger-deployment.zip');
  }

  // Install dependencies
  console.log('📦 Installing dependencies...');
  execSync('npm install', { stdio: 'inherit' });

  // Build the project
  console.log('🔨 Building project for production...');
  execSync('npm run build', { stdio: 'inherit' });

  // Verify build
  if (!fs.existsSync('dist')) {
    throw new Error('Build failed - dist directory not found');
  }

  console.log('✅ Build completed successfully!');
  console.log('\n📁 Build output is in the "dist" directory');
  console.log('📋 Next steps:');
  console.log('   1. Upload all contents of the "dist" folder to your Hostinger public_html directory');
  console.log('   2. Make sure .htaccess file is uploaded and working');
  console.log('   3. Test your application at your domain');
  console.log('\n🎉 Ready for Hostinger deployment!');

} catch (error) {
  console.error('❌ Deployment build failed:', error.message);
  process.exit(1);
}
