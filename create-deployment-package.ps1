# FSPro Hostinger Deployment Package Creator
Write-Host "Creating FSPro Hostinger Deployment Package..." -ForegroundColor Green

# Check if dist folder exists
if (-not (Test-Path "dist")) {
    Write-Host "Error: dist folder not found. Please run 'npm run build' first." -ForegroundColor Red
    exit 1
}

# Create deployment package name with timestamp
$timestamp = Get-Date -Format "yyyyMMdd-HHmmss"
$packageName = "fspro-hostinger-deployment-$timestamp.zip"

# Remove old deployment packages (keep only the latest 3)
Get-ChildItem -Path "." -Name "fspro-hostinger-deployment-*.zip" |
    Sort-Object LastWriteTime -Descending |
    Select-Object -Skip 2 |
    ForEach-Object { Remove-Item $_ -Force }

Write-Host "Creating deployment package: $packageName" -ForegroundColor Yellow

# Create the zip file
try {
    Compress-Archive -Path "dist\*" -DestinationPath $packageName -Force
    Write-Host "Deployment package created successfully!" -ForegroundColor Green
    Write-Host ""
    Write-Host "Package Details:" -ForegroundColor Cyan
    Write-Host "   Package Name: $packageName"
    Write-Host "   Package Size: $([math]::Round((Get-Item $packageName).Length / 1MB, 2)) MB"
    Write-Host ""
    Write-Host "Next Steps:" -ForegroundColor Yellow
    Write-Host "   1. Extract this ZIP file"
    Write-Host "   2. Upload ALL extracted contents to your Hostinger public_html folder"
    Write-Host "   3. Ensure .htaccess file is uploaded (it might be hidden)"
    Write-Host "   4. Test your application at your domain"
    Write-Host ""
    Write-Host "Important Files Included:" -ForegroundColor Magenta
    Write-Host "   - index.html (main application file)"
    Write-Host "   - .htaccess (routing configuration)"
    Write-Host "   - assets/ folder (JavaScript, CSS, images)"
    Write-Host "   - favicon.ico"
    Write-Host "   - robots.txt"
    Write-Host "   - _redirects (backup routing)"
    Write-Host ""
    Write-Host "Ready for Hostinger deployment!" -ForegroundColor Green
}
catch {
    Write-Host "Error creating deployment package: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
