// Chatbot service for EMS system using Llama API
const LLAMA_API_KEY = '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';
const LLAMA_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// EMS System Knowledge Base
const EMS_CONTEXT = `
You are an intelligent AI assistant for FSPro Employee Management System (EMS). You understand natural language and help employees and administrators navigate and use the system effectively.

NATURAL LANGUAGE UNDERSTANDING:
- Understand casual, conversational language
- Interpret questions asked in different ways
- Recognize synonyms and alternative phrasings
- Provide helpful responses even for unclear questions
- Ask clarifying questions when needed
- Be conversational and friendly while remaining professional

COMPANY INFORMATION:
FSProgrammers is a leading software house that specializes in developing innovative business solutions. The company focuses on creating efficient, user-friendly software systems that help businesses streamline their operations and improve productivity.

ABOUT FSPro EMS:
FSPro (FSProgrammers Professional) Employee Management System is a comprehensive, modern solution designed specifically for businesses to manage their workforce effectively. This system was developed by FSProgrammers to address the common challenges faced by organizations in managing employee attendance, tasks, schedules, and overall productivity.

WHAT IS EMS (Employee Management System)?
An Employee Management System (EMS) is a digital platform that helps organizations manage all aspects of their workforce. It serves as a centralized hub for:
- Employee information and profiles
- Attendance tracking and time management
- Task assignment and progress monitoring
- Leave and holiday management
- Performance reporting and analytics
- Compensation and overtime tracking
- Administrative oversight and control

WHY FSPro EMS?
FSPro EMS stands out because it's:
- Built by FSProgrammers with years of software development expertise
- Designed for modern businesses of all sizes
- User-friendly with intuitive interfaces
- Comprehensive yet simple to use
- Cloud-based for accessibility anywhere
- Secure and reliable for business-critical data

SYSTEM OVERVIEW:
FSPro is a comprehensive Employee Management System with the following features:

FOR EMPLOYEES:
1. Dashboard - View personal statistics, recent activities, and quick actions
2. Check In/Out - Record attendance with time validation and location tracking
3. Time Table - View and manage weekly work schedules
4. Progress Reports - Submit daily/weekly progress reports with task details
5. Holiday Requests - Submit and track holiday/leave requests
6. My Tasks - View assigned tasks and update their status
7. Compensate Hours - Request compensation for overtime or missed hours

FOR ADMINISTRATORS:
1. Admin Dashboard - Overview of all employees, attendance, and system metrics
2. Employee Management - Add, edit, and manage employee profiles and roles
3. Time Table Management - Create and assign work schedules to employees
4. Progress Reports - Review and approve employee progress reports
5. Attendance Records - Monitor and manage all employee attendance data
6. Holiday Management - Review and approve/reject holiday requests
7. Task Management - Create, assign, and track tasks for employees
8. Compensation Records - Review and approve compensation requests

DETAILED SECTION GUIDES:

=== EMPLOYEE SECTIONS ===

DASHBOARD:
- Shows personal statistics: total check-ins, check-outs, late check-ins, pending leaves
- Displays recent activity feed
- Quick access to main functions
- Real-time updates of your work metrics

CHECK IN/OUT:
- Click "Check In" button to record arrival time
- Click "Check Out" button to record departure time
- System automatically tracks time and validates against schedule
- Location tracking may be enabled for verification
- Cannot check in/out outside designated work hours without admin approval

TIME TABLE:
- View your weekly work schedule
- See assigned work days and hours
- Schedule automatically resets every week
- Cannot modify schedule (admin-only function)
- Shows upcoming shifts and time slots

PROGRESS REPORTS:
- Submit daily or weekly progress reports
- Include task details, accomplishments, and challenges
- Select date range for the report period
- Add detailed descriptions of work completed
- Submit for admin review and approval

HOLIDAY REQUESTS:
- Submit leave requests with start and end dates
- Provide reason for leave request
- Track status: Pending, Approved, or Rejected
- View history of all previous requests
- Receive notifications when status changes

MY TASKS:
- View all tasks assigned to you
- Update task status: Not Started, In Progress, Completed
- Add progress notes and comments
- See task deadlines and priorities
- Filter tasks by status or date

COMPENSATE HOURS:
- Request compensation for overtime work
- Submit requests for missed hours due to system issues
- Provide detailed explanation and evidence
- Track approval status
- View compensation history

=== ADMIN SECTIONS ===

ADMIN DASHBOARD:
- Overview of all system metrics
- Total employees, attendance records, pending items
- Quick action buttons for common admin tasks
- Recent activity across all employees
- System-wide statistics and trends

EMPLOYEE MANAGEMENT:
- Add new employees with name, email, role, department
- Edit existing employee profiles and information
- Manage user roles (Employee/Admin)
- Deactivate or remove employee accounts
- View employee details and work history
- Reset employee passwords if needed

TIME TABLE MANAGEMENT:
- Create weekly schedules for all employees
- Assign work days and hours to specific employees
- Modify existing schedules as needed
- Set up recurring weekly patterns
- Handle schedule conflicts and overlaps
- Bulk schedule operations for multiple employees

PROGRESS REPORTS (Admin):
- Review all employee progress reports
- Approve or request revisions for reports
- Add admin comments and feedback
- Track report submission trends
- Generate progress analytics
- Export reports for external use

ATTENDANCE RECORDS:
- Monitor all employee check-ins and check-outs
- View attendance patterns and trends
- Identify late arrivals and early departures
- Generate attendance reports
- Handle attendance disputes
- Export attendance data

HOLIDAY MANAGEMENT:
- Review all holiday requests from employees
- Approve or reject leave requests
- Add admin comments to decisions
- Check team availability before approving
- Manage holiday calendars
- Set company holiday policies

TASK MANAGEMENT:
- Create new tasks and assignments
- Assign tasks to specific employees
- Set task priorities and deadlines
- Monitor task progress across teams
- Update task details and requirements
- Generate task completion reports

COMPENSATION RECORDS:
- Review employee compensation requests
- Approve or reject overtime claims
- Verify compensation calculations
- Track compensation history
- Generate payroll-related reports
- Handle compensation disputes

KEY FEATURES:
- Real-time attendance tracking
- Automated weekly schedule resets
- Role-based access control (Employee/Admin)
- Firebase authentication and data storage
- Responsive design for mobile and desktop
- Comprehensive reporting and analytics

COMMON WORKFLOWS:
1. Daily Check-in: Employee logs in → Goes to Check In/Out → Records attendance
2. Task Management: Admin creates task → Assigns to employee → Employee updates progress
3. Holiday Request: Employee submits request → Admin reviews → Approves/Rejects
4. Progress Reporting: Employee submits daily/weekly reports → Admin reviews

NATURAL LANGUAGE EXAMPLES:
Users might ask questions in various ways:
- "I need to clock in" = Check In/Out section
- "Where do I mark my time?" = Check In/Out section
- "I want time off" = Holiday Requests section
- "How do I get vacation approved?" = Holiday Requests section
- "I finished my work" = My Tasks section to update status
- "What am I supposed to do today?" = My Tasks section
- "I worked extra hours" = Compensate Hours section
- "My boss wants a report" = Progress Reports section
- "When do I work this week?" = Time Table section
- "I can't get in" = Login/access issues
- "This isn't working" = Technical troubleshooting

RESPONSE GUIDELINES:
- Be conversational and friendly
- Understand context and intent
- Provide step-by-step guidance
- Ask clarifying questions if needed
- Offer related suggestions
- Use simple, clear language
- Be patient with unclear questions
- Always try to help, even with vague queries

Always provide helpful, specific guidance about using these features. Be conversational, understanding, and informative.
`;

class ChatbotService {
  constructor() {
    this.conversationHistory = [];
  }

  // Preprocess user message to enhance natural language understanding
  preprocessUserMessage(userMessage) {
    // Convert common natural language phrases to more specific queries
    const naturalLanguageMap = {
      // Clock in/out variations
      'clock in': 'check in for work',
      'punch in': 'check in for work',
      'sign in': 'check in for work',
      'mark attendance': 'check in for work',
      'clock out': 'check out from work',
      'punch out': 'check out from work',
      'sign out': 'check out from work',

      // Time off variations
      'time off': 'holiday request',
      'vacation': 'holiday request',
      'leave': 'holiday request',
      'day off': 'holiday request',
      'sick leave': 'holiday request',

      // Task variations
      'my work': 'my tasks',
      'assignments': 'my tasks',
      'to do': 'my tasks',
      'what to do': 'my tasks',
      'work items': 'my tasks',

      // Schedule variations
      'my schedule': 'time table',
      'work hours': 'time table',
      'shift': 'time table',
      'when do i work': 'time table',

      // Report variations
      'report': 'progress report',
      'update': 'progress report',
      'status': 'progress report',

      // Compensation variations
      'overtime': 'compensation',
      'extra hours': 'compensation',
      'missed time': 'compensation',

      // Admin variations
      'manage employees': 'employee management',
      'add employee': 'employee management',
      'staff': 'employee management'
    };

    let processedMessage = userMessage.toLowerCase();

    // Replace natural language phrases with more specific terms
    for (const [natural, specific] of Object.entries(naturalLanguageMap)) {
      if (processedMessage.includes(natural)) {
        processedMessage = processedMessage.replace(natural, specific);
      }
    }

    // Add context clues for better understanding
    if (processedMessage.includes('how') || processedMessage.includes('where') || processedMessage.includes('what')) {
      processedMessage += ' - Please provide step-by-step guidance';
    }

    return processedMessage;
  }

  async sendMessage(userMessage, userRole = 'employee') {
    try {
      // Process the user message to extract intent
      const processedMessage = this.preprocessUserMessage(userMessage);

      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        content: processedMessage
      });

      // Prepare the system message with EMS context and natural language understanding
      const systemMessage = {
        role: 'system',
        content: `${EMS_CONTEXT}\n\nUser Role: ${userRole}\n\nYou are a helpful, conversational assistant for the FSPro EMS system. Understand natural language queries and provide friendly, concise guidance. If the user's question is unclear, ask for clarification. Always maintain a helpful, supportive tone.`
      };

      // Add a few examples to help with few-shot learning
      const exampleMessages = [
        {
          role: 'user',
          content: 'I need to clock in'
        },
        {
          role: 'assistant',
          content: 'I can help you clock in! Go to the "Check In/Out" section from the sidebar menu. Once there, you\'ll see a "Check In" button - just click that and your attendance will be recorded with the current time. Is there anything specific about the check-in process you\'d like to know?'
        },
        {
          role: 'user',
          content: 'How do I get time off?'
        },
        {
          role: 'assistant',
          content: 'To request time off, go to the "Holiday Requests" section in the sidebar. Click the "New Request" button, then select your start and end dates, add a reason for your leave, and submit the request. Your administrator will review it and either approve or reject it. You can check the status of your request in the same section. Would you like more details about this process?'
        }
      ];

      // Prepare messages for API with examples for few-shot learning
      const messages = [
        systemMessage,
        ...exampleMessages,
        ...this.conversationHistory.slice(-10) // Keep last 10 messages for context
      ];

      const response = await fetch(LLAMA_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${LLAMA_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'FSPro EMS Chatbot'
        },
        body: JSON.stringify({
          model: 'meta-llama/llama-3.1-8b-instruct:free',
          messages: messages,
          max_tokens: 600,
          temperature: 0.7,
          top_p: 0.9,
          presence_penalty: 0.6, // Encourage more diverse responses
          frequency_penalty: 0.2, // Reduce repetition
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const botResponse = data.choices[0]?.message?.content || 'Sorry, I could not process your request.';

      // Add bot response to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: botResponse
      });

      return botResponse;
    } catch (error) {
      console.error('Chatbot API Error:', error);
      return this.getFallbackResponse(userMessage);
    }
  }

  getFallbackResponse(userMessage) {
    const message = userMessage.toLowerCase();

    // Dashboard related
    if (message.includes('dashboard') || message.includes('overview')) {
      return "Dashboard shows your personal statistics including check-ins, check-outs, and pending items. For admins, it displays system-wide metrics and quick action buttons for common tasks.";
    }

    // Check-in/Attendance related
    if (message.includes('check in') || message.includes('check out') || message.includes('attendance')) {
      return "To check in/out: Go to 'Check In/Out' section from the sidebar. Click the check-in button to record your arrival time, and check-out button when leaving. The system automatically tracks time and validates against your schedule.";
    }

    // Task related
    if (message.includes('task') || message.includes('assignment')) {
      return "For tasks: Employees go to 'My Tasks' to view assigned tasks, update status, and add progress notes. Admins use 'Task Management' to create new tasks, assign them to employees, set priorities and deadlines.";
    }

    // Holiday/Leave related
    if (message.includes('holiday') || message.includes('leave') || message.includes('vacation')) {
      return "For holidays: Employees go to 'Holiday Requests' to submit leave requests with dates and reason. Admins review requests in 'Holiday Management' and can approve/reject with comments.";
    }

    // Progress Report related
    if (message.includes('progress') || message.includes('report')) {
      return "For progress reports: Employees submit reports in 'Progress Report' section with date range and work details. Admins review all reports in 'Progress Reports' section and can approve or request revisions.";
    }

    // Schedule/Timetable related
    if (message.includes('schedule') || message.includes('timetable') || message.includes('time table')) {
      return "For schedules: Employees view their weekly schedule in 'Time Table' section. Admins manage all schedules in 'Time Table Management' where they can create, modify, and assign work schedules to employees.";
    }

    // Compensation related
    if (message.includes('compensation') || message.includes('overtime') || message.includes('compensate')) {
      return "For compensation: Employees request compensation for overtime or missed hours in 'Compensate Hours' section. Admins review and approve requests in 'Compensation Records' section.";
    }

    // Employee Management (Admin)
    if (message.includes('employee management') || message.includes('add employee') || message.includes('manage employee')) {
      return "Employee Management (Admin only): Go to 'Employee Management' to add new employees, edit profiles, manage roles, and handle employee accounts. You can add name, email, role, and department information.";
    }

    // Attendance Records (Admin)
    if (message.includes('attendance record') || message.includes('monitor attendance')) {
      return "Attendance Records (Admin only): Go to 'Attendance Records' to monitor all employee check-ins/check-outs, view attendance patterns, identify late arrivals, and generate attendance reports.";
    }

    // General navigation
    if (message.includes('navigate') || message.includes('find') || message.includes('where')) {
      return "Navigation: Use the sidebar menu to access different sections. Employees see Dashboard, Check In/Out, Time Table, Progress Report, Holiday Requests, My Tasks, and Compensate Hours. Admins have additional management sections.";
    }

    // Login/Access issues
    if (message.includes('login') || message.includes('access') || message.includes('password')) {
      return "For login issues: Contact your administrator to reset your password or check your account status. Make sure you're using the correct email address associated with your employee account.";
    }

    // EMS Overview questions
    if (message.includes('what is ems') || message.includes('tell me about ems') || message.includes('explain ems') ||
        message.includes('about the system') || message.includes('overview of ems')) {
      return "FSPro EMS (Employee Management System) is a comprehensive workforce management solution developed by FSProgrammers, a leading software house. It helps organizations manage employee attendance, tasks, schedules, leave requests, progress reports, and compensation. The system provides both employee and admin interfaces to streamline workforce management and improve productivity.";
    }

    // FSProgrammers questions
    if (message.includes('fsprogrammers') || message.includes('who made') || message.includes('who developed') ||
        message.includes('company') || message.includes('software house')) {
      return "FSProgrammers is a leading software house that specializes in developing innovative business solutions. They created the FSPro EMS (Employee Management System) to help businesses efficiently manage their workforce with features like attendance tracking, task management, scheduling, and comprehensive reporting.";
    }

    return "I'm here to help you with the FSPro EMS system! You can ask me about any section: Dashboard, Check In/Out, Tasks, Holidays, Progress Reports, Schedules, Compensation, or Admin functions. You can also ask about what EMS is or about FSProgrammers, the company that developed this system. What would you like to know?";
  }

  clearHistory() {
    this.conversationHistory = [];
  }

  getQuickSuggestions(userRole) {
    const commonSuggestions = [
      // Specific questions with clear answers
      "What is FSPro EMS and who developed it?",
      "How do I check in for my shift?",
      "How do I submit a progress report?",
      "How do I request vacation days?",
      "Where can I see my assigned tasks?",
      "How do I view my weekly schedule?",
      "How do I request compensation for overtime?",
      "What information is shown on my dashboard?",
      "How do I update the status of my task?",
      "How do I check my attendance history?",
      "How do I know if my leave request was approved?"
    ];

    const adminSuggestions = [
      // Specific admin questions with clear answers
      "How do I add a new employee to the system?",
      "How do I create and assign tasks to employees?",
      "How do I approve or reject holiday requests?",
      "How do I view all employee attendance records?",
      "How do I create weekly schedules for employees?",
      "Guide me through the compensation management section",
      "How do I review employee progress reports?",
      "How do I generate attendance reports for payroll?",
      "How do I reset an employee's password?",
      "How do I handle late check-in notifications?",
      "How do I assign the same task to multiple employees?",
      "How do I modify an existing employee schedule?"
    ];

    return userRole === 'admin' ? [...commonSuggestions, ...adminSuggestions] : commonSuggestions;
  }
}

export const chatbotService = new ChatbotService();
