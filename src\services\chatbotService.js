// Chatbot service for EMS system using Llama API
const LLAMA_API_KEY = '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';
const LLAMA_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// EMS System Knowledge Base
const EMS_CONTEXT = `
You are an AI assistant for FSPro Employee Management System (EMS). You help employees and administrators understand and use the system effectively.

SYSTEM OVERVIEW:
FSPro is a comprehensive Employee Management System with the following features:

FOR EMPLOYEES:
1. Dashboard - View personal statistics, recent activities, and quick actions
2. Check In/Out - Record attendance with time validation and location tracking
3. Time Table - View and manage weekly work schedules
4. Progress Reports - Submit daily/weekly progress reports with task details
5. Holiday Requests - Submit and track holiday/leave requests
6. My Tasks - View assigned tasks and update their status
7. Compensate Hours - Request compensation for overtime or missed hours

FOR ADMINISTRATORS:
1. Admin Dashboard - Overview of all employees, attendance, and system metrics
2. Employee Management - Add, edit, and manage employee profiles and roles
3. Time Table Management - Create and assign work schedules to employees
4. Progress Reports - Review and approve employee progress reports
5. Attendance Records - Monitor and manage all employee attendance data
6. Holiday Management - Review and approve/reject holiday requests
7. Task Management - Create, assign, and track tasks for employees
8. Compensation Records - Review and approve compensation requests

KEY FEATURES:
- Real-time attendance tracking
- Automated weekly schedule resets
- Role-based access control (Employee/Admin)
- Firebase authentication and data storage
- Responsive design for mobile and desktop
- Comprehensive reporting and analytics

COMMON WORKFLOWS:
1. Daily Check-in: Employee logs in → Goes to Check In/Out → Records attendance
2. Task Management: Admin creates task → Assigns to employee → Employee updates progress
3. Holiday Request: Employee submits request → Admin reviews → Approves/Rejects
4. Progress Reporting: Employee submits daily/weekly reports → Admin reviews

Always provide helpful, specific guidance about using these features. Be concise but informative.
`;

class ChatbotService {
  constructor() {
    this.conversationHistory = [];
  }

  async sendMessage(userMessage, userRole = 'employee') {
    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        content: userMessage
      });

      // Prepare the system message with EMS context
      const systemMessage = {
        role: 'system',
        content: `${EMS_CONTEXT}\n\nUser Role: ${userRole}\nProvide helpful guidance about the FSPro EMS system. Keep responses concise and actionable.`
      };

      // Prepare messages for API
      const messages = [
        systemMessage,
        ...this.conversationHistory.slice(-10) // Keep last 10 messages for context
      ];

      const response = await fetch(LLAMA_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${LLAMA_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'FSPro EMS Chatbot'
        },
        body: JSON.stringify({
          model: 'meta-llama/llama-3.1-8b-instruct:free',
          messages: messages,
          max_tokens: 500,
          temperature: 0.7,
          top_p: 0.9,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const botResponse = data.choices[0]?.message?.content || 'Sorry, I could not process your request.';

      // Add bot response to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: botResponse
      });

      return botResponse;
    } catch (error) {
      console.error('Chatbot API Error:', error);
      return this.getFallbackResponse(userMessage);
    }
  }

  getFallbackResponse(userMessage) {
    const message = userMessage.toLowerCase();
    
    if (message.includes('check in') || message.includes('attendance')) {
      return "To check in/out: Go to the 'Check In/Out' section from the sidebar. Click the check-in button to record your attendance. The system will automatically track your time and location.";
    }
    
    if (message.includes('task') || message.includes('assignment')) {
      return "To view your tasks: Go to 'My Tasks' section. You can see all assigned tasks, update their status, and add progress notes. Admins can create new tasks in 'Task Management'.";
    }
    
    if (message.includes('holiday') || message.includes('leave')) {
      return "To request holiday: Go to 'Holiday Requests' section, fill in the dates and reason, then submit. Admins will review and approve/reject your request.";
    }
    
    if (message.includes('progress') || message.includes('report')) {
      return "To submit progress reports: Go to 'Progress Report' section, select the date range, describe your work completed, and submit. This helps track your productivity.";
    }
    
    if (message.includes('schedule') || message.includes('timetable')) {
      return "To view your schedule: Go to 'Time Table' section to see your weekly work schedule. Admins can modify schedules in 'Time Table Management'.";
    }
    
    return "I'm here to help you with the FSPro EMS system! You can ask me about check-in/out, tasks, holidays, progress reports, schedules, or any other system features.";
  }

  clearHistory() {
    this.conversationHistory = [];
  }

  getQuickSuggestions(userRole) {
    const commonSuggestions = [
      "How do I check in for work?",
      "How to submit a progress report?",
      "How to request holiday leave?",
      "How to view my tasks?",
      "How to check my schedule?"
    ];

    const adminSuggestions = [
      "How to add a new employee?",
      "How to create tasks for employees?",
      "How to approve holiday requests?",
      "How to view attendance records?",
      "How to manage employee schedules?"
    ];

    return userRole === 'admin' ? [...commonSuggestions, ...adminSuggestions] : commonSuggestions;
  }
}

export const chatbotService = new ChatbotService();
