// Chatbot service for EMS system using Llama API
const LLAMA_API_KEY = '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';
const LLAMA_API_URL = 'https://openrouter.ai/api/v1/chat/completions';

// EMS System Knowledge Base
const EMS_CONTEXT = `
You are an AI assistant for FSPro Employee Management System (EMS). You help employees and administrators understand and use the system effectively.

SYSTEM OVERVIEW:
FSPro is a comprehensive Employee Management System with the following features:

FOR EMPLOYEES:
1. Dashboard - View personal statistics, recent activities, and quick actions
2. Check In/Out - Record attendance with time validation and location tracking
3. Time Table - View and manage weekly work schedules
4. Progress Reports - Submit daily/weekly progress reports with task details
5. Holiday Requests - Submit and track holiday/leave requests
6. My Tasks - View assigned tasks and update their status
7. Compensate Hours - Request compensation for overtime or missed hours

FOR ADMINISTRATORS:
1. <PERSON>min Dashboard - Overview of all employees, attendance, and system metrics
2. Employee Management - Add, edit, and manage employee profiles and roles
3. Time Table Management - Create and assign work schedules to employees
4. Progress Reports - Review and approve employee progress reports
5. Attendance Records - Monitor and manage all employee attendance data
6. Holiday Management - Review and approve/reject holiday requests
7. Task Management - Create, assign, and track tasks for employees
8. Compensation Records - Review and approve compensation requests

DETAILED SECTION GUIDES:

=== EMPLOYEE SECTIONS ===

DASHBOARD:
- Shows personal statistics: total check-ins, check-outs, late check-ins, pending leaves
- Displays recent activity feed
- Quick access to main functions
- Real-time updates of your work metrics

CHECK IN/OUT:
- Click "Check In" button to record arrival time
- Click "Check Out" button to record departure time
- System automatically tracks time and validates against schedule
- Location tracking may be enabled for verification
- Cannot check in/out outside designated work hours without admin approval

TIME TABLE:
- View your weekly work schedule
- See assigned work days and hours
- Schedule automatically resets every week
- Cannot modify schedule (admin-only function)
- Shows upcoming shifts and time slots

PROGRESS REPORTS:
- Submit daily or weekly progress reports
- Include task details, accomplishments, and challenges
- Select date range for the report period
- Add detailed descriptions of work completed
- Submit for admin review and approval

HOLIDAY REQUESTS:
- Submit leave requests with start and end dates
- Provide reason for leave request
- Track status: Pending, Approved, or Rejected
- View history of all previous requests
- Receive notifications when status changes

MY TASKS:
- View all tasks assigned to you
- Update task status: Not Started, In Progress, Completed
- Add progress notes and comments
- See task deadlines and priorities
- Filter tasks by status or date

COMPENSATE HOURS:
- Request compensation for overtime work
- Submit requests for missed hours due to system issues
- Provide detailed explanation and evidence
- Track approval status
- View compensation history

=== ADMIN SECTIONS ===

ADMIN DASHBOARD:
- Overview of all system metrics
- Total employees, attendance records, pending items
- Quick action buttons for common admin tasks
- Recent activity across all employees
- System-wide statistics and trends

EMPLOYEE MANAGEMENT:
- Add new employees with name, email, role, department
- Edit existing employee profiles and information
- Manage user roles (Employee/Admin)
- Deactivate or remove employee accounts
- View employee details and work history
- Reset employee passwords if needed

TIME TABLE MANAGEMENT:
- Create weekly schedules for all employees
- Assign work days and hours to specific employees
- Modify existing schedules as needed
- Set up recurring weekly patterns
- Handle schedule conflicts and overlaps
- Bulk schedule operations for multiple employees

PROGRESS REPORTS (Admin):
- Review all employee progress reports
- Approve or request revisions for reports
- Add admin comments and feedback
- Track report submission trends
- Generate progress analytics
- Export reports for external use

ATTENDANCE RECORDS:
- Monitor all employee check-ins and check-outs
- View attendance patterns and trends
- Identify late arrivals and early departures
- Generate attendance reports
- Handle attendance disputes
- Export attendance data

HOLIDAY MANAGEMENT:
- Review all holiday requests from employees
- Approve or reject leave requests
- Add admin comments to decisions
- Check team availability before approving
- Manage holiday calendars
- Set company holiday policies

TASK MANAGEMENT:
- Create new tasks and assignments
- Assign tasks to specific employees
- Set task priorities and deadlines
- Monitor task progress across teams
- Update task details and requirements
- Generate task completion reports

COMPENSATION RECORDS:
- Review employee compensation requests
- Approve or reject overtime claims
- Verify compensation calculations
- Track compensation history
- Generate payroll-related reports
- Handle compensation disputes

KEY FEATURES:
- Real-time attendance tracking
- Automated weekly schedule resets
- Role-based access control (Employee/Admin)
- Firebase authentication and data storage
- Responsive design for mobile and desktop
- Comprehensive reporting and analytics

COMMON WORKFLOWS:
1. Daily Check-in: Employee logs in → Goes to Check In/Out → Records attendance
2. Task Management: Admin creates task → Assigns to employee → Employee updates progress
3. Holiday Request: Employee submits request → Admin reviews → Approves/Rejects
4. Progress Reporting: Employee submits daily/weekly reports → Admin reviews

Always provide helpful, specific guidance about using these features. Be concise but informative.
`;

class ChatbotService {
  constructor() {
    this.conversationHistory = [];
  }

  async sendMessage(userMessage, userRole = 'employee') {
    try {
      // Add user message to conversation history
      this.conversationHistory.push({
        role: 'user',
        content: userMessage
      });

      // Prepare the system message with EMS context
      const systemMessage = {
        role: 'system',
        content: `${EMS_CONTEXT}\n\nUser Role: ${userRole}\nProvide helpful guidance about the FSPro EMS system. Keep responses concise and actionable.`
      };

      // Prepare messages for API
      const messages = [
        systemMessage,
        ...this.conversationHistory.slice(-10) // Keep last 10 messages for context
      ];

      const response = await fetch(LLAMA_API_URL, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${LLAMA_API_KEY}`,
          'Content-Type': 'application/json',
          'HTTP-Referer': window.location.origin,
          'X-Title': 'FSPro EMS Chatbot'
        },
        body: JSON.stringify({
          model: 'meta-llama/llama-3.1-8b-instruct:free',
          messages: messages,
          max_tokens: 500,
          temperature: 0.7,
          top_p: 0.9,
          stream: false
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.status}`);
      }

      const data = await response.json();
      const botResponse = data.choices[0]?.message?.content || 'Sorry, I could not process your request.';

      // Add bot response to conversation history
      this.conversationHistory.push({
        role: 'assistant',
        content: botResponse
      });

      return botResponse;
    } catch (error) {
      console.error('Chatbot API Error:', error);
      return this.getFallbackResponse(userMessage);
    }
  }

  getFallbackResponse(userMessage) {
    const message = userMessage.toLowerCase();

    // Dashboard related
    if (message.includes('dashboard') || message.includes('overview')) {
      return "Dashboard shows your personal statistics including check-ins, check-outs, and pending items. For admins, it displays system-wide metrics and quick action buttons for common tasks.";
    }

    // Check-in/Attendance related
    if (message.includes('check in') || message.includes('check out') || message.includes('attendance')) {
      return "To check in/out: Go to 'Check In/Out' section from the sidebar. Click the check-in button to record your arrival time, and check-out button when leaving. The system automatically tracks time and validates against your schedule.";
    }

    // Task related
    if (message.includes('task') || message.includes('assignment')) {
      return "For tasks: Employees go to 'My Tasks' to view assigned tasks, update status, and add progress notes. Admins use 'Task Management' to create new tasks, assign them to employees, set priorities and deadlines.";
    }

    // Holiday/Leave related
    if (message.includes('holiday') || message.includes('leave') || message.includes('vacation')) {
      return "For holidays: Employees go to 'Holiday Requests' to submit leave requests with dates and reason. Admins review requests in 'Holiday Management' and can approve/reject with comments.";
    }

    // Progress Report related
    if (message.includes('progress') || message.includes('report')) {
      return "For progress reports: Employees submit reports in 'Progress Report' section with date range and work details. Admins review all reports in 'Progress Reports' section and can approve or request revisions.";
    }

    // Schedule/Timetable related
    if (message.includes('schedule') || message.includes('timetable') || message.includes('time table')) {
      return "For schedules: Employees view their weekly schedule in 'Time Table' section. Admins manage all schedules in 'Time Table Management' where they can create, modify, and assign work schedules to employees.";
    }

    // Compensation related
    if (message.includes('compensation') || message.includes('overtime') || message.includes('compensate')) {
      return "For compensation: Employees request compensation for overtime or missed hours in 'Compensate Hours' section. Admins review and approve requests in 'Compensation Records' section.";
    }

    // Employee Management (Admin)
    if (message.includes('employee management') || message.includes('add employee') || message.includes('manage employee')) {
      return "Employee Management (Admin only): Go to 'Employee Management' to add new employees, edit profiles, manage roles, and handle employee accounts. You can add name, email, role, and department information.";
    }

    // Attendance Records (Admin)
    if (message.includes('attendance record') || message.includes('monitor attendance')) {
      return "Attendance Records (Admin only): Go to 'Attendance Records' to monitor all employee check-ins/check-outs, view attendance patterns, identify late arrivals, and generate attendance reports.";
    }

    // General navigation
    if (message.includes('navigate') || message.includes('find') || message.includes('where')) {
      return "Navigation: Use the sidebar menu to access different sections. Employees see Dashboard, Check In/Out, Time Table, Progress Report, Holiday Requests, My Tasks, and Compensate Hours. Admins have additional management sections.";
    }

    // Login/Access issues
    if (message.includes('login') || message.includes('access') || message.includes('password')) {
      return "For login issues: Contact your administrator to reset your password or check your account status. Make sure you're using the correct email address associated with your employee account.";
    }

    return "I'm here to help you with the FSPro EMS system! You can ask me about any section: Dashboard, Check In/Out, Tasks, Holidays, Progress Reports, Schedules, Compensation, or Admin functions. What would you like to know?";
  }

  clearHistory() {
    this.conversationHistory = [];
  }

  getQuickSuggestions(userRole) {
    const commonSuggestions = [
      "How do I check in for work?",
      "How to submit a progress report?",
      "How to request holiday leave?",
      "How to view my tasks?",
      "How to check my schedule?",
      "How to request compensation for overtime?",
      "What information is on my dashboard?",
      "How do I update a task status?",
      "Can I check my attendance history?",
      "How to view my pending leave requests?"
    ];

    const adminSuggestions = [
      "How to add a new employee?",
      "How to create tasks for employees?",
      "How to approve holiday requests?",
      "How to view attendance records?",
      "How to manage employee schedules?",
      "Guide me through the compensation section",
      "How to review progress reports?",
      "How to generate attendance reports?",
      "How to reset an employee password?",
      "How to handle late check-ins?",
      "How to assign tasks to multiple employees?",
      "How to modify an existing schedule?"
    ];

    return userRole === 'admin' ? [...commonSuggestions, ...adminSuggestions] : commonSuggestions;
  }
}

export const chatbotService = new ChatbotService();
