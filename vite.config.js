import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true
  },
  build: {
    outDir: 'dist',
    sourcemap: false,
    minify: 'esbuild',
    assetsDir: 'assets',
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom'],
          firebase: ['firebase/app', 'firebase/auth', 'firebase/firestore'],
          utils: ['date-fns', 'lucide-react', 'react-hot-toast']
        },
        // Ensure consistent file naming for better caching
        entryFileNames: 'assets/[name]-[hash].js',
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: 'assets/[name]-[hash].[ext]'
      }
    },
    // Optimize for production
    target: 'es2015',
    cssCodeSplit: true,
    reportCompressedSize: false
  },
  // Use relative paths for Hostinger compatibility
  base: './',
  // Ensure proper asset handling
  publicDir: 'public',
  // Define for production build
  define: {
    'process.env.NODE_ENV': '"production"'
  }
})
