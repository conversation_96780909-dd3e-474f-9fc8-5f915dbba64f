# 🤖 FSPro EMS Chatbot Training Guide

## 📚 **Complete Question Bank for Testing**

This guide contains comprehensive questions to test and train your chatbot. Use these questions to ensure the chatbot provides accurate, helpful responses for all system features.

---

## 🏠 **DASHBOARD QUESTIONS**

### Employee Dashboard:
- "What information is shown on my dashboard?"
- "How do I understand my dashboard statistics?"
- "What are the numbers on my dashboard?"
- "Where can I see my recent activity?"
- "How to access quick actions from dashboard?"

### Admin Dashboard:
- "Guide me through the admin dashboard"
- "What metrics are shown on admin dashboard?"
- "How to use quick action buttons?"
- "What are the system-wide statistics?"
- "How to navigate from admin dashboard?"

---

## ⏰ **CHECK IN/OUT QUESTIONS**

### Basic Check-in/out:
- "How do I check in for work?"
- "How to check out when leaving?"
- "What happens when I click check in?"
- "Can I check in early?"
- "What if I forget to check out?"
- "How does time tracking work?"
- "Is location tracking enabled?"
- "Can I check in from home?"

### Troubleshooting:
- "Check in button is not working"
- "I can't check out"
- "Wrong time recorded for check in"
- "How to fix attendance mistakes?"

---

## 📅 **TIME TABLE QUESTIONS**

### Employee Schedule:
- "How to view my schedule?"
- "Where can I see my weekly timetable?"
- "How to check my work hours?"
- "When does my schedule reset?"
- "Can I modify my schedule?"
- "How to see next week's schedule?"

### Admin Schedule Management:
- "How to create employee schedules?"
- "Guide me through time table management"
- "How to assign work hours to employees?"
- "How to modify existing schedules?"
- "How to set up recurring schedules?"
- "How to handle schedule conflicts?"
- "How to create bulk schedules?"

---

## 📊 **PROGRESS REPORTS QUESTIONS**

### Employee Reports:
- "How to submit a progress report?"
- "What should I include in progress reports?"
- "How to select date range for reports?"
- "Can I edit submitted reports?"
- "How to track report status?"
- "What happens after I submit a report?"

### Admin Report Management:
- "How to review employee progress reports?"
- "Guide me through progress reports section"
- "How to approve or reject reports?"
- "How to add feedback to reports?"
- "How to generate progress analytics?"
- "How to export progress reports?"

---

## 🏖️ **HOLIDAY MANAGEMENT QUESTIONS**

### Employee Holiday Requests:
- "How to request holiday leave?"
- "How to submit vacation request?"
- "What information is needed for leave request?"
- "How to check my leave balance?"
- "How to track holiday request status?"
- "Can I cancel a holiday request?"
- "How to see my holiday history?"

### Admin Holiday Management:
- "How to approve holiday requests?"
- "Guide me through holiday management section"
- "How to reject a leave request?"
- "How to add comments to holiday decisions?"
- "How to check team availability?"
- "How to set company holiday policies?"
- "How to handle conflicting holiday requests?"

---

## ✅ **TASK MANAGEMENT QUESTIONS**

### Employee Tasks:
- "How to view my tasks?"
- "How to update task status?"
- "How to mark a task as completed?"
- "How to add progress notes to tasks?"
- "How to see task deadlines?"
- "How to filter my tasks?"
- "What task statuses are available?"

### Admin Task Management:
- "How to create new tasks?"
- "Guide me through task management section"
- "How to assign tasks to employees?"
- "How to set task priorities?"
- "How to set task deadlines?"
- "How to monitor task progress?"
- "How to assign tasks to multiple employees?"
- "How to generate task reports?"

---

## 💰 **COMPENSATION QUESTIONS**

### Employee Compensation:
- "How to request compensation for overtime?"
- "How to submit compensate hours request?"
- "What information is needed for compensation?"
- "How to track compensation status?"
- "How to request compensation for missed hours?"
- "Can I see my compensation history?"

### Admin Compensation Management:
- "Guide me through the compensation section"
- "How to review compensation requests?"
- "How to approve overtime claims?"
- "How to reject compensation requests?"
- "How to verify compensation calculations?"
- "How to generate compensation reports?"
- "How to handle compensation disputes?"

---

## 👥 **EMPLOYEE MANAGEMENT QUESTIONS (Admin Only)**

### Adding Employees:
- "How to add a new employee?"
- "What information is needed for new employees?"
- "How to set employee roles?"
- "How to assign departments to employees?"
- "How to send login credentials to new employees?"

### Managing Employees:
- "How to edit employee profiles?"
- "How to change employee roles?"
- "How to deactivate an employee account?"
- "How to reset employee passwords?"
- "How to view employee work history?"
- "How to remove an employee from system?"

---

## 📈 **ATTENDANCE RECORDS QUESTIONS (Admin Only)**

### Monitoring Attendance:
- "How to view all employee attendance?"
- "Guide me through attendance records section"
- "How to identify late arrivals?"
- "How to check attendance patterns?"
- "How to generate attendance reports?"
- "How to export attendance data?"
- "How to handle attendance disputes?"

---

## 🔧 **GENERAL SYSTEM QUESTIONS**

### Navigation:
- "How to navigate the system?"
- "Where can I find different sections?"
- "How to use the sidebar menu?"
- "What sections are available to me?"
- "How to access admin functions?"

### Account & Access:
- "How to change my password?"
- "I can't access my account"
- "How to update my profile?"
- "Who can I contact for support?"
- "How to logout safely?"

### Mobile Usage:
- "Can I use this on mobile?"
- "How to access on phone?"
- "Is the system mobile responsive?"

---

## 🎯 **WORKFLOW QUESTIONS**

### Daily Workflows:
- "What should I do when I arrive at work?"
- "How to complete my daily tasks?"
- "What's the process for end of day?"

### Weekly Workflows:
- "How to submit weekly progress?"
- "When do schedules reset?"
- "How to plan my weekly tasks?"

### Monthly Workflows:
- "How to request monthly leave?"
- "How to review monthly progress?"

---

## 🚨 **TROUBLESHOOTING QUESTIONS**

### Common Issues:
- "The system is not loading"
- "I can't see my data"
- "Buttons are not working"
- "How to report a bug?"
- "System is running slowly"

### Error Messages:
- "What does this error mean?"
- "How to fix login errors?"
- "Permission denied error"

---

## 📝 **TEST THESE SPECIFIC ADMIN QUESTIONS**

1. **"Guide me through the compensation section"** - Should provide step-by-step navigation
2. **"How do I approve overtime requests?"** - Should explain the approval process
3. **"What can I do in employee management?"** - Should list all available functions
4. **"How to handle late check-ins?"** - Should explain the process for managing late arrivals
5. **"How to generate reports?"** - Should explain reporting features across different sections

---

## 🎉 **TESTING TIPS**

1. **Ask follow-up questions** to test conversation memory
2. **Use different phrasings** for the same question
3. **Test both employee and admin perspectives**
4. **Ask about specific features** in each section
5. **Test error scenarios** and troubleshooting

The chatbot should provide helpful, specific guidance for all these questions while adapting responses based on the user's role (employee vs admin).

---

**🚀 Start testing with these questions to ensure your chatbot is fully trained on the FSPro EMS system!**
