# 🤖 FSPro EMS Chatbot - Questions Bank

## 📋 **How to Use the Chatbot**

Your FSPro EMS system includes an intelligent AI chatbot that can help you with any questions about the system. Here's how to use it:

### **🔹 Finding the Chatbot**
- Look for the **dark blue chat button** in the bottom-right corner of your screen
- Click the button to open the chat window
- The chatbot is available on all pages once you're logged in

### **🔹 Asking Questions**
- **Type naturally** - Ask questions in your own words
- **Be specific** - The more specific your question, the better the answer
- **Use examples** - You can say "I need to clock in" instead of formal language
- **Ask follow-ups** - The chatbot remembers your conversation

### **🔹 Getting Help**
- Click on **quick suggestion buttons** for common questions
- Use the **minimize/maximize** buttons to control the chat window
- Click **"Clear Chat"** to start a fresh conversation

---

## 🏢 **ABOUT THE SYSTEM**

### **What is FSPro EMS?**
Ask these questions to learn about the system:

- "What is FSPro EMS?"
- "What is EMS and how does it work?"
- "Tell me about this employee management system"
- "What can I do with FSPro EMS?"
- "How does EMS help businesses manage employees?"
- "What features are available in this system?"

### **About FSProgrammers**
Learn about the company behind the system:

- "Who developed FSPro EMS?"
- "Tell me about FSProgrammers"
- "What company created this system?"
- "Who is behind FSPro EMS?"
- "What does FSProgrammers specialize in?"

---

## 👤 **FOR EMPLOYEES**

### **🏠 Dashboard Questions**
- "What information is shown on my dashboard?"
- "How do I understand my dashboard statistics?"
- "What do the numbers on my dashboard mean?"
- "Where can I see my recent activity?"
- "How do I access quick actions from my dashboard?"

### **⏰ Check In/Out Questions**
- "How do I check in for my shift?"
- "How do I check out when leaving work?"
- "What happens when I click check in?"
- "Can I check in before my scheduled time?"
- "What if I forget to check out?"
- "How does the time tracking work?"
- "Can I see my check-in history?"
- "What if the check-in button isn't working?"

### **📅 Schedule & Time Table Questions**
- "How do I view my weekly schedule?"
- "Where can I see my work hours?"
- "When does my schedule reset each week?"
- "Can I modify my own schedule?"
- "How do I check what days I'm working?"
- "Where do I see my shift times?"

### **✅ Task Management Questions**
- "Where can I see my assigned tasks?"
- "How do I update the status of my task?"
- "How do I mark a task as completed?"
- "How do I add progress notes to my tasks?"
- "Where can I see task deadlines?"
- "How do I filter my tasks by status?"
- "What task statuses are available?"

### **📊 Progress Reports Questions**
- "How do I submit a progress report?"
- "What should I include in my progress report?"
- "How do I select the date range for my report?"
- "Can I edit a progress report after submitting?"
- "How do I track the status of my submitted reports?"
- "What happens after I submit a progress report?"

### **🏖️ Holiday & Leave Questions**
- "How do I request vacation days?"
- "How do I submit a holiday request?"
- "What information do I need for a leave request?"
- "How do I check if my leave request was approved?"
- "Can I cancel a holiday request?"
- "Where can I see my holiday request history?"
- "How do I request sick leave?"

### **💰 Compensation Questions**
- "How do I request compensation for overtime?"
- "How do I submit a request for extra hours worked?"
- "What information is needed for compensation requests?"
- "How do I track my compensation request status?"
- "Can I see my compensation history?"
- "How do I request compensation for missed breaks?"

---

## 👨‍💼 **FOR ADMINISTRATORS**

### **👥 Employee Management Questions**
- "How do I add a new employee to the system?"
- "What information is needed to create an employee account?"
- "How do I edit an existing employee's profile?"
- "How do I change an employee's role or permissions?"
- "How do I deactivate an employee account?"
- "How do I reset an employee's password?"
- "How do I view an employee's work history?"

### **✅ Task Management Questions**
- "How do I create and assign tasks to employees?"
- "How do I set task priorities and deadlines?"
- "How do I assign the same task to multiple employees?"
- "How do I monitor task progress across my team?"
- "How do I update task details or requirements?"
- "How do I generate task completion reports?"

### **📅 Schedule Management Questions**
- "How do I create weekly schedules for employees?"
- "How do I modify an existing employee schedule?"
- "How do I assign work hours to specific employees?"
- "How do I handle schedule conflicts?"
- "How do I set up recurring weekly schedules?"
- "How do I create schedules for multiple employees at once?"

### **🏖️ Holiday Management Questions**
- "How do I approve or reject holiday requests?"
- "How do I review pending leave requests?"
- "How do I add comments to holiday request decisions?"
- "How do I check team availability before approving leave?"
- "How do I set company holiday policies?"
- "How do I handle conflicting holiday requests?"

### **📊 Progress Reports Management**
- "How do I review employee progress reports?"
- "How do I approve or request revisions for reports?"
- "How do I add feedback to employee reports?"
- "How do I track report submission trends?"
- "How do I generate progress analytics?"
- "How do I export progress reports?"

### **📈 Attendance Management Questions**
- "How do I view all employee attendance records?"
- "How do I identify employees with late arrivals?"
- "How do I generate attendance reports for payroll?"
- "How do I handle attendance disputes?"
- "How do I export attendance data?"
- "How do I monitor attendance patterns?"

### **💰 Compensation Management Questions**
- "Guide me through the compensation management section"
- "How do I review employee compensation requests?"
- "How do I approve or reject overtime claims?"
- "How do I verify compensation calculations?"
- "How do I handle compensation disputes?"
- "How do I generate compensation reports for payroll?"

---

## 🔧 **GENERAL SYSTEM QUESTIONS**

### **Navigation & Access**
- "How do I navigate between different sections?"
- "Where can I find the employee management section?"
- "How do I access admin functions?"
- "What sections are available to me?"
- "How do I use the sidebar menu?"

### **Account & Profile**
- "How do I change my password?"
- "How do I update my profile information?"
- "I can't access my account, what should I do?"
- "How do I logout safely?"
- "Who can I contact for technical support?"

### **Mobile & Accessibility**
- "Can I use this system on my mobile phone?"
- "Is the system mobile responsive?"
- "How do I access FSPro EMS on my tablet?"

---

## 🚨 **TROUBLESHOOTING QUESTIONS**

### **Common Issues**
- "The system is loading slowly, what should I do?"
- "I can't see my data, what's wrong?"
- "The buttons are not responding, how do I fix this?"
- "How do I report a system bug?"
- "I'm getting an error message, what does it mean?"

### **Specific Problems**
- "The check-in button is not working"
- "I can't submit my progress report"
- "My schedule is not showing up"
- "I can't see my tasks"
- "The holiday request form won't submit"

---

## 💡 **TIPS FOR BETTER CHATBOT INTERACTION**

### **✅ Do This:**
- Ask specific questions: "How do I check in?" instead of "Help me"
- Use natural language: "I need to request time off"
- Be clear about your role: Mention if you're an admin or employee
- Ask follow-up questions if you need more details

### **❌ Avoid This:**
- Very vague questions: "What do I do?"
- Multiple questions at once
- Technical jargon the system might not understand
- Questions about other software systems

---

## 🎯 **QUICK REFERENCE**

### **Most Common Employee Questions:**
1. "How do I check in for work?"
2. "How do I submit a progress report?"
3. "How do I request vacation days?"
4. "Where can I see my tasks?"
5. "How do I view my schedule?"

### **Most Common Admin Questions:**
1. "How do I add a new employee?"
2. "How do I approve holiday requests?"
3. "Guide me through the compensation section"
4. "How do I create tasks for employees?"
5. "How do I generate attendance reports?"

---

---

## 📝 **SAMPLE CONVERSATIONS**

### **Example 1: Employee Checking In**
**You:** "I need to clock in"
**Chatbot:** "I can help you clock in! Go to the 'Check In/Out' section from the sidebar menu. Once there, you'll see a 'Check In' button - just click that and your attendance will be recorded with the current time. Is there anything specific about the check-in process you'd like to know?"

### **Example 2: Admin Adding Employee**
**You:** "How do I add a new team member?"
**Chatbot:** "To add a new employee: Go to 'Employee Management' section, click 'Add Employee', fill in their details including name, email, role, and department, then save. They'll receive login credentials via email. Would you like me to explain any specific part of this process?"

### **Example 3: Requesting Time Off**
**You:** "I want to request vacation next week"
**Chatbot:** "To request time off: Go to 'Holiday Requests' section, click 'New Request', select your start and end dates, add a reason for your leave, and submit the request. Your administrator will review it and either approve or reject it. You can check the status in the same section. How many days are you planning to take?"

---

## 🎉 **GET STARTED**

1. **Look for the dark blue chat button** in the bottom-right corner
2. **Click to open** the chat window
3. **Try asking:** "What is FSPro EMS?" to get started
4. **Use the quick suggestions** or ask your own questions
5. **Be specific** for the best answers

**🚀 Start chatting with your FSPro EMS assistant today! The chatbot is ready to help you with any questions about the system.**
