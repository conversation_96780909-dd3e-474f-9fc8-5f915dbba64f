import{r as x,a as As,R as Yt}from"./vendor-nf7bT_Uh.js";import{r as Re,_ as ze,C as Pe,a as Be,E as Kt,o as Ds,F as mt,L as Os,g as xt,i as Ms,b as Ls,v as _s,c as vt,d as Gt,e as zs,f as Ps,S as Bs,h as Fs,j as Hs,k as Xt,l as Us,m as Jt,n as qe,s as Ws,p as Le,q as Oe,t as nt,w as St,u as $s,x as qs,y as Vs,z as at,A as Ys,B as Ks,D as Gs,G as Xs,H as Js,I as Qs,J as Ct}from"./firebase-BfSkx5kM.js";import{V as R,f as W,s as ct,e as Qt,E as Zt,a as fe,L as Zs,b as en,B as It,C as tn,I as Tt,c as V,X as De,d as te,A as ae,M as sn,U as gt,g as nn,h as ue,i as se,F as he,j as ke,H as an,k as on,l as re,m as Ae,n as rn,o as ln,p as cn,P as ge,q as xe,r as dn,D as ft,t as un,u as dt,T as hn,S as Qe,v as pn,w as mn,x as xn,y as gn,z as es,G as He,J as Ue,K as We,N as fn,O as yn,Q as jn,R as bn}from"./utils-B8y0nSP-.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))a(o);new MutationObserver(o=>{for(const i of o)if(i.type==="childList")for(const r of i.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&a(r)}).observe(document,{childList:!0,subtree:!0});function n(o){const i={};return o.integrity&&(i.integrity=o.integrity),o.referrerPolicy&&(i.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?i.credentials="include":o.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function a(o){if(o.ep)return;o.ep=!0;const i=n(o);fetch(o.href,i)}})();var ts={exports:{}},Ze={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kn=x,wn=Symbol.for("react.element"),vn=Symbol.for("react.fragment"),Sn=Object.prototype.hasOwnProperty,Cn=kn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,In={key:!0,ref:!0,__self:!0,__source:!0};function ss(t,s,n){var a,o={},i=null,r=null;n!==void 0&&(i=""+n),s.key!==void 0&&(i=""+s.key),s.ref!==void 0&&(r=s.ref);for(a in s)Sn.call(s,a)&&!In.hasOwnProperty(a)&&(o[a]=s[a]);if(t&&t.defaultProps)for(a in s=t.defaultProps,s)o[a]===void 0&&(o[a]=s[a]);return{$$typeof:wn,type:t,key:i,ref:r,props:o,_owner:Cn.current}}Ze.Fragment=vn;Ze.jsx=ss;Ze.jsxs=ss;ts.exports=Ze;var e=ts.exports,ut={},Et=As;ut.createRoot=Et.createRoot,ut.hydrateRoot=Et.hydrateRoot;const ns="@firebase/installations",yt="0.6.9";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const as=1e4,os=`w:${yt}`,is="FIS_v2",Tn="https://firebaseinstallations.googleapis.com/v1",En=60*60*1e3,Nn="installations",Rn="Installations";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const An={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."},we=new Kt(Nn,Rn,An);function rs(t){return t instanceof mt&&t.code.includes("request-failed")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ls({projectId:t}){return`${Tn}/projects/${t}/installations`}function cs(t){return{token:t.token,requestStatus:2,expiresIn:On(t.expiresIn),creationTime:Date.now()}}async function ds(t,s){const a=(await s.json()).error;return we.create("request-failed",{requestName:t,serverCode:a.code,serverMessage:a.message,serverStatus:a.status})}function us({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function Dn(t,{refreshToken:s}){const n=us(t);return n.append("Authorization",Mn(s)),n}async function hs(t){const s=await t();return s.status>=500&&s.status<600?t():s}function On(t){return Number(t.replace("s","000"))}function Mn(t){return`${is} ${t}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Ln({appConfig:t,heartbeatServiceProvider:s},{fid:n}){const a=ls(t),o=us(t),i=s.getImmediate({optional:!0});if(i){const d=await i.getHeartbeatsHeader();d&&o.append("x-firebase-client",d)}const r={fid:n,authVersion:is,appId:t.appId,sdkVersion:os},l={method:"POST",headers:o,body:JSON.stringify(r)},u=await hs(()=>fetch(a,l));if(u.ok){const d=await u.json();return{fid:d.fid||n,registrationStatus:2,refreshToken:d.refreshToken,authToken:cs(d.authToken)}}else throw await ds("Create Installation",u)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ps(t){return new Promise(s=>{setTimeout(s,t)})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function _n(t){return btoa(String.fromCharCode(...t)).replace(/\+/g,"-").replace(/\//g,"_")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const zn=/^[cdef][\w-]{21}$/,ht="";function Pn(){try{const t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;const n=Bn(t);return zn.test(n)?n:ht}catch{return ht}}function Bn(t){return _n(t).substr(0,22)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function et(t){return`${t.appName}!${t.appId}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ms=new Map;function xs(t,s){const n=et(t);gs(n,s),Fn(n,s)}function gs(t,s){const n=ms.get(t);if(n)for(const a of n)a(s)}function Fn(t,s){const n=Hn();n&&n.postMessage({key:t,fid:s}),Un()}let ye=null;function Hn(){return!ye&&"BroadcastChannel"in self&&(ye=new BroadcastChannel("[Firebase] FID Change"),ye.onmessage=t=>{gs(t.data.key,t.data.fid)}),ye}function Un(){ms.size===0&&ye&&(ye.close(),ye=null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Wn="firebase-installations-database",$n=1,ve="firebase-installations-store";let ot=null;function jt(){return ot||(ot=Ds(Wn,$n,{upgrade:(t,s)=>{switch(s){case 0:t.createObjectStore(ve)}}})),ot}async function Ke(t,s){const n=et(t),o=(await jt()).transaction(ve,"readwrite"),i=o.objectStore(ve),r=await i.get(n);return await i.put(s,n),await o.done,(!r||r.fid!==s.fid)&&xs(t,s.fid),s}async function fs(t){const s=et(t),a=(await jt()).transaction(ve,"readwrite");await a.objectStore(ve).delete(s),await a.done}async function tt(t,s){const n=et(t),o=(await jt()).transaction(ve,"readwrite"),i=o.objectStore(ve),r=await i.get(n),l=s(r);return l===void 0?await i.delete(n):await i.put(l,n),await o.done,l&&(!r||r.fid!==l.fid)&&xs(t,l.fid),l}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function bt(t){let s;const n=await tt(t.appConfig,a=>{const o=qn(a),i=Vn(t,o);return s=i.registrationPromise,i.installationEntry});return n.fid===ht?{installationEntry:await s}:{installationEntry:n,registrationPromise:s}}function qn(t){const s=t||{fid:Pn(),registrationStatus:0};return ys(s)}function Vn(t,s){if(s.registrationStatus===0){if(!navigator.onLine){const o=Promise.reject(we.create("app-offline"));return{installationEntry:s,registrationPromise:o}}const n={fid:s.fid,registrationStatus:1,registrationTime:Date.now()},a=Yn(t,n);return{installationEntry:n,registrationPromise:a}}else return s.registrationStatus===1?{installationEntry:s,registrationPromise:Kn(t)}:{installationEntry:s}}async function Yn(t,s){try{const n=await Ln(t,s);return Ke(t.appConfig,n)}catch(n){throw rs(n)&&n.customData.serverCode===409?await fs(t.appConfig):await Ke(t.appConfig,{fid:s.fid,registrationStatus:0}),n}}async function Kn(t){let s=await Nt(t.appConfig);for(;s.registrationStatus===1;)await ps(100),s=await Nt(t.appConfig);if(s.registrationStatus===0){const{installationEntry:n,registrationPromise:a}=await bt(t);return a||n}return s}function Nt(t){return tt(t,s=>{if(!s)throw we.create("installation-not-found");return ys(s)})}function ys(t){return Gn(t)?{fid:t.fid,registrationStatus:0}:t}function Gn(t){return t.registrationStatus===1&&t.registrationTime+as<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Xn({appConfig:t,heartbeatServiceProvider:s},n){const a=Jn(t,n),o=Dn(t,n),i=s.getImmediate({optional:!0});if(i){const d=await i.getHeartbeatsHeader();d&&o.append("x-firebase-client",d)}const r={installation:{sdkVersion:os,appId:t.appId}},l={method:"POST",headers:o,body:JSON.stringify(r)},u=await hs(()=>fetch(a,l));if(u.ok){const d=await u.json();return cs(d)}else throw await ds("Generate Auth Token",u)}function Jn(t,{fid:s}){return`${ls(t)}/${s}/authTokens:generate`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function kt(t,s=!1){let n;const a=await tt(t.appConfig,i=>{if(!js(i))throw we.create("not-registered");const r=i.authToken;if(!s&&ea(r))return i;if(r.requestStatus===1)return n=Qn(t,s),i;{if(!navigator.onLine)throw we.create("app-offline");const l=sa(i);return n=Zn(t,l),l}});return n?await n:a.authToken}async function Qn(t,s){let n=await Rt(t.appConfig);for(;n.authToken.requestStatus===1;)await ps(100),n=await Rt(t.appConfig);const a=n.authToken;return a.requestStatus===0?kt(t,s):a}function Rt(t){return tt(t,s=>{if(!js(s))throw we.create("not-registered");const n=s.authToken;return na(n)?Object.assign(Object.assign({},s),{authToken:{requestStatus:0}}):s})}async function Zn(t,s){try{const n=await Xn(t,s),a=Object.assign(Object.assign({},s),{authToken:n});return await Ke(t.appConfig,a),n}catch(n){if(rs(n)&&(n.customData.serverCode===401||n.customData.serverCode===404))await fs(t.appConfig);else{const a=Object.assign(Object.assign({},s),{authToken:{requestStatus:0}});await Ke(t.appConfig,a)}throw n}}function js(t){return t!==void 0&&t.registrationStatus===2}function ea(t){return t.requestStatus===2&&!ta(t)}function ta(t){const s=Date.now();return s<t.creationTime||t.creationTime+t.expiresIn<s+En}function sa(t){const s={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:s})}function na(t){return t.requestStatus===1&&t.requestTime+as<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function aa(t){const s=t,{installationEntry:n,registrationPromise:a}=await bt(s);return a?a.catch(console.error):kt(s).catch(console.error),n.fid}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function oa(t,s=!1){const n=t;return await ia(n),(await kt(n,s)).token}async function ia(t){const{registrationPromise:s}=await bt(t);s&&await s}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ra(t){if(!t||!t.options)throw it("App Configuration");if(!t.name)throw it("App Name");const s=["projectId","apiKey","appId"];for(const n of s)if(!t.options[n])throw it(n);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}function it(t){return we.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const bs="installations",la="installations-internal",ca=t=>{const s=t.getProvider("app").getImmediate(),n=ra(s),a=Be(s,"heartbeat");return{app:s,appConfig:n,heartbeatServiceProvider:a,_delete:()=>Promise.resolve()}},da=t=>{const s=t.getProvider("app").getImmediate(),n=Be(s,bs).getImmediate();return{getId:()=>aa(n),getToken:o=>oa(n,o)}};function ua(){ze(new Pe(bs,ca,"PUBLIC")),ze(new Pe(la,da,"PRIVATE"))}ua();Re(ns,yt);Re(ns,yt,"esm2017");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ge="analytics",ha="firebase_id",pa="origin",ma=60*1e3,xa="https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig",wt="https://www.googletagmanager.com/gtag/js";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const oe=new Os("@firebase/analytics");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ga={"already-exists":"A Firebase Analytics instance with the appId {$id}  already exists. Only one Firebase Analytics instance can be created for each appId.","already-initialized":"initializeAnalytics() cannot be called again with different options than those it was initially called with. It can be called again with the same options to return the existing instance, or getAnalytics() can be used to get a reference to the already-initialized instance.","already-initialized-settings":"Firebase Analytics has already been initialized.settings() must be called before initializing any Analytics instanceor it will have no effect.","interop-component-reg-failed":"Firebase Analytics Interop Component failed to instantiate: {$reason}","invalid-analytics-context":"Firebase Analytics is not supported in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","indexeddb-unavailable":"IndexedDB unavailable or restricted in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","fetch-throttle":"The config fetch request timed out while in an exponential backoff state. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.","config-fetch-failed":"Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}","no-api-key":'The "apiKey" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid API key.',"no-app-id":'The "appId" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid app ID.',"no-client-id":'The "client_id" field is empty.',"invalid-gtag-resource":"Trusted Types detected an invalid gtag resource: {$gtagURL}."},le=new Kt("analytics","Analytics",ga);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function fa(t){if(!t.startsWith(wt)){const s=le.create("invalid-gtag-resource",{gtagURL:t});return oe.warn(s.message),""}return t}function ks(t){return Promise.all(t.map(s=>s.catch(n=>n)))}function ya(t,s){let n;return window.trustedTypes&&(n=window.trustedTypes.createPolicy(t,s)),n}function ja(t,s){const n=ya("firebase-js-sdk-policy",{createScriptURL:fa}),a=document.createElement("script"),o=`${wt}?l=${t}&id=${s}`;a.src=n?n==null?void 0:n.createScriptURL(o):o,a.async=!0,document.head.appendChild(a)}function ba(t){let s=[];return Array.isArray(window[t])?s=window[t]:window[t]=s,s}async function ka(t,s,n,a,o,i){const r=a[o];try{if(r)await s[r];else{const u=(await ks(n)).find(d=>d.measurementId===o);u&&await s[u.appId]}}catch(l){oe.error(l)}t("config",o,i)}async function wa(t,s,n,a,o){try{let i=[];if(o&&o.send_to){let r=o.send_to;Array.isArray(r)||(r=[r]);const l=await ks(n);for(const u of r){const d=l.find(y=>y.measurementId===u),j=d&&s[d.appId];if(j)i.push(j);else{i=[];break}}}i.length===0&&(i=Object.values(s)),await Promise.all(i),t("event",a,o||{})}catch(i){oe.error(i)}}function va(t,s,n,a){async function o(i,...r){try{if(i==="event"){const[l,u]=r;await wa(t,s,n,l,u)}else if(i==="config"){const[l,u]=r;await ka(t,s,n,a,l,u)}else if(i==="consent"){const[l,u]=r;t("consent",l,u)}else if(i==="get"){const[l,u,d]=r;t("get",l,u,d)}else if(i==="set"){const[l]=r;t("set",l)}else t(i,...r)}catch(l){oe.error(l)}}return o}function Sa(t,s,n,a,o){let i=function(...r){window[a].push(arguments)};return window[o]&&typeof window[o]=="function"&&(i=window[o]),window[o]=va(i,t,s,n),{gtagCore:i,wrappedGtag:window[o]}}function Ca(t){const s=window.document.getElementsByTagName("script");for(const n of Object.values(s))if(n.src&&n.src.includes(wt)&&n.src.includes(t))return n;return null}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ia=30,Ta=1e3;class Ea{constructor(s={},n=Ta){this.throttleMetadata=s,this.intervalMillis=n}getThrottleMetadata(s){return this.throttleMetadata[s]}setThrottleMetadata(s,n){this.throttleMetadata[s]=n}deleteThrottleMetadata(s){delete this.throttleMetadata[s]}}const ws=new Ea;function Na(t){return new Headers({Accept:"application/json","x-goog-api-key":t})}async function Ra(t){var s;const{appId:n,apiKey:a}=t,o={method:"GET",headers:Na(a)},i=xa.replace("{app-id}",n),r=await fetch(i,o);if(r.status!==200&&r.status!==304){let l="";try{const u=await r.json();!((s=u.error)===null||s===void 0)&&s.message&&(l=u.error.message)}catch{}throw le.create("config-fetch-failed",{httpStatus:r.status,responseMessage:l})}return r.json()}async function Aa(t,s=ws,n){const{appId:a,apiKey:o,measurementId:i}=t.options;if(!a)throw le.create("no-app-id");if(!o){if(i)return{measurementId:i,appId:a};throw le.create("no-api-key")}const r=s.getThrottleMetadata(a)||{backoffCount:0,throttleEndTimeMillis:Date.now()},l=new Ma;return setTimeout(async()=>{l.abort()},ma),vs({appId:a,apiKey:o,measurementId:i},r,l,s)}async function vs(t,{throttleEndTimeMillis:s,backoffCount:n},a,o=ws){var i;const{appId:r,measurementId:l}=t;try{await Da(a,s)}catch(u){if(l)return oe.warn(`Timed out fetching this Firebase app's measurement ID from the server. Falling back to the measurement ID ${l} provided in the "measurementId" field in the local Firebase config. [${u==null?void 0:u.message}]`),{appId:r,measurementId:l};throw u}try{const u=await Ra(t);return o.deleteThrottleMetadata(r),u}catch(u){const d=u;if(!Oa(d)){if(o.deleteThrottleMetadata(r),l)return oe.warn(`Failed to fetch this Firebase app's measurement ID from the server. Falling back to the measurement ID ${l} provided in the "measurementId" field in the local Firebase config. [${d==null?void 0:d.message}]`),{appId:r,measurementId:l};throw u}const j=Number((i=d==null?void 0:d.customData)===null||i===void 0?void 0:i.httpStatus)===503?vt(n,o.intervalMillis,Ia):vt(n,o.intervalMillis),y={throttleEndTimeMillis:Date.now()+j,backoffCount:n+1};return o.setThrottleMetadata(r,y),oe.debug(`Calling attemptFetch again in ${j} millis`),vs(t,y,a,o)}}function Da(t,s){return new Promise((n,a)=>{const o=Math.max(s-Date.now(),0),i=setTimeout(n,o);t.addEventListener(()=>{clearTimeout(i),a(le.create("fetch-throttle",{throttleEndTimeMillis:s}))})})}function Oa(t){if(!(t instanceof mt)||!t.customData)return!1;const s=Number(t.customData.httpStatus);return s===429||s===500||s===503||s===504}class Ma{constructor(){this.listeners=[]}addEventListener(s){this.listeners.push(s)}abort(){this.listeners.forEach(s=>s())}}async function La(t,s,n,a,o){if(o&&o.global){t("event",n,a);return}else{const i=await s,r=Object.assign(Object.assign({},a),{send_to:i});t("event",n,r)}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function _a(){if(Ls())try{await _s()}catch(t){return oe.warn(le.create("indexeddb-unavailable",{errorInfo:t==null?void 0:t.toString()}).message),!1}else return oe.warn(le.create("indexeddb-unavailable",{errorInfo:"IndexedDB is not available in this environment."}).message),!1;return!0}async function za(t,s,n,a,o,i,r){var l;const u=Aa(t);u.then(p=>{n[p.measurementId]=p.appId,t.options.measurementId&&p.measurementId!==t.options.measurementId&&oe.warn(`The measurement ID in the local Firebase config (${t.options.measurementId}) does not match the measurement ID fetched from the server (${p.measurementId}). To ensure analytics events are always sent to the correct Analytics property, update the measurement ID field in the local config or remove it from the local config.`)}).catch(p=>oe.error(p)),s.push(u);const d=_a().then(p=>{if(p)return a.getId()}),[j,y]=await Promise.all([u,d]);Ca(i)||ja(i,j.measurementId),o("js",new Date);const b=(l=r==null?void 0:r.config)!==null&&l!==void 0?l:{};return b[pa]="firebase",b.update=!0,y!=null&&(b[ha]=y),o("config",j.measurementId,b),j.measurementId}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Pa{constructor(s){this.app=s}_delete(){return delete _e[this.app.options.appId],Promise.resolve()}}let _e={},At=[];const Dt={};let rt="dataLayer",Ba="gtag",Ot,Ss,Mt=!1;function Fa(){const t=[];if(Ms()&&t.push("This is a browser extension environment."),Ps()||t.push("Cookies are not available."),t.length>0){const s=t.map((a,o)=>`(${o+1}) ${a}`).join(" "),n=le.create("invalid-analytics-context",{errorInfo:s});oe.warn(n.message)}}function Ha(t,s,n){Fa();const a=t.options.appId;if(!a)throw le.create("no-app-id");if(!t.options.apiKey)if(t.options.measurementId)oe.warn(`The "apiKey" field is empty in the local Firebase config. This is needed to fetch the latest measurement ID for this Firebase app. Falling back to the measurement ID ${t.options.measurementId} provided in the "measurementId" field in the local Firebase config.`);else throw le.create("no-api-key");if(_e[a]!=null)throw le.create("already-exists",{id:a});if(!Mt){ba(rt);const{wrappedGtag:i,gtagCore:r}=Sa(_e,At,Dt,rt,Ba);Ss=i,Ot=r,Mt=!0}return _e[a]=za(t,At,Dt,s,Ot,rt,n),new Pa(t)}function Ua(t=Gt()){t=xt(t);const s=Be(t,Ge);return s.isInitialized()?s.getImmediate():Wa(t)}function Wa(t,s={}){const n=Be(t,Ge);if(n.isInitialized()){const o=n.getImmediate();if(zs(s,n.getOptions()))return o;throw le.create("already-initialized")}return n.initialize({options:s})}function $a(t,s,n,a){t=xt(t),La(Ss,_e[t.app.options.appId],s,n,a).catch(o=>oe.error(o))}const Lt="@firebase/analytics",_t="0.10.8";function qa(){ze(new Pe(Ge,(s,{options:n})=>{const a=s.getProvider("app").getImmediate(),o=s.getProvider("installations-internal").getImmediate();return Ha(a,o,n)},"PUBLIC")),ze(new Pe("analytics-internal",t,"PRIVATE")),Re(Lt,_t),Re(Lt,_t,"esm2017");function t(s){try{const n=s.getProvider(Ge).getImmediate();return{logEvent:(a,o,i)=>$a(n,a,o,i)}}catch(n){throw le.create("interop-component-reg-failed",{reason:n})}}}qa();/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Cs="firebasestorage.googleapis.com",Va="storageBucket",Ya=2*60*1e3,Ka=10*60*1e3;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class me extends mt{constructor(s,n,a=0){super(lt(s),`Firebase Storage: ${n} (${lt(s)})`),this.status_=a,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,me.prototype)}get status(){return this.status_}set status(s){this.status_=s}_codeEquals(s){return lt(s)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(s){this.customData.serverResponse=s,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}}var pe;(function(t){t.UNKNOWN="unknown",t.OBJECT_NOT_FOUND="object-not-found",t.BUCKET_NOT_FOUND="bucket-not-found",t.PROJECT_NOT_FOUND="project-not-found",t.QUOTA_EXCEEDED="quota-exceeded",t.UNAUTHENTICATED="unauthenticated",t.UNAUTHORIZED="unauthorized",t.UNAUTHORIZED_APP="unauthorized-app",t.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",t.INVALID_CHECKSUM="invalid-checksum",t.CANCELED="canceled",t.INVALID_EVENT_NAME="invalid-event-name",t.INVALID_URL="invalid-url",t.INVALID_DEFAULT_BUCKET="invalid-default-bucket",t.NO_DEFAULT_BUCKET="no-default-bucket",t.CANNOT_SLICE_BLOB="cannot-slice-blob",t.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",t.NO_DOWNLOAD_URL="no-download-url",t.INVALID_ARGUMENT="invalid-argument",t.INVALID_ARGUMENT_COUNT="invalid-argument-count",t.APP_DELETED="app-deleted",t.INVALID_ROOT_OPERATION="invalid-root-operation",t.INVALID_FORMAT="invalid-format",t.INTERNAL_ERROR="internal-error",t.UNSUPPORTED_ENVIRONMENT="unsupported-environment"})(pe||(pe={}));function lt(t){return"storage/"+t}function Ga(){const t="An unknown error occurred, please check the error payload for server response.";return new me(pe.UNKNOWN,t)}function Xa(){return new me(pe.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function Ja(){return new me(pe.CANCELED,"User canceled the upload/download.")}function Qa(t){return new me(pe.INVALID_URL,"Invalid URL '"+t+"'.")}function Za(t){return new me(pe.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+t+"'.")}function zt(t){return new me(pe.INVALID_ARGUMENT,t)}function Is(){return new me(pe.APP_DELETED,"The Firebase app was deleted.")}function eo(t){return new me(pe.INVALID_ROOT_OPERATION,"The operation '"+t+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ce{constructor(s,n){this.bucket=s,this.path_=n}get path(){return this.path_}get isRoot(){return this.path.length===0}fullServerUrl(){const s=encodeURIComponent;return"/b/"+s(this.bucket)+"/o/"+s(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(s,n){let a;try{a=ce.makeFromUrl(s,n)}catch{return new ce(s,"")}if(a.path==="")return a;throw Za(s)}static makeFromUrl(s,n){let a=null;const o="([A-Za-z0-9.\\-_]+)";function i(m){m.path.charAt(m.path.length-1)==="/"&&(m.path_=m.path_.slice(0,-1))}const r="(/(.*))?$",l=new RegExp("^gs://"+o+r,"i"),u={bucket:1,path:3};function d(m){m.path_=decodeURIComponent(m.path)}const j="v[A-Za-z0-9_]+",y=n.replace(/[.]/g,"\\."),b="(/([^?#]*).*)?$",p=new RegExp(`^https?://${y}/${j}/b/${o}/o${b}`,"i"),w={bucket:1,path:3},v=n===Cs?"(?:storage.googleapis.com|storage.cloud.google.com)":n,g="([^?#]*)",f=new RegExp(`^https?://${v}/${o}/${g}`,"i"),D=[{regex:l,indices:u,postModify:i},{regex:p,indices:w,postModify:d},{regex:f,indices:{bucket:1,path:2},postModify:d}];for(let m=0;m<D.length;m++){const N=D[m],k=N.regex.exec(s);if(k){const O=k[N.indices.bucket];let S=k[N.indices.path];S||(S=""),a=new ce(O,S),N.postModify(a);break}}if(a==null)throw Qa(s);return a}}class to{constructor(s){this.promise_=Promise.reject(s)}getPromise(){return this.promise_}cancel(s=!1){}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function so(t,s,n){let a=1,o=null,i=null,r=!1,l=0;function u(){return l===2}let d=!1;function j(...g){d||(d=!0,s.apply(null,g))}function y(g){o=setTimeout(()=>{o=null,t(p,u())},g)}function b(){i&&clearTimeout(i)}function p(g,...f){if(d){b();return}if(g){b(),j.call(null,g,...f);return}if(u()||r){b(),j.call(null,g,...f);return}a<64&&(a*=2);let D;l===1?(l=2,D=0):D=(a+Math.random())*1e3,y(D)}let w=!1;function v(g){w||(w=!0,b(),!d&&(o!==null?(g||(l=2),clearTimeout(o),y(0)):g||(l=1)))}return y(0),i=setTimeout(()=>{r=!0,v(!0)},n),v}function no(t){t(!1)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ao(t){return t!==void 0}function Pt(t,s,n,a){if(a<s)throw zt(`Invalid value for '${t}'. Expected ${s} or greater.`);if(a>n)throw zt(`Invalid value for '${t}'. Expected ${n} or less.`)}function oo(t){const s=encodeURIComponent;let n="?";for(const a in t)if(t.hasOwnProperty(a)){const o=s(a)+"="+s(t[a]);n=n+o+"&"}return n=n.slice(0,-1),n}var Xe;(function(t){t[t.NO_ERROR=0]="NO_ERROR",t[t.NETWORK_ERROR=1]="NETWORK_ERROR",t[t.ABORT=2]="ABORT"})(Xe||(Xe={}));/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function io(t,s){const n=t>=500&&t<600,o=[408,429].indexOf(t)!==-1,i=s.indexOf(t)!==-1;return n||o||i}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ro{constructor(s,n,a,o,i,r,l,u,d,j,y,b=!0){this.url_=s,this.method_=n,this.headers_=a,this.body_=o,this.successCodes_=i,this.additionalRetryCodes_=r,this.callback_=l,this.errorCallback_=u,this.timeout_=d,this.progressCallback_=j,this.connectionFactory_=y,this.retry=b,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((p,w)=>{this.resolve_=p,this.reject_=w,this.start_()})}start_(){const s=(a,o)=>{if(o){a(!1,new $e(!1,null,!0));return}const i=this.connectionFactory_();this.pendingConnection_=i;const r=l=>{const u=l.loaded,d=l.lengthComputable?l.total:-1;this.progressCallback_!==null&&this.progressCallback_(u,d)};this.progressCallback_!==null&&i.addUploadProgressListener(r),i.send(this.url_,this.method_,this.body_,this.headers_).then(()=>{this.progressCallback_!==null&&i.removeUploadProgressListener(r),this.pendingConnection_=null;const l=i.getErrorCode()===Xe.NO_ERROR,u=i.getStatus();if(!l||io(u,this.additionalRetryCodes_)&&this.retry){const j=i.getErrorCode()===Xe.ABORT;a(!1,new $e(!1,null,j));return}const d=this.successCodes_.indexOf(u)!==-1;a(!0,new $e(d,i))})},n=(a,o)=>{const i=this.resolve_,r=this.reject_,l=o.connection;if(o.wasSuccessCode)try{const u=this.callback_(l,l.getResponse());ao(u)?i(u):i()}catch(u){r(u)}else if(l!==null){const u=Ga();u.serverResponse=l.getErrorText(),this.errorCallback_?r(this.errorCallback_(l,u)):r(u)}else if(o.canceled){const u=this.appDelete_?Is():Ja();r(u)}else{const u=Xa();r(u)}};this.canceled_?n(!1,new $e(!1,null,!0)):this.backoffId_=so(s,n,this.timeout_)}getPromise(){return this.promise_}cancel(s){this.canceled_=!0,this.appDelete_=s||!1,this.backoffId_!==null&&no(this.backoffId_),this.pendingConnection_!==null&&this.pendingConnection_.abort()}}class $e{constructor(s,n,a){this.wasSuccessCode=s,this.connection=n,this.canceled=!!a}}function lo(t,s){s!==null&&s.length>0&&(t.Authorization="Firebase "+s)}function co(t,s){t["X-Firebase-Storage-Version"]="webjs/"+(s??"AppManager")}function uo(t,s){s&&(t["X-Firebase-GMPID"]=s)}function ho(t,s){s!==null&&(t["X-Firebase-AppCheck"]=s)}function po(t,s,n,a,o,i,r=!0){const l=oo(t.urlParams),u=t.url+l,d=Object.assign({},t.headers);return uo(d,s),lo(d,n),co(d,i),ho(d,a),new ro(u,t.method,d,t.body,t.successCodes,t.additionalRetryCodes,t.handler,t.errorHandler,t.timeout,t.progressCallback,o,r)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function mo(t){if(t.length===0)return null;const s=t.lastIndexOf("/");return s===-1?"":t.slice(0,s)}function xo(t){const s=t.lastIndexOf("/",t.length-2);return s===-1?t:t.slice(s+1)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Je{constructor(s,n){this._service=s,n instanceof ce?this._location=n:this._location=ce.makeFromUrl(n,s.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(s,n){return new Je(s,n)}get root(){const s=new ce(this._location.bucket,"");return this._newRef(this._service,s)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return xo(this._location.path)}get storage(){return this._service}get parent(){const s=mo(this._location.path);if(s===null)return null;const n=new ce(this._location.bucket,s);return new Je(this._service,n)}_throwIfRoot(s){if(this._location.path==="")throw eo(s)}}function Bt(t,s){const n=s==null?void 0:s[Va];return n==null?null:ce.makeFromBucketSpec(n,t)}function go(t,s,n,a={}){t.host=`${s}:${n}`,t._protocol="http";const{mockUserToken:o}=a;o&&(t._overrideAuthToken=typeof o=="string"?o:Hs(o,t.app.options.projectId))}class fo{constructor(s,n,a,o,i){this.app=s,this._authProvider=n,this._appCheckProvider=a,this._url=o,this._firebaseVersion=i,this._bucket=null,this._host=Cs,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=Ya,this._maxUploadRetryTime=Ka,this._requests=new Set,o!=null?this._bucket=ce.makeFromBucketSpec(o,this._host):this._bucket=Bt(this._host,this.app.options)}get host(){return this._host}set host(s){this._host=s,this._url!=null?this._bucket=ce.makeFromBucketSpec(this._url,s):this._bucket=Bt(s,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(s){Pt("time",0,Number.POSITIVE_INFINITY,s),this._maxUploadRetryTime=s}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(s){Pt("time",0,Number.POSITIVE_INFINITY,s),this._maxOperationRetryTime=s}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;const s=this._authProvider.getImmediate({optional:!0});if(s){const n=await s.getToken();if(n!==null)return n.accessToken}return null}async _getAppCheckToken(){const s=this._appCheckProvider.getImmediate({optional:!0});return s?(await s.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(s=>s.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(s){return new Je(this,s)}_makeRequest(s,n,a,o,i=!0){if(this._deleted)return new to(Is());{const r=po(s,this._appId,a,o,n,this._firebaseVersion,i);return this._requests.add(r),r.getPromise().then(()=>this._requests.delete(r),()=>this._requests.delete(r)),r}}async makeRequestWithTokens(s,n){const[a,o]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(s,n,a,o).getPromise()}}const Ft="@firebase/storage",Ht="0.13.2";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ts="storage";function yo(t=Gt(),s){t=xt(t);const a=Be(t,Ts).getImmediate({identifier:s}),o=Fs("storage");return o&&jo(a,...o),a}function jo(t,s,n,a={}){go(t,s,n,a)}function bo(t,{instanceIdentifier:s}){const n=t.getProvider("app").getImmediate(),a=t.getProvider("auth-internal"),o=t.getProvider("app-check-internal");return new fo(n,a,o,s,Bs)}function ko(){ze(new Pe(Ts,bo,"PUBLIC").setMultipleInstances(!0)),Re(Ft,Ht,""),Re(Ft,Ht,"esm2017")}ko();const Es={apiKey:"AIzaSyC8GW8S1UFOmCyrKV80n331G3Tm5tfW-fo",authDomain:"fspro-8f755.firebaseapp.com",projectId:"fspro-8f755",storageBucket:"fspro-8f755.firebasestorage.app",messagingSenderId:"83658549317",appId:"1:83658549317:web:fe0b8659df5015b7889b2c",measurementId:"G-301EY8V9S4"},st=Xt(Es),wo=Xt(Es,"secondary");Ua(st);const de=Us(st),Ve=Jt(st),Me=Jt(wo);yo(st);const vo=Object.freeze(Object.defineProperty({__proto__:null,auth:Ve,db:de,secondaryAuth:Me},Symbol.toStringTag,{value:"Module"})),L={USERS:"users",ATTENDANCE:"attendance",PROGRESS_REPORTS:"progressReports",HOLIDAY_REQUESTS:"holidayRequests",TASKS:"tasks",TIME_TABLES:"timeTables",SCHEDULES:"timeTables",COMPENSATION:"compensation",NOTIFICATIONS:"notifications"},z={async create(t,s){try{return(await Gs(Oe(de,t),{...s,createdAt:Le(),updatedAt:Le()})).id}catch(n){throw console.error("Error creating document:",n),n}},async getById(t,s){try{const n=qe(de,t,s),a=await Ks(n);return a.exists()?{id:a.id,...a.data()}:null}catch(n){throw console.error("Error getting document:",n),n}},async getAll(t,s="createdAt"){try{try{const n=nt(Oe(de,t),Ys(s,"desc"));return(await at(n)).docs.map(o=>({id:o.id,...o.data()}))}catch(n){return console.warn(`Ordering by ${s} failed, fetching without order:`,n.message),(await at(Oe(de,t))).docs.map(o=>({id:o.id,...o.data()}))}}catch(n){throw console.error("Error getting documents:",n),n}},async getWhere(t,s,n,a){try{const o=nt(Oe(de,t),St(s,n,a));return(await at(o)).docs.map(r=>({id:r.id,...r.data()}))}catch(o){throw console.error("Error getting documents with condition:",o),o}},async update(t,s,n){try{const a=qe(de,t,s);await Vs(a,{...n,updatedAt:Le()})}catch(a){throw console.error("Error updating document:",a),a}},async delete(t,s){try{const n=qe(de,t,s);await qs(n)}catch(n){throw console.error("Error deleting document:",n),n}},onSnapshot(t,s,n=[]){try{let a=Oe(de,t);return n.forEach(o=>{a=nt(a,St(o.field,o.operator,o.value))}),$s(a,o=>{const i=o.docs.map(r=>({id:r.id,...r.data()}));s(i)})}catch(a){throw console.error("Error setting up real-time listener:",a),a}}},je={async createUser(t){try{const s=qe(de,L.USERS,t.uid);return await Ws(s,{...t,createdAt:Le(),updatedAt:Le()}),t.uid}catch(s){throw console.error("Error creating user:",s),s}},async getUserByEmail(t){try{return(await z.getWhere(L.USERS,"email","==",t))[0]||null}catch(s){return console.error("Error getting user by email:",s),null}},async getUserByUid(t){try{return await z.getById(L.USERS,t)}catch(s){return console.error("Error getting user by UID:",s),null}},async getAllEmployees(){try{return await z.getWhere(L.USERS,"role","==","employee")}catch(t){return console.error("Error getting employees:",t),[]}}},Ut={async checkIn(t,s){return z.create(L.ATTENDANCE,{userId:t,...s,type:"checkin"})},async checkOut(t,s){return z.update(L.ATTENDANCE,t,{...s,type:"checkout"})},async getUserAttendance(t,s){return z.getWhere(L.ATTENDANCE,"userId","==",t)}},Te={async createTask(t){return z.create(L.TASKS,t)},async getUserTasks(t){return z.getWhere(L.TASKS,"assignedTo","==",t)},async getAllTasks(){return z.getAll(L.TASKS,"createdAt")},async updateTaskStatus(t,s,n="",a=""){const o={status:s,reason:n,employeeRemarks:a,updatedAt:new Date().toISOString()};return z.update(L.TASKS,t,o)},async updateTask(t,s){return z.update(L.TASKS,t,s)},async deleteTask(t){return z.delete(L.TASKS,t)}},pt={async createRequest(t){return z.create(L.HOLIDAY_REQUESTS,t)},async getUserRequests(t){return z.getWhere(L.HOLIDAY_REQUESTS,"userId","==",t)},async getAllRequests(){return z.getAll(L.HOLIDAY_REQUESTS,"requestDate")},async updateRequestStatus(t,s,n=""){return z.update(L.HOLIDAY_REQUESTS,t,{status:s,adminRemarks:n})}},Ee={async createNotification(t){return z.create(L.NOTIFICATIONS,t)},async getUserNotifications(t){return z.getWhere(L.NOTIFICATIONS,"userId","==",t)},async getTodayNotifications(t){const s=new Date().toISOString().split("T")[0];return(await z.getWhere(L.NOTIFICATIONS,"userId","==",t)).filter(a=>a.date===s).sort((a,o)=>{var i,r;return new Date(((i=o.createdAt)==null?void 0:i.seconds)*1e3)-new Date(((r=a.createdAt)==null?void 0:r.seconds)*1e3)})},async markAsRead(t){return z.update(L.NOTIFICATIONS,t,{isRead:!0,readAt:new Date().toISOString()})},async markAllAsRead(t){new Date().toISOString().split("T")[0];const a=(await this.getTodayNotifications(t)).filter(o=>!o.isRead).map(o=>this.markAsRead(o.id));return Promise.all(a)},async getUnreadCount(t){return(await this.getTodayNotifications(t)).filter(n=>!n.isRead).length}},Ne={async getUserCompensation(t){return z.getWhere(L.COMPENSATION,"employeeId","==",t)},async createCompensation(t){return z.create(L.COMPENSATION,t)},async checkMissedCheckIns(t){return[]},async getAllCompensation(){return z.getAll(L.COMPENSATION,"createdAt")},async updateCompensationStatus(t,s,n=""){return z.update(L.COMPENSATION,t,{status:s,adminRemarks:n})}},Ns=x.createContext(),Q=()=>{const t=x.useContext(Ns);if(!t)throw new Error("useAuth must be used within an AuthProvider");return t},So=({children:t})=>{const[s,n]=x.useState(null),[a,o]=x.useState(null),[i,r]=x.useState(!0),[l,u]=x.useState(!1),d=async(p,w)=>{try{try{const g=(await Qs(Ve,p,w)).user,f=await je.getUserByUid(g.uid);if(!f)throw new Error("User profile not found");if(!f.isActive)throw new Error("Account is deactivated");return n(g),o(f),R.success("Logged in successfully!"),{user:g,profile:f}}catch(v){if(v.code==="auth/user-not-found"||v.code==="auth/wrong-password"){console.log("Firebase Auth failed, checking for legacy user...");const g=await je.getUserByEmail(p);if(g&&g.password===w){if(!g.isActive)throw new Error("Account is deactivated");const f={uid:g.uid||g.id,email:g.email};return n(f),o(g),R.success("Logged in successfully! (Legacy Account)"),R.info("Please contact admin to upgrade your account for better security."),{user:f,profile:g}}}throw v}}catch(v){console.error("Login error:",v);let g="Failed to sign in";switch(v.code){case"auth/user-not-found":g="No account found with this email";break;case"auth/wrong-password":g="Incorrect password";break;case"auth/invalid-email":g="Invalid email address";break;case"auth/user-disabled":g="Account has been disabled";break;case"auth/too-many-requests":g="Too many failed attempts. Please try again later";break;default:g=v.message}throw R.error(g),new Error(g)}},j=async()=>{try{await Js(Ve),n(null),o(null),R.success("Logged out successfully!")}catch(p){throw R.error("Error logging out"),p}},y=async p=>{if(p)try{let w=await je.getUserByUid(p.uid);if(w)o(w);else{console.error("No profile found for user:",p.uid),console.log("Creating default admin profile for authenticated user...");const v={uid:p.uid,email:p.email||"<EMAIL>",name:"System Administrator",employeeId:"ADMIN001",role:"admin",isActive:!0};try{await je.createUser(v),console.log("Default admin profile created successfully"),o(v),R.success("Welcome! Admin profile created successfully.")}catch(g){console.error("Error creating default profile:",g),o(null),R.error("Error creating user profile. Please contact support.")}}}catch(w){console.error("Error loading user profile:",w),o(null)}else o(null)};x.useEffect(()=>Xs(Ve,async w=>{l||(n(w),await y(w),r(!1))}),[l]);const b={currentUser:s,userProfile:a,login:d,logout:j,loading:i,setIgnoreAuthChanges:u};return e.jsx(Ns.Provider,{value:b,children:!i&&t})},be={getCurrentWeekId(){const s=ct(new Date,{weekStartsOn:1});return W(s,"yyyy-'W'II")},getCurrentWeekDates(){const t=new Date,s=ct(t,{weekStartsOn:1}),n=Qt(t,{weekStartsOn:1});return{start:s,end:n}},async canEmployeeEditTimetable(t){try{const s=this.getCurrentWeekId(),n=await z.getWhere(L.TIME_TABLES,"userId","==",t);if(n.length===0)return{canEdit:!0,reason:"NO_TIMETABLE"};const a=n[0];return a.weekId===s&&a.setByEmployee?{canEdit:!1,reason:"ALREADY_SET_THIS_WEEK",message:"You have already set your timetable for this week. You cannot modify it until next week."}:a.weekId===s&&a.lastModifiedBy==="system_reset"?{canEdit:!0,reason:"CARRIED_OVER_SCHEDULE",message:"Previous week schedule carried over. You can modify it or save as-is."}:{canEdit:!0,reason:"CAN_EDIT"}}catch(s){return console.error("Error checking timetable edit permission:",s),{canEdit:!1,reason:"ERROR",message:"Error checking permissions"}}},async saveEmployeeTimetable(t,s,n){try{const a=this.getCurrentWeekId(),o=await this.canEmployeeEditTimetable(t);if(!o.canEdit)throw new Error(o.message||"Cannot edit timetable");const i=await z.getWhere(L.TIME_TABLES,"userId","==",t),r={userId:t,employeeId:s.employeeId,employeeName:s.name,schedule:n,weekId:a,setByEmployee:!0,lastModifiedBy:"employee",lastModifiedAt:new Date().toISOString()};return i.length>0?await z.update(L.TIME_TABLES,i[0].id,r):await z.create(L.TIME_TABLES,r),{success:!0}}catch(a){throw console.error("Error saving employee timetable:",a),a}},async saveAdminTimetable(t,s,n,a){try{const o=this.getCurrentWeekId(),i=await z.getWhere(L.TIME_TABLES,"userId","==",t),r={userId:t,employeeId:s.employeeId,employeeName:s.name,schedule:n,weekId:o,setByEmployee:!1,lastModifiedBy:"admin",lastModifiedAt:new Date().toISOString(),modifiedByAdminId:a};let l=!1;return i.length>0?(await z.update(L.TIME_TABLES,i[0].id,r),l=!0):await z.create(L.TIME_TABLES,r),l&&await this.notifyEmployeeOfTimetableChange(t,s.name),{success:!0}}catch(o){throw console.error("Error saving admin timetable:",o),o}},async notifyEmployeeOfTimetableChange(t,s){try{const n={userId:t,title:"Timetable Updated by Admin",message:"Your timetable has been updated by the administrator. Please check your new schedule.",type:"timetable_update",date:W(new Date,"yyyy-MM-dd"),read:!1,priority:"medium"};await Ee.createNotification(n)}catch(n){console.error("Error creating timetable notification:",n)}},async resetTimetablesForNewWeek(){try{const t=this.getCurrentWeekId();console.log("Checking for timetable reset for week:",t);const s=await z.getAll(L.TIME_TABLES);let n=0;for(const a of s)if(a.weekId&&a.weekId!==t){const o={...a,weekId:t,setByEmployee:!1,lastModifiedBy:"system_reset",lastModifiedAt:new Date().toISOString(),schedule:a.schedule||{}};await z.update(L.TIME_TABLES,a.id,o),n++}return console.log(`Reset ${n} timetables for new week with previous schedules carried over`),{resetCount:n}}catch(t){throw console.error("Error resetting timetables for new week:",t),t}},async checkAndPerformWeeklyReset(){try{await this.resetTimetablesForNewWeek()}catch(t){console.error("Error in weekly reset check:",t)}}},Rs=({height:t="auto",width:s="200px",className:n="",style:a={},showFallback:o=!0})=>{const[i,r]=x.useState(!1),[l,u]=x.useState(!1),d=()=>{console.warn("Logo image failed to load, showing fallback"),r(!0)},j=()=>{u(!0)},y=()=>e.jsx("div",{style:{height:t==="auto"?"50px":t,width:s,backgroundColor:"#3498db",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontWeight:"bold",fontSize:"18px",fontFamily:"Arial, sans-serif",...a},className:n,title:"FSPro - Employee Management System",children:"FSPro"});return i&&o?e.jsx(y,{}):e.jsxs("div",{className:`logo-container ${n}`,style:{position:"relative",display:"inline-block",width:"100%",maxWidth:s,height:"auto"},children:[e.jsx("img",{src:"/images/fsprologo.png",alt:"FSPro - Employee Management System",style:{height:t,width:"100%",maxWidth:s,opacity:l?1:.7,transition:"opacity 0.3s ease, transform 0.2s ease",display:"block",objectFit:"contain",...a},className:n,onError:d,onLoad:j,loading:"lazy"}),!l&&!i&&e.jsx("div",{style:{position:"absolute",top:0,left:0,height:t==="auto"?"50px":t,width:s,backgroundColor:"#f8f9fa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",animation:"pulse 1.5s ease-in-out infinite"},children:e.jsx("div",{style:{width:"60%",height:"60%",backgroundColor:"#e9ecef",borderRadius:"2px"}})})]})},Co=()=>{const[t,s]=x.useState({email:"",password:""}),[n,a]=x.useState(!1),[o,i]=x.useState(!1),[r,l]=x.useState(""),[u,d]=x.useState(!1),{login:j}=Q(),y=p=>{s({...t,[p.target.name]:p.target.value})},b=async p=>{p.preventDefault(),a(!0),l(""),console.log("Login attempt:",t.email);try{const w=await j(t.email,t.password);console.log("Login successful:",w)}catch(w){console.error("Login failed:",w),l("Invalid email or password. Please try again.")}finally{a(!1)}};return e.jsxs("div",{className:"login-container",style:{position:"relative",overflow:"hidden"},children:[e.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",zIndex:1},children:e.jsx("span",{className:"fspro-bg-text",children:"FSPro"})}),e.jsxs("div",{className:"login-card",style:{boxShadow:"0 10px 32px rgba(52, 78, 123, 0.15)",position:"relative",zIndex:2,padding:"32px 28px 28px 28px",maxWidth:380,width:"100%",margin:"20px 0"},children:[e.jsxs("div",{className:"text-center mb-20",children:[e.jsx("div",{style:{display:"flex",justifyContent:"center",marginBottom:"20px",padding:"0 20px"},children:e.jsx(Rs,{height:"auto",width:"280px",style:{maxHeight:"90px",maxWidth:"100%",objectFit:"contain",filter:"drop-shadow(0 4px 12px rgba(0,0,0,0.15))"}})}),e.jsx("h1",{className:"login-title",style:{fontWeight:800,letterSpacing:1,fontSize:"2rem",marginBottom:6,marginTop:0},children:"Login"}),e.jsx("div",{style:{width:"60px",height:"3px",background:"linear-gradient(135deg, #000000 0%, #1a237e 50%, #1E88E5 100%)",borderRadius:2,margin:"0 auto 12px auto"}}),e.jsx("div",{style:{background:"linear-gradient(135deg, #000000 0%, #1a237e 50%, #1E88E5 100%)",color:"#ffffff !important",fontWeight:700,fontSize:"1rem",letterSpacing:"0.04em",marginBottom:20,textShadow:"0 2px 8px rgba(0,0,0,0.2)",textTransform:"uppercase",fontFamily:"Segoe UI, Arial, sans-serif",padding:"6px 12px",borderRadius:"6px",display:"inline-block"},children:e.jsx("span",{style:{color:"#ffffff"},children:"Employee Management System"})})]}),r&&e.jsx("div",{style:{background:"#ffeaea",color:"#c0392b",padding:"10px",borderRadius:"6px",marginBottom:"18px",textAlign:"center",fontSize:"14px"},children:r}),e.jsxs("form",{onSubmit:b,autoComplete:"on",children:[e.jsxs("div",{className:"form-group",style:{position:"relative",marginBottom:"20px"},children:[e.jsx("input",{type:"email",name:"email",value:t.email,onChange:y,className:"form-input",required:!0,placeholder:" ","aria-label":"Email",autoFocus:!0,style:{paddingTop:"18px",paddingBottom:"8px"},autoComplete:"email"}),e.jsx("label",{className:"form-label",htmlFor:"email",style:{position:"absolute",left:"12px",top:t.email?"2px":"14px",fontSize:t.email?"12px":"15px",color:t.email?"#1a237e":"#333",background:"white",padding:"0 4px",transition:"all 0.2s",pointerEvents:"none"},children:"Email"})]}),e.jsxs("div",{className:"form-group",style:{position:"relative",marginBottom:"16px"},children:[e.jsx("input",{type:o?"text":"password",name:"password",value:t.password,onChange:y,className:"form-input",required:!0,placeholder:" ","aria-label":"Password",style:{paddingTop:"18px",paddingBottom:"8px"},autoComplete:"current-password"}),e.jsx("label",{className:"form-label",htmlFor:"password",style:{position:"absolute",left:"12px",top:t.password?"2px":"14px",fontSize:t.password?"12px":"15px",color:t.password?"#1a237e":"#333",background:"white",padding:"0 4px",transition:"all 0.2s",pointerEvents:"none"},children:"Password"}),e.jsx("button",{type:"button","aria-label":o?"Hide password":"Show password",onClick:()=>i(p=>!p),style:{position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#888",padding:0},tabIndex:-1,children:o?e.jsx(Zt,{size:20}):e.jsx(fe,{size:20})})]}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"18px"},children:[e.jsxs("label",{style:{display:"flex",alignItems:"center",fontSize:"14px",color:"#1a237e",fontWeight:600,cursor:"pointer"},children:[e.jsx("input",{type:"checkbox",checked:u,onChange:p=>d(p.target.checked),style:{marginRight:"7px"}}),"Remember me"]}),e.jsx("a",{href:"#",style:{color:"#1a237e",fontSize:"14px",textDecoration:"none"},tabIndex:0,children:"Forgot password?"})]}),e.jsxs("button",{type:"submit",className:"btn btn-primary modern-login-btn",style:{width:"100%",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",fontWeight:600,fontSize:"16px",letterSpacing:.5,marginTop:4},disabled:n,"aria-busy":n,children:[n?e.jsx(Zs,{size:20,className:"spin"}):e.jsx(en,{size:20}),n?"Logging in...":"Login"]})]})]})]})},Io=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState(0),[i,r]=x.useState(!1),[l,u]=x.useState(!1),d=x.useRef(null);x.useEffect(()=>{t!=null&&t.uid&&(j(),y())},[t]),x.useEffect(()=>{const g=f=>{d.current&&!d.current.contains(f.target)&&r(!1)};return document.addEventListener("mousedown",g),()=>document.removeEventListener("mousedown",g)},[]);const j=async()=>{try{u(!0);const g=await Ee.getTodayNotifications(t.uid);n(g)}catch(g){console.error("Error loading notifications:",g)}finally{u(!1)}},y=async()=>{try{const g=await Ee.getUnreadCount(t.uid);o(g)}catch(g){console.error("Error loading unread count:",g)}},b=async g=>{try{await Ee.markAsRead(g),n(f=>f.map(E=>E.id===g?{...E,isRead:!0}:E)),o(f=>Math.max(0,f-1))}catch(f){console.error("Error marking notification as read:",f),R.error("Failed to mark notification as read")}},p=async()=>{try{await Ee.markAllAsRead(t.uid),n(g=>g.map(f=>({...f,isRead:!0}))),o(0),R.success("All notifications marked as read")}catch(g){console.error("Error marking all notifications as read:",g),R.error("Failed to mark all notifications as read")}},w=g=>{switch(g){case"task_assigned":return e.jsx(ae,{size:16,style:{color:"#f39c12"}});case"task_completed":return e.jsx(te,{size:16,style:{color:"#27ae60"}});case"holiday_approved":return e.jsx(te,{size:16,style:{color:"#27ae60"}});case"holiday_rejected":return e.jsx(De,{size:16,style:{color:"#e74c3c"}});case"report_submitted":return e.jsx(Tt,{size:16,style:{color:"#3498db"}});case"late_checkin":return e.jsx(V,{size:16,style:{color:"#f39c12"}});default:return e.jsx(Tt,{size:16,style:{color:"#3498db"}})}},v=g=>{if(!g)return"";const f=g.seconds?new Date(g.seconds*1e3):new Date(g);return W(f,"HH:mm")};return e.jsxs("div",{className:"notification-dropdown",ref:d,style:{position:"relative",display:"inline-block"},children:[e.jsxs("button",{onClick:()=>r(!i),style:{position:"relative",background:i?"#f0f0f0":"none",border:"1px solid #ddd",cursor:"pointer",padding:"8px",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center"},children:[e.jsx(It,{size:20,style:{color:"#666"}}),a>0&&e.jsx("span",{style:{position:"absolute",top:"2px",right:"2px",backgroundColor:"#e74c3c",color:"white",borderRadius:"50%",width:"18px",height:"18px",fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold"},children:a>9?"9+":a})]}),i&&e.jsxs("div",{style:{position:"absolute",top:"100%",right:"0",backgroundColor:"white",border:"2px solid #e74c3c",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.3)",zIndex:99999,width:"350px",maxHeight:"400px",overflowY:"auto",marginTop:"5px"},onClick:g=>g.stopPropagation(),children:[e.jsxs("div",{style:{padding:"15px",borderBottom:"1px solid #eee",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("h4",{style:{margin:0,fontSize:"16px",color:"#333"},children:"Today's Notifications"}),a>0&&e.jsxs("button",{onClick:p,style:{background:"none",border:"none",color:"#3498db",cursor:"pointer",fontSize:"12px",display:"flex",alignItems:"center",gap:"4px"},children:[e.jsx(tn,{size:14}),"Mark all read"]})]}),e.jsx("div",{style:{maxHeight:"300px",overflowY:"auto"},children:l?e.jsx("div",{style:{padding:"20px",textAlign:"center",color:"#666"},children:"Loading notifications..."}):s.length===0?e.jsxs("div",{style:{padding:"20px",textAlign:"center",color:"#666"},children:[e.jsx(It,{size:32,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{style:{margin:0,fontSize:"14px"},children:"No notifications today"})]}):s.map(g=>e.jsx("div",{style:{padding:"12px 15px",borderBottom:"1px solid #f5f5f5",backgroundColor:g.isRead?"white":"#f8f9ff",cursor:g.isRead?"default":"pointer"},onClick:()=>!g.isRead&&b(g.id),children:e.jsxs("div",{style:{display:"flex",alignItems:"flex-start",gap:"10px"},children:[e.jsx("div",{style:{marginTop:"2px"},children:w(g.type)}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontSize:"14px",fontWeight:g.isRead?"normal":"bold",color:"#333",marginBottom:"4px"},children:g.title}),e.jsx("div",{style:{fontSize:"12px",color:"#666",marginBottom:"4px"},children:g.message}),e.jsx("div",{style:{fontSize:"11px",color:"#999"},children:v(g.createdAt)})]}),!g.isRead&&e.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#3498db",borderRadius:"50%",marginTop:"6px"}})]})},g.id))})]})]})},To=({onMobileMenuToggle:t})=>{const{userProfile:s}=Q();return e.jsxs("div",{className:"navbar",children:[e.jsxs("div",{className:"navbar-left",children:[e.jsx("button",{className:"mobile-menu-btn",onClick:t,"aria-label":"Toggle mobile menu",children:e.jsx(sn,{size:24})}),e.jsx("div",{className:"navbar-title",children:e.jsx("h1",{style:{fontSize:"24px",fontWeight:"600",color:"#333",margin:0},children:"Dashboard"})})]}),e.jsx("div",{className:"flex align-center gap-10",children:e.jsxs("div",{className:"flex align-center gap-10",children:[e.jsx(Io,{}),e.jsxs("div",{className:"flex align-center gap-10",children:[e.jsx(gt,{size:20,style:{color:"#666"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"14px",fontWeight:"500"},children:(s==null?void 0:s.name)||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||"Unknown User"}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:(s==null?void 0:s.role)==="admin"?"Administrator":"Employee"})]})]})]})})]})},Eo=({activeTab:t,setActiveTab:s,isMobileOpen:n,onMobileClose:a})=>{const{userProfile:o,logout:i}=Q(),d=(o==null?void 0:o.role)==="admin"?[{id:"admin-dashboard",label:"Admin Dashboard",icon:nn},{id:"employees",label:"Employee Management",icon:ue},{id:"admin-timetable",label:"Time Table Management",icon:se},{id:"admin-progress-reports",label:"Progress Reports",icon:he},{id:"attendance-records",label:"Attendance Records",icon:V},{id:"holiday-management",label:"Holiday Management",icon:se},{id:"task-management",label:"Task Management",icon:ke},{id:"compensation-records",label:"Compensation Records",icon:V}]:[{id:"dashboard",label:"Dashboard",icon:an},{id:"checkin",label:"Check In/Out",icon:V},{id:"timetable",label:"Time Table",icon:se},{id:"progress",label:"Progress Report",icon:he},{id:"holidays",label:"Holiday Requests",icon:se},{id:"tasks",label:"My Tasks",icon:ke},{id:"compensation",label:"Compensate Hours",icon:V}],j=async()=>{try{await i()}catch(y){console.error("Logout error:",y)}};return e.jsxs("div",{className:`sidebar ${n?"mobile-open":""}`,children:[e.jsxs("div",{style:{padding:"0 15px",marginBottom:"20px"},children:[e.jsx("div",{style:{display:"flex",alignItems:"center",marginBottom:"0px",padding:"0px 0 0 0",justifyContent:"center"},children:e.jsx(Rs,{height:"auto",width:"240px",style:{maxHeight:"110px",maxWidth:"100%",objectFit:"contain",filter:"drop-shadow(0 2px 6px rgba(0,0,0,0.1))"}})}),e.jsxs("div",{style:{fontSize:"14px",opacity:.8,textAlign:"center",marginTop:"0px",paddingTop:"0px"},children:[o==null?void 0:o.name," | ID: ",o==null?void 0:o.employeeId]})]}),e.jsxs("nav",{children:[d.map(y=>{const b=y.icon;return e.jsx("div",{className:`nav-item ${t===y.id?"active":""}`,onClick:()=>s(y.id),children:e.jsxs("a",{href:"#",onClick:p=>p.preventDefault(),children:[e.jsx(b,{size:18}),y.label]})},y.id)}),e.jsx("div",{className:"nav-item",onClick:j,style:{marginTop:"20px"},children:e.jsxs("a",{href:"#",onClick:y=>y.preventDefault(),children:[e.jsx(on,{size:18}),"Logout"]})})]})]})},Wt=()=>{const{userProfile:t}=Q(),[s,n]=x.useState({totalCheckIns:0,totalCheckOuts:0,lateCheckIns:0,totalAbsences:0,unapprovedLeaves:0,totalEmployees:0}),[a,o]=x.useState(!0);x.useEffect(()=>{i()},[t]);const i=async()=>{try{if((t==null?void 0:t.role)==="admin"){const[l,u,d]=await Promise.all([z.getAll(L.ATTENDANCE),z.getAll(L.HOLIDAY_REQUESTS),z.getAll(L.USERS)]),j=l.filter(w=>w.type==="checkin").length,y=l.filter(w=>w.type==="checkout").length,b=u.filter(w=>w.status==="pending").length,p=d.filter(w=>w.role==="employee").length;n({totalCheckIns:j,totalCheckOuts:y,lateCheckIns:0,totalAbsences:0,unapprovedLeaves:b,totalEmployees:p})}else{const[l,u,d]=await Promise.all([z.getWhere(L.ATTENDANCE,"userId","==",t.uid),z.getWhere(L.HOLIDAY_REQUESTS,"userId","==",t.uid),z.getWhere(L.TASKS,"assignedTo","==",t.uid)]),j=l.filter(p=>p.type==="checkin").length,y=l.filter(p=>p.type==="checkout").length,b=u.filter(p=>p.status==="pending").length;n({totalCheckIns:j,totalCheckOuts:y,lateCheckIns:0,totalAbsences:0,unapprovedLeaves:b,totalTasks:d.length})}}catch(l){console.error("Error loading dashboard stats:",l)}finally{o(!1)}},r=({icon:l,title:u,value:d,color:j="#3498db"})=>e.jsxs("div",{className:"stat-card",children:[e.jsx(l,{size:32,style:{color:j,marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:j},children:a?"...":d}),e.jsx("div",{className:"stat-label",children:u})]});return e.jsxs("div",{className:"content",children:[e.jsx("h2",{style:{marginBottom:"30px",color:"#333"},children:(t==null?void 0:t.role)==="admin"?"Admin Dashboard":"Employee Dashboard"}),e.jsx("div",{className:"stats-grid",children:(t==null?void 0:t.role)==="admin"?e.jsxs(e.Fragment,{children:[e.jsx(r,{icon:ue,title:"Total Employees",value:s.totalEmployees,color:"#27ae60"}),e.jsx(r,{icon:te,title:"Total Check-Ins",value:s.totalCheckIns,color:"#3498db"}),e.jsx(r,{icon:re,title:"Total Check-Outs",value:s.totalCheckOuts,color:"#9b59b6"}),e.jsx(r,{icon:Ae,title:"Late Check-Ins",value:s.lateCheckIns,color:"#e74c3c"}),e.jsx(r,{icon:se,title:"Unapproved Leaves",value:s.unapprovedLeaves,color:"#f39c12"})]}):e.jsxs(e.Fragment,{children:[e.jsx(r,{icon:te,title:"Total Check-Ins",value:s.totalCheckIns,color:"#3498db"}),e.jsx(r,{icon:re,title:"Total Check-Outs",value:s.totalCheckOuts,color:"#9b59b6"}),e.jsx(r,{icon:Ae,title:"Late Check-Ins",value:s.lateCheckIns,color:"#e74c3c"}),e.jsx(r,{icon:se,title:"Pending Leaves",value:s.unapprovedLeaves,color:"#f39c12"})]})}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Recent Activity"}),e.jsxs("div",{style:{color:"#666",textAlign:"center",padding:"40px"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"Recent activity will appear here"})]})]})]})};class Y{static timeToMinutes(s){if(!s)return 0;const n=s.split(":"),a=parseInt(n[0])||0,o=parseInt(n[1])||0;return a*60+o}static minutesToTime(s){const n=Math.floor(s/60),a=s%60;return`${n.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}static getCurrentDay(){return W(new Date,"EEEE")}static getCurrentTime(){return W(new Date,"HH:mm:ss")}static hasSchedule(s){if(!s)return console.log("No schedule provided"),!1;const n=this.getCurrentDay();return console.log("Checking schedule for day:",{currentDay:n,scheduleKeys:Object.keys(s),scheduleForToday:s[n],hasSlots:s[n]&&s[n].length>0}),s[n]&&s[n].length>0}static getTodaySchedule(s){if(!s)return console.log("No schedule provided to getTodaySchedule"),[];const n=this.getCurrentDay(),a=s[n]||[];return console.log("Getting today's schedule:",{currentDay:n,scheduleKeys:Object.keys(s),todaySlots:a,slotsCount:a.length}),a}static getNextScheduledSlot(s,n){const a=this.getTodaySchedule(s),o=this.timeToMinutes(this.getCurrentTime());if(a.length===0)return null;const i=this.getCompletedSlots(n);for(let r=0;r<a.length;r++){const l=a[r],u=this.timeToMinutes(l.checkIn);this.timeToMinutes(l.checkOut);const d=i.some(v=>v.slotIndex===r),j=n.some(v=>v.type==="checkin"&&(v.slotIndex===r||Math.abs(this.timeToMinutes(v.scheduledCheckIn||v.checkInTime)-u)<15)&&!n.some(g=>g.type==="checkout"&&(g.slotIndex===r||g.checkInTime===v.checkInTime)&&new Date(g.timestamp)>new Date(v.timestamp))),y=u+120,b=o<=y,p=u+30,w=o>p;if(!d&&!j&&b)return{...l,slotIndex:r,scheduledCheckIn:l.checkIn,scheduledCheckOut:l.checkOut,hasMissedRegularWindow:w}}return null}static getCompletedSlots(s){const n=s.filter(i=>i.type==="checkin"),a=s.filter(i=>i.type==="checkout"),o=[];return n.forEach(i=>{const r=a.find(l=>i.slotIndex!==void 0&&l.slotIndex!==void 0?l.slotIndex===i.slotIndex&&new Date(l.timestamp)>new Date(i.timestamp):l.checkInTime===i.checkInTime);r&&o.push({slotIndex:i.slotIndex,scheduledCheckIn:i.scheduledCheckIn||i.checkInTime,scheduledCheckOut:r.scheduledCheckOut||r.checkOutTime,checkInTime:i.checkInTime,checkOutTime:r.checkOutTime})}),o}static isCurrentlyCheckedIn(s){const n=s.filter(o=>o.type==="checkin").length,a=s.filter(o=>o.type==="checkout").length;return n>a}static getLastCheckIn(s){const n=s.filter(a=>a.type==="checkin");return n.length>0?n[n.length-1]:null}static validateRegularCheckIn(s,n){if(!this.hasSchedule(s))return{allowed:!1,reason:"NO_SCHEDULE",message:"📅 Please set up your work schedule in the Timetable section first to start checking in"};if(this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"ALREADY_CHECKED_IN",message:"✅ You are already checked in for this session. Please check out first before checking in again"};if(this.getRecentCheckIns(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKIN",message:"⏰ Please wait 10 minutes between check-in attempts to prevent duplicate entries"};const o=this.getNextScheduledSlot(s,n);if(!o){const j=this.getTodaySchedule(s);return this.getCompletedSlots(n).length===j.length?{allowed:!1,reason:"ALL_SLOTS_COMPLETED",message:"🎯 All your scheduled work sessions for today are complete. Great job!"}:{allowed:!1,reason:"MISSED_SLOTS",message:"❌ You have missed your scheduled work sessions for today. Please contact your administrator if needed."}}if(n.find(j=>j.type==="checkin"&&j.isLate===!0&&j.slotIndex===o.slotIndex))return{allowed:!1,reason:"LATE_CHECKIN_DONE",message:"Late check-in already completed for this slot"};const r=this.timeToMinutes(this.getCurrentTime()),l=this.timeToMinutes(o.scheduledCheckIn),u=l-60,d=l+30;if(o.hasMissedRegularWindow)return{allowed:!1,reason:"MISSED_REGULAR_WINDOW",message:`You missed the regular check-in window for slot ${o.slotIndex+1}. Please use late check-in option.`};if(r<u)return{allowed:!1,reason:"TOO_EARLY",message:`⏰ Check-in window opens at ${this.minutesToTime(u)} (1 hour before your scheduled time)`};if(r>d){const j=r-l,y=Math.floor(j/60),b=j%60;return{allowed:!1,reason:"TOO_LATE",message:`🚨 You are ${y>0?`${y}h `:""}${b}m late. Please use the "Late Check-in" button and provide a reason`}}return{allowed:!0,slot:o,message:"Regular check-in allowed"}}static validateLateCheckIn(s,n){if(!this.hasSchedule(s))return{allowed:!1,reason:"NO_SCHEDULE",message:"📅 Please set up your work schedule in the Timetable section first to start checking in"};if(this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"ALREADY_CHECKED_IN",message:"You are already checked in"};if(this.getRecentCheckIns(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKIN",message:"Multiple check-ins within 10 minutes are not allowed"};const o=this.getNextScheduledSlot(s,n);if(!o){const d=this.getTodaySchedule(s);return this.getCompletedSlots(n).length===d.length?{allowed:!1,reason:"ALL_SLOTS_COMPLETED",message:"All your scheduled work sessions for today are complete"}:{allowed:!1,reason:"MISSED_SLOTS",message:"You have missed your scheduled work sessions for today"}}const i=this.timeToMinutes(this.getCurrentTime()),r=this.timeToMinutes(o.scheduledCheckIn),l=r+30,u=r+120;if(i>u)return{allowed:!1,reason:"TOO_LATE_FOR_LATE",message:"❌ Late check-in window has expired (maximum 2 hours after scheduled time). Please contact your administrator"};if(i<=l)return{allowed:!1,reason:"USE_REGULAR",message:"Use regular check-in option - you are not late yet"};if(i>l&&i<=u){const d=i-r,j=Math.floor(d/60),y=d%60;return{allowed:!0,slot:o,message:`Late check-in allowed (${j>0?`${j}h `:""}${y}m late)`}}return{allowed:!1,reason:"UNKNOWN",message:"Unable to determine late check-in eligibility"}}static validateRegularCheckOut(s,n){if(!this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"NOT_CHECKED_IN",message:"📝 Please check in first before you can check out"};const a=this.getLastCheckIn(n);if(!a)return{allowed:!1,reason:"NO_CHECKIN_FOUND",message:"No check-in record found"};if(this.getRecentCheckOuts(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKOUT",message:"⏰ Please wait 10 minutes between check-out attempts to prevent duplicate entries"};const i=this.timeToMinutes(this.getCurrentTime()),r=this.timeToMinutes(a.checkInTime);return i<=r?{allowed:!1,reason:"INVALID_TIME",message:"⚠️ Check-out time must be after your check-in time. Please wait a moment and try again"}:{allowed:!0,lastCheckIn:a,message:"Regular check-out allowed"}}static validateLateCheckOut(s,n){if(!this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"NOT_CHECKED_IN",message:"📝 Please check in first before you can check out"};const a=this.getLastCheckIn(n);if(!a)return{allowed:!1,reason:"NO_CHECKIN_FOUND",message:"No check-in record found"};if(this.getRecentCheckOuts(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKOUT",message:"Multiple check-outs within 10 minutes are not allowed"};const i=this.timeToMinutes(this.getCurrentTime()),r=this.timeToMinutes(a.checkInTime);if(i<=r)return{allowed:!1,reason:"INVALID_TIME",message:"Check-out time must be after check-in time"};const l=a.scheduledCheckOut;if(l){const d=this.timeToMinutes(l)+30;if(i<d)return{allowed:!1,reason:"NOT_LATE_YET",message:"✅ Please use the regular check-out button as you are not working overtime yet"}}return{allowed:!0,lastCheckIn:a,message:"Late check-out allowed"}}static getRecentCheckOuts(s,n){const a=this.timeToMinutes(this.getCurrentTime());return s.filter(i=>i.type==="checkout").filter(i=>{const r=this.timeToMinutes(i.checkOutTime);return a-r<=n})}static getRecentCheckIns(s,n){const a=this.timeToMinutes(this.getCurrentTime());return s.filter(i=>i.type==="checkin").filter(i=>{const r=this.timeToMinutes(i.checkInTime);return a-r<=n})}static calculateWorkingHours(s,n){if(!s||!n)return{hours:0,minutes:0,total:0,display:"Incomplete"};const a=this.timeToMinutes(s);let i=this.timeToMinutes(n)-a;if(i<0)return{hours:0,minutes:0,total:0,display:"Error: Invalid time range"};const r=Math.floor(i/60),l=i%60,u=i/60;return{hours:r,minutes:l,total:u,display:`${r}h ${l}m`}}static canUseCompensationHours(s,n){return W(new Date,"yyyy-MM-dd"),s.length>0?{allowed:!1,reason:"HAS_RECORDS",message:"Compensation hours can only be used for completely missed days"}:this.hasSchedule(n)?{allowed:!0,message:"Compensation hours can be used"}:{allowed:!1,reason:"NO_SCHEDULE",message:"No schedule set for today"}}}class No{static calculateDayWorkingHours(s){if(!s||s.length===0)return{regularHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},lateHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},totalHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},pairs:[],status:"NO_RECORDS"};const n=s.filter(b=>b.type==="checkin"),a=s.filter(b=>b.type==="checkout"),o=this.pairCheckInOuts(n,a),i=o.filter(b=>!b.isLate),r=o.filter(b=>b.isLate),l=this.calculatePairsHours(i),u=this.calculatePairsHours(r),d=l.totalMinutes+u.totalMinutes,j={hours:Math.floor(d/60),minutes:d%60,totalMinutes:d,display:`${Math.floor(d/60)}h ${d%60}m`};let y="COMPLETE";return(n.length!==a.length||o.some(b=>!b.complete))&&(y="INCOMPLETE"),{regularHours:l,lateHours:u,totalHours:j,pairs:o,status:y,breakdown:{regularPairs:i.length,latePairs:r.length,totalPairs:o.length}}}static pairCheckInOuts(s,n){const a=[],o=[...s].sort((r,l)=>Y.timeToMinutes(r.checkInTime)-Y.timeToMinutes(l.checkInTime)),i=[...n].sort((r,l)=>Y.timeToMinutes(r.checkOutTime)-Y.timeToMinutes(l.checkOutTime));for(let r=0;r<o.length;r++){const l=o[r],u=i[r];u?a.push({checkIn:l,checkOut:u,complete:!0,isLate:l.isLate||l.lateReason||u.isLate||u.lateReason,workingHours:Y.calculateWorkingHours(l.checkInTime,u.checkOutTime)}):a.push({checkIn:l,checkOut:null,complete:!1,isLate:l.isLate||l.lateReason,workingHours:{hours:0,minutes:0,total:0,display:"Incomplete"}})}return a}static calculatePairsHours(s){let n=0,a=0;return s.forEach(o=>{o.complete&&o.workingHours.total>0&&(n+=o.workingHours.hours*60+o.workingHours.minutes,a++)}),{hours:Math.floor(n/60),minutes:n%60,totalMinutes:n,display:`${Math.floor(n/60)}h ${n%60}m`,completePairs:a}}static formatWorkingHoursDisplay(s){const{regularHours:n,lateHours:a,totalHours:o,status:i}=s;return i==="NO_RECORDS"?"No records":i==="INCOMPLETE"?"Incomplete":a.totalMinutes===0?n.display:n.totalMinutes===0?`${a.display} (Late)`:`${n.display} + ${a.display} = ${o.display}`}static calculateScheduledHours(s,n){if(!s||!s[n])return{hours:0,minutes:0,display:"0h 0m"};const a=s[n];let o=0;return a.forEach(i=>{if(i.checkIn&&i.checkOut){const r=Y.calculateWorkingHours(i.checkIn,i.checkOut);r.total>0&&(o+=r.hours*60+r.minutes)}}),{hours:Math.floor(o/60),minutes:o%60,totalMinutes:o,display:`${Math.floor(o/60)}h ${o%60}m`}}static compareHours(s,n){const a=s.totalMinutes||0,o=n.totalMinutes||0,i=a-o;let r="EXACT";i>0?r="OVERTIME":i<0&&(r="UNDERTIME");const l=Math.floor(Math.abs(i)/60),u=Math.abs(i)%60;return{status:r,difference:{hours:l,minutes:u,totalMinutes:Math.abs(i),display:`${l}h ${u}m`},percentage:o>0?a/o*100:0}}static validateWorkingHours(s){const n=[],a=[];if(!s||s.length===0)return{valid:!0,errors:n,warnings:a};const o=s.filter(r=>r.type==="checkin"),i=s.filter(r=>r.type==="checkout");o.length!==i.length&&n.push("Unequal number of check-ins and check-outs"),o.forEach((r,l)=>{const u=i[l];if(u){const d=Y.timeToMinutes(r.checkInTime);Y.timeToMinutes(u.checkOutTime)<=d&&n.push(`Invalid time range: ${r.checkInTime} to ${u.checkOutTime}`)}});for(let r=0;r<o.length-1;r++){const l=Y.timeToMinutes(o[r].checkInTime),u=Y.timeToMinutes(o[r+1].checkInTime);Math.abs(u-l)<10&&a.push("Multiple check-ins within 10 minutes detected")}return{valid:n.length===0,errors:n,warnings:a}}}const Ro=({attendanceRecords:t,schedule:s})=>{const[n,a]=x.useState({}),[o,i]=x.useState(!1),[r,l]=x.useState({reason:"",type:"",time:""}),u=()=>{const E=t.reduce((m,N)=>{const k=N.date;return m[k]||(m[k]=[]),m[k].push(N),m},{}),D={};return Object.keys(E).forEach(m=>{const N=E[m];D[m]=d(N)}),D},d=E=>{const D=E.filter(O=>O.type==="checkin").sort((O,S)=>new Date(O.timestamp)-new Date(S.timestamp)),m=E.filter(O=>O.type==="checkout").sort((O,S)=>new Date(O.timestamp)-new Date(S.timestamp)),N=[],k=new Set;return D.forEach((O,S)=>{let _=null;for(let T=0;T<m.length;T++){const h=m[T];if(!k.has(T)&&new Date(h.timestamp)>new Date(O.timestamp)){_=h,k.add(T);break}}let P=null;_&&(P=Y.calculateWorkingHours(O.checkInTime,_.checkOutTime)),N.push({checkIn:O,checkOut:_,workingHours:P,pairIndex:S,isComplete:!!_,isLate:O.isLate||_&&_.isLate})}),m.forEach((O,S)=>{k.has(S)||N.push({checkIn:null,checkOut:O,workingHours:null,pairIndex:N.length,isComplete:!1,isLate:O.isLate})}),N.sort((O,S)=>{const _=O.checkIn?new Date(O.checkIn.timestamp):new Date(O.checkOut.timestamp);return(S.checkIn?new Date(S.checkIn.timestamp):new Date(S.checkOut.timestamp))-_})},j=E=>{a(D=>({...D,[E]:!D[E]}))},y=(E,D)=>{E&&E.lateReason&&(l({reason:E.lateReason,type:"Check-in",time:E.checkInTime}),i(!0))},b=E=>new Date(E).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),p=E=>{if(!E)return"-";const D=E.split(":");return D.length===2?`${D[0]}:${D[1]}:00`:E},w=(E,D)=>{if(!E)return null;const m=E.isLate,N=m?"status-rejected":D==="checkin"?"status-approved":"status-pending",k=D==="checkin"?m?"Late Check In":"Check In":m?"Late Check Out":"Check Out";return e.jsxs("div",{children:[e.jsx("span",{className:`status-badge ${N}`,children:k}),m&&E.lateReason&&e.jsxs("div",{style:{fontSize:"11px",color:"#666",marginTop:"3px"},children:["Reason: ",E.lateReason]})]})},v=E=>{const D=E.filter(O=>O.isComplete);let m=0,N=0,k=0;return D.forEach(O=>{if(O.workingHours&&O.workingHours.total>0){const S=O.workingHours.hours*60+O.workingHours.minutes;m+=S,O.isLate?k+=S:N+=S}}),{totalHours:{hours:Math.floor(m/60),minutes:m%60,display:`${Math.floor(m/60)}h ${m%60}m`},regularHours:{hours:Math.floor(N/60),minutes:N%60,display:`${Math.floor(N/60)}h ${N%60}m`},lateHours:{hours:Math.floor(k/60),minutes:k%60,display:`${Math.floor(k/60)}h ${k%60}m`},completePairs:D.length,totalPairs:E.length}},g=u(),f=Object.keys(g).sort((E,D)=>new Date(D)-new Date(E));return f.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No attendance records found"})]}):e.jsxs("div",{className:"paired-attendance",children:[f.map(E=>{const D=g[E],m=n[E],N=v(D);return e.jsxs("div",{className:"date-group",style:{marginBottom:"20px"},children:[e.jsxs("div",{className:"date-header",style:{background:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"8px",padding:"15px",cursor:"pointer",display:"flex",justifyContent:"space-between",alignItems:"center",transition:"background 0.2s"},onClick:()=>j(E),onMouseEnter:k=>k.target.style.background="#e9ecef",onMouseLeave:k=>k.target.style.background="#f8f9fa",children:[e.jsxs("div",{children:[e.jsx("h4",{style:{margin:"0 0 5px 0",color:"#333"},children:b(E)}),e.jsxs("div",{style:{fontSize:"14px",color:"#666"},children:[e.jsxs("span",{style:{marginRight:"20px"},children:["Pairs: ",N.completePairs,"/",N.totalPairs]}),e.jsxs("span",{style:{marginRight:"20px"},children:["Total Hours: ",N.totalHours.display]}),N.lateHours.hours>0&&e.jsxs("span",{style:{color:"#e67e22"},children:["Late: ",N.lateHours.display]})]})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx("span",{style:{fontSize:"12px",color:"#666"},children:m?"Hide Details":"Show Details"}),m?e.jsx(rn,{size:20}):e.jsx(ln,{size:20})]})]}),m&&e.jsxs("div",{className:"date-records",style:{border:"1px solid #dee2e6",borderTop:"none",borderRadius:"0 0 8px 8px",background:"white"},children:[e.jsxs("table",{className:"table",style:{margin:0},children:[e.jsx("thead",{children:e.jsxs("tr",{style:{background:"#f8f9fa"},children:[e.jsx("th",{children:"Regular Check In"}),e.jsx("th",{children:"Regular Check Out"}),e.jsx("th",{children:"Late Check In"}),e.jsx("th",{children:"Late Check Out"}),e.jsx("th",{children:"Working Hours"}),e.jsx("th",{children:"Status"})]})}),e.jsx("tbody",{children:D.map((k,O)=>{var S,_;return e.jsxs("tr",{style:{background:k.isComplete?"white":"#fff3cd",borderLeft:k.isLate?"3px solid #e74c3c":"3px solid transparent"},children:[e.jsx("td",{children:k.checkIn&&!k.checkIn.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px"},children:p(k.checkIn.checkInTime)}),w(k.checkIn,"checkin")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{children:k.checkOut&&!k.checkOut.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px"},children:p(k.checkOut.checkOutTime)}),w(k.checkOut,"checkout")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{children:k.checkIn&&k.checkIn.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:"#f39c12"},children:p(k.checkIn.checkInTime)}),e.jsxs("button",{onClick:()=>y(k.checkIn),style:{background:"none",border:"none",color:"#f39c12",cursor:"pointer",fontSize:"11px",textDecoration:"underline",padding:"2px 0",display:"flex",alignItems:"center"},title:"Click to view late reason",children:[e.jsx(ae,{size:12,style:{marginRight:"3px"}}),"Late (View Reason)"]})]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{children:k.checkOut&&k.checkOut.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:"#f39c12"},children:p(k.checkOut.checkOutTime)}),e.jsxs("div",{style:{fontSize:"11px",color:"#f39c12",display:"flex",alignItems:"center"},children:[e.jsx(ae,{size:12,style:{marginRight:"3px"}}),"Late Check Out"]})]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{style:{fontFamily:"monospace",fontWeight:"bold"},children:k.isComplete?e.jsxs("div",{children:[e.jsx("div",{style:{color:"#007bff",fontWeight:"bold"},children:k.workingHours?k.workingHours.display:"0h 0m"}),(((S=k.checkIn)==null?void 0:S.isLate)||((_=k.checkOut)==null?void 0:_.isLate))&&e.jsx("div",{style:{fontSize:"11px",color:"#f39c12",marginTop:"2px"},children:"(Includes Late Hours)"})]}):e.jsx("span",{style:{color:"#ffc107"},children:"Incomplete"})}),e.jsx("td",{children:k.isComplete?e.jsxs("span",{style:{color:"#28a745",fontSize:"12px"},children:["✓ Complete ",k.isLate?"(Late)":""]}):e.jsx("span",{style:{color:"#ffc107",fontSize:"12px"},children:"⚠ Incomplete"})})]},O)})})]}),e.jsx("div",{style:{padding:"15px",background:"#f8f9fa",borderTop:"1px solid #dee2e6",borderRadius:"0 0 8px 8px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"15px"},children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Regular Hours:"}),e.jsx("br",{}),e.jsx("span",{style:{color:"#28a745"},children:N.regularHours.display})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Late Hours:"}),e.jsx("br",{}),e.jsx("span",{style:{color:"#ffc107"},children:N.lateHours.display})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Total Hours:"}),e.jsx("br",{}),e.jsx("span",{style:{color:"#007bff",fontWeight:"bold"},children:N.totalHours.display})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Completion:"}),e.jsx("br",{}),e.jsxs("span",{style:{color:"#6c757d"},children:[N.completePairs,"/",N.totalPairs," pairs"]})]})]})})]})]},E)}),o&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e4},children:e.jsxs("div",{style:{backgroundColor:"white",borderRadius:"8px",padding:"30px",maxWidth:"500px",width:"90%",maxHeight:"80vh",overflowY:"auto",boxShadow:"0 10px 30px rgba(0, 0, 0, 0.3)"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[e.jsxs("h3",{style:{margin:0,color:"#333",display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(ae,{size:24,style:{color:"#f39c12"}}),"Late ",r.type," Reason"]}),e.jsx("button",{onClick:()=>i(!1),style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666",padding:"0",width:"30px",height:"30px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(De,{size:20})})]}),e.jsxs("div",{style:{marginBottom:"20px"},children:[e.jsxs("div",{style:{padding:"15px",backgroundColor:"#fff3cd",borderRadius:"6px",border:"1px solid #ffeaa7",marginBottom:"15px"},children:[e.jsxs("div",{style:{fontSize:"14px",color:"#856404",marginBottom:"5px"},children:[e.jsx("strong",{children:"Time:"})," ",r.time]}),e.jsxs("div",{style:{fontSize:"14px",color:"#856404"},children:[e.jsx("strong",{children:"Type:"})," Late ",r.type]})]}),e.jsxs("div",{children:[e.jsxs("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#333"},children:["Reason for Late ",r.type,":"]}),e.jsx("div",{style:{padding:"15px",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"6px",minHeight:"80px",fontSize:"14px",lineHeight:"1.5",color:"#333"},children:r.reason})]})]}),e.jsx("div",{style:{textAlign:"right"},children:e.jsx("button",{onClick:()=>i(!1),className:"btn btn-secondary",style:{padding:"10px 20px"},children:"Close"})})]})})]})},Ao=({todayAttendance:t})=>{const[s,n]=x.useState(!1),[a,o]=x.useState({reason:"",type:"",time:""}),i=b=>{const p=b.filter(f=>f.type==="checkin").sort((f,E)=>new Date(f.timestamp)-new Date(E.timestamp)),w=b.filter(f=>f.type==="checkout").sort((f,E)=>new Date(f.timestamp)-new Date(E.timestamp)),v=[],g=new Set;return p.forEach((f,E)=>{let D=null;for(let N=0;N<w.length;N++){const k=w[N];if(!g.has(N)&&new Date(k.timestamp)>new Date(f.timestamp)){D=k,g.add(N);break}}let m=null;D&&(m=Y.calculateWorkingHours(f.checkInTime,D.checkOutTime)),v.push({checkIn:f,checkOut:D,workingHours:m,pairIndex:E,isComplete:!!D,isLate:f.isLate||D&&D.isLate})}),w.forEach((f,E)=>{g.has(E)||v.push({checkIn:null,checkOut:f,workingHours:null,pairIndex:v.length,isComplete:!1,isLate:f.isLate})}),v.sort((f,E)=>{const D=f.checkIn?new Date(f.checkIn.timestamp):new Date(f.checkOut.timestamp),m=E.checkIn?new Date(E.checkIn.timestamp):new Date(E.checkOut.timestamp);return D-m})},r=b=>{const p=b.filter(f=>f.isComplete);let w=0,v=0,g=0;return p.forEach(f=>{if(f.workingHours&&f.workingHours.total>0){const E=f.workingHours.hours*60+f.workingHours.minutes;w+=E,f.isLate?g+=E:v+=E}}),{totalHours:{hours:Math.floor(w/60),minutes:w%60,display:`${Math.floor(w/60)}h ${w%60}m`},regularHours:{hours:Math.floor(v/60),minutes:v%60,display:`${Math.floor(v/60)}h ${v%60}m`},lateHours:{hours:Math.floor(g/60),minutes:g%60,display:`${Math.floor(g/60)}h ${g%60}m`},completePairs:p.length,totalPairs:b.length}},l=b=>{if(!b)return"-";const p=b.split(":");return p.length===2?`${p[0]}:${p[1]}:00`:b},u=(b,p)=>{b&&b.lateReason&&(o({reason:b.lateReason,type:p==="checkin"?"Check-in":"Check-out",time:p==="checkin"?b.checkInTime:b.checkOutTime}),n(!0))},d=(b,p)=>{if(!b)return null;const w=b.isLate,v=w?"status-rejected":p==="checkin"?"status-approved":"status-pending",g=p==="checkin"?w?"Late Check In":"Check In":w?"Late Check Out":"Check Out";return e.jsxs("div",{children:[e.jsx("span",{className:`status-badge ${v}`,style:{fontSize:"11px"},children:g}),w&&b.lateReason&&e.jsxs("button",{style:{fontSize:"10px",color:"#f39c12",marginLeft:"6px",background:"none",border:"none",cursor:"pointer",textDecoration:"underline"},onClick:()=>u(b,p),children:[e.jsx(ae,{size:12,style:{marginRight:"3px"}}),"View Reason"]})]})},j=i(t),y=r(j);return e.jsxs("div",{children:[e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%",borderCollapse:"collapse"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Check In"}),e.jsx("th",{children:"Check Out"}),e.jsx("th",{children:"Working Hours"}),e.jsx("th",{children:"Status"})]})}),e.jsx("tbody",{children:j.map((b,p)=>e.jsxs("tr",{style:{background:b.isComplete?"white":"#fff3cd",borderLeft:b.isLate?"3px solid #e74c3c":"3px solid transparent",height:"auto",verticalAlign:"top"},children:[e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:b.checkIn?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:b.checkIn.isLate?"#f39c12":void 0},children:l(b.checkIn.checkInTime)}),d(b.checkIn,"checkin")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:b.checkOut?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:b.checkOut.isLate?"#f39c12":void 0},children:l(b.checkOut.checkOutTime)}),d(b.checkOut,"checkout")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:b.workingHours?b.workingHours.display:"-"}),e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:b.isComplete?e.jsxs("span",{style:{color:b.isLate?"#f39c12":"#27ae60",fontWeight:500},children:["✓ Complete",b.isLate?" (Late)":""]}):e.jsx("span",{style:{color:"#999"},children:"Incomplete"})})]},p))})]}),e.jsxs("div",{style:{marginTop:"10px",fontWeight:500,color:"#333"},children:["Total Working Hours: ",y.totalHours.display,y.lateHours.totalMinutes>0&&e.jsxs("span",{style:{color:"#f39c12",marginLeft:"10px"},children:["(Late: ",y.lateHours.display,")"]})]}),s&&e.jsx("div",{className:"modal-overlay",onClick:()=>n(!1),children:e.jsxs("div",{className:"modal-content",onClick:b=>b.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h3",{children:["Late ",a.type," Reason"]}),e.jsx("button",{className:"modal-close",onClick:()=>n(!1),children:e.jsx(De,{size:18})})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Time:"})," ",a.time]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Reason:"})," ",a.reason]})]})]})})]})},Se=async(t,s,n,a,o={})=>{try{const i={userId:t,type:s,title:n,message:a,date:new Date().toISOString().split("T")[0],isRead:!1,...o};await Ee.createNotification(i)}catch(i){console.error("Error creating notification:",i)}},Ce={TASK_ASSIGNED:"task_assigned",TASK_COMPLETED:"task_completed",TASK_UPDATED:"task_updated",HOLIDAY_APPROVED:"holiday_approved",HOLIDAY_REJECTED:"holiday_rejected",HOLIDAY_SUBMITTED:"holiday_submitted",LATE_CHECKIN:"late_checkin"},Do=async(t,s,n)=>{await Se(t,Ce.TASK_ASSIGNED,"New Task Assigned",`You have been assigned a new task: "${s}" by ${n}`,{taskTitle:s,assignedBy:n})},Oo=async(t,s,n)=>{await Se(t,Ce.TASK_COMPLETED,"Task Completed",`${n} has completed the task: "${s}"`,{taskTitle:s,employeeName:n})},Mo=async(t,s,n,a)=>{await Se(t,Ce.TASK_UPDATED,"Task Status Updated",`${n} has updated the task "${s}" status to: ${a}`,{taskTitle:s,employeeName:n,status:a})},Lo=async(t,s,n)=>{await Se(t,Ce.HOLIDAY_APPROVED,"Holiday Request Approved",`Your holiday request from ${s} to ${n} has been approved`,{startDate:s,endDate:n})},_o=async(t,s,n,a)=>{await Se(t,Ce.HOLIDAY_REJECTED,"Holiday Request Rejected",`Your holiday request from ${s} to ${n} has been rejected. Reason: ${a}`,{startDate:s,endDate:n,reason:a})},zo=async(t,s,n,a)=>{await Se(t,Ce.HOLIDAY_SUBMITTED,"New Holiday Request",`${s} has submitted a holiday request from ${n} to ${a}`,{employeeName:s,startDate:n,endDate:a})},Po=async(t,s,n,a)=>{await Se(t,Ce.LATE_CHECKIN,"Late Check-in Alert",`${s} checked in late at ${n}. Reason: ${a}`,{employeeName:s,time:n,reason:a})},Bo=()=>{const{userProfile:t}=Q(),[s,n]=x.useState(null),[a,o]=x.useState([]),[i,r]=x.useState(!1),[l,u]=x.useState(!1),[d,j]=x.useState(""),[y,b]=x.useState(""),[p,w]=x.useState(null),[v,g]=x.useState({}),[f,E]=x.useState([]),[D,m]=x.useState(!1),[N,k]=x.useState(new Date);x.useEffect(()=>{S(),O()},[t]),x.useEffect(()=>{p&&a&&_(a)},[p,a]),x.useEffect(()=>{const M=setInterval(()=>{k(new Date)},1e3);return()=>clearInterval(M)},[]);const O=async()=>{try{console.log("Loading schedule for user:",t.uid);const M=await z.getWhere(L.TIME_TABLES,"userId","==",t.uid);if(console.log("Found time tables:",M),M.length>0){const U=M[0].schedule||{};console.log("Setting schedule data:",U),w(U)}else console.log("No time tables found, setting empty schedule"),w({})}catch(M){console.error("Error loading schedule:",M),w({})}},S=async()=>{try{const M=W(new Date,"yyyy-MM-dd"),U=await z.getWhere(L.ATTENDANCE,"userId","==",t.uid),q=U.sort((J,ee)=>{const Ie=new Date(ee.date)-new Date(J.date);return Ie!==0?Ie:new Date(ee.timestamp)-new Date(J.timestamp)});E(q);const G=U.filter(J=>J.date===M).sort((J,ee)=>new Date(ee.timestamp)-new Date(J.timestamp));o(G);const ne=Y.isCurrentlyCheckedIn(G);n(ne?"checked-in":"not-checked-in"),p&&_(G)}catch(M){console.error("Error loading attendance:",M)}},_=M=>{if(!p)return;const U=Y.validateRegularCheckIn(p,M),q=Y.validateLateCheckIn(p,M),G=Y.validateRegularCheckOut(p,M),ne=Y.validateLateCheckOut(p,M);g({regularCheckIn:U,lateCheckIn:q,regularCheckOut:G,lateCheckOut:ne})},P=async()=>{const M=Y.validateRegularCheckIn(p,a);if(!M.allowed){R.error(M.message);return}if(Y.getRecentCheckIns(a,10).length>0){R.error("Multiple check-ins within 10 minutes are not allowed");return}r(!0);try{const q=new Date,G=M.slot,ne={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(q,"yyyy-MM-dd"),day:W(q,"EEEE"),checkInTime:W(q,"HH:mm:ss"),timestamp:q.toISOString(),type:"checkin",scheduledCheckIn:G.scheduledCheckIn,scheduledCheckOut:G.scheduledCheckOut,slotIndex:G.slotIndex};await Ut.checkIn(t.uid,ne),R.success("Checked in successfully!"),S()}catch(q){console.error("Check-in error:",q),R.error("Failed to check in")}finally{r(!1)}},T=async()=>{const M=Y.validateRegularCheckOut(p,a);if(!M.allowed){R.error(M.message);return}r(!0);try{const U=new Date,q=M.lastCheckIn,G=Y.calculateWorkingHours(q.checkInTime,W(U,"HH:mm:ss")),ne={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(U,"yyyy-MM-dd"),day:W(U,"EEEE"),checkOutTime:W(U,"HH:mm:ss"),workingHours:G.total,workingHoursDisplay:G.display,timestamp:U.toISOString(),type:"checkout",checkInTime:q.checkInTime,scheduledCheckIn:q.scheduledCheckIn,scheduledCheckOut:q.scheduledCheckOut,slotIndex:q.slotIndex};await z.create(L.ATTENDANCE,ne),R.success("Checked out successfully!"),S()}catch(U){console.error("Check-out error:",U),R.error("Failed to check out")}finally{r(!1)}},h=async()=>{if(!d.trim()){R.error("Please provide a reason for late check-in");return}const M=Y.validateLateCheckIn(p,a);if(!M.allowed){R.error(M.message);return}r(!0);try{const U=new Date,q=M.slot,G={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(U,"yyyy-MM-dd"),day:W(U,"EEEE"),checkInTime:W(U,"HH:mm:ss"),timestamp:U.toISOString(),type:"checkin",isLate:!0,lateReason:d.trim(),scheduledCheckIn:q.scheduledCheckIn,scheduledCheckOut:q.scheduledCheckOut,slotIndex:q.slotIndex};await Ut.checkIn(t.uid,G);const J=(await z.getAll(L.USERS)).filter(ee=>ee.role==="admin");for(const ee of J)await Po(ee.uid,t.name,W(U,"HH:mm:ss"),d.trim());R.success("Late check-in recorded successfully!"),u(!1),j(""),b(""),S()}catch(U){console.error("Late check-in error:",U),R.error("Failed to record late check-in")}finally{r(!1)}},c=async()=>{const M=Y.validateLateCheckOut(p,a);if(!M.allowed){R.error(M.message);return}r(!0);try{const U=new Date,q=M.lastCheckIn,G=Y.calculateWorkingHours(q.checkInTime,W(U,"HH:mm:ss")),ne={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(U,"yyyy-MM-dd"),day:W(U,"EEEE"),checkOutTime:W(U,"HH:mm:ss"),workingHours:G.total,workingHoursDisplay:G.display,timestamp:U.toISOString(),type:"checkout",checkInTime:q.checkInTime,scheduledCheckIn:q.scheduledCheckIn,scheduledCheckOut:q.scheduledCheckOut,slotIndex:q.slotIndex,isLate:!0};await z.create(L.ATTENDANCE,ne),R.success("Late check-out recorded successfully!"),u(!1),j(""),b(""),S()}catch(U){console.error("Late check-out error:",U),R.error("Failed to record late check-out")}finally{r(!1)}},I=()=>{y==="checkin"?h():y==="checkout"&&c()},B=(()=>{const M=s==="checked-in",U=()=>{var J;return!p||M?!1:((J=v.regularCheckIn)==null?void 0:J.allowed)===!0},q=()=>{var J,ee;return!p||M?!1:((J=v.regularCheckIn)==null?void 0:J.allowed)===!1&&((ee=v.regularCheckIn)==null?void 0:ee.reason)==="TOO_LATE"},G=()=>{var ee;if(!p||!M)return!1;const J=a.find(Ie=>Ie.type==="checkin");return J&&!J.isLate&&((ee=v.regularCheckOut)==null?void 0:ee.allowed)===!0},ne=()=>{var ee;if(!p||!M)return!1;const J=a.find(Ie=>Ie.type==="checkin");return J&&J.isLate&&((ee=v.regularCheckOut)==null?void 0:ee.allowed)===!1};return M?{text:"Currently Checked In",color:"#27ae60",icon:te,action:"Check Out",handler:T,canRegularCheckOut:G(),canLateCheckOut:ne()}:{text:"Not Checked In",color:"#f39c12",icon:V,action:"Check In",handler:P,canRegularCheckIn:U(),canLateCheckIn:q()}})(),F=B.icon,H=s==="checked-in",K=Y.validateRegularCheckIn(p,a),Z=Y.validateLateCheckIn(p,a),ie=Y.validateRegularCheckOut(p,a),C=Y.validateLateCheckOut(p,a),X=a.filter(M=>M.type==="checkin").sort((M,U)=>new Date(U.timestamp)-new Date(M.timestamp))[0],$=(X==null?void 0:X.isLate)===!0;return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"card text-center",style:{marginBottom:"20px",marginTop:"10px"},children:[e.jsx("h2",{style:{marginBottom:"15px",color:"#333",fontSize:"24px"},children:"Check In/Out"}),e.jsx(F,{size:48,style:{color:B.color,marginBottom:"15px"}}),e.jsx("h3",{style:{color:B.color,marginBottom:"15px",fontSize:"20px"},children:B.text}),e.jsxs("p",{style:{color:"#666",marginBottom:"15px",fontSize:"14px"},children:["Current Time: ",W(N,"HH:mm:ss - EEEE, MMMM do, yyyy")]}),!H&&!K.allowed&&!Z.allowed&&e.jsxs("div",{style:{background:"#f8d7da",border:"1px solid #f5c6cb",borderRadius:"4px",padding:"12px",marginBottom:"15px",color:"#721c24",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:["❌ ",K.message||Z.message]}),H&&!ie.allowed&&!C.allowed&&e.jsxs("div",{style:{background:"#f8d7da",border:"1px solid #f5c6cb",borderRadius:"4px",padding:"8px",marginBottom:"15px",color:"#721c24",fontSize:"14px"},children:["❌ ",ie.message||C.message]}),e.jsxs("div",{style:{display:"flex",gap:"12px",justifyContent:"center",flexWrap:"wrap"},children:[!H&&K.allowed&&e.jsx("button",{onClick:P,disabled:i,className:"btn btn-success",style:{fontSize:"16px",padding:"12px 24px"},children:i?"Processing...":"Regular Check-in"}),!H&&!K.allowed&&Z.allowed&&e.jsxs("button",{onClick:()=>{b("checkin"),u(!0)},disabled:i,className:"btn btn-warning",style:{fontSize:"16px",padding:"12px 24px"},title:"Submit late check-in with reason",children:[e.jsx(ae,{size:16,style:{marginRight:"6px"}}),"Late Check-in"]}),H&&!$&&ie.allowed&&e.jsx("button",{onClick:T,disabled:i,className:"btn btn-danger",style:{fontSize:"16px",padding:"12px 24px"},children:i?"Processing...":"Regular Check-out"}),H&&$&&C.allowed&&e.jsxs("button",{onClick:()=>{b("checkout"),u(!0)},disabled:i,className:"btn btn-warning",style:{fontSize:"16px",padding:"12px 24px"},title:"Submit late check-out",children:[e.jsx(ae,{size:16,style:{marginRight:"6px"}}),"Late Check-out"]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[e.jsx("h3",{style:{margin:0,color:"#333"},children:D?"All Attendance Records":"Today's Attendance"}),e.jsxs("div",{style:{display:"flex",gap:"10px"},children:[e.jsx("button",{onClick:()=>m(!1),className:`btn ${D?"btn-secondary":"btn-primary"}`,style:{padding:"8px 16px",fontSize:"14px"},children:"Today Only"}),e.jsx("button",{onClick:()=>m(!0),className:`btn ${D?"btn-primary":"btn-secondary"}`,style:{padding:"8px 16px",fontSize:"14px"},children:"All Records"})]})]}),D?e.jsx(Ro,{attendanceRecords:f,schedule:p}):e.jsx(e.Fragment,{children:a.length>0?e.jsx(e.Fragment,{children:e.jsx(Ao,{todayAttendance:a})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No attendance records for today"})]})}),l&&e.jsx("div",{className:"modal-overlay",onClick:()=>{u(!1),j(""),b("")},children:e.jsxs("div",{className:"modal-content",onClick:M=>M.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h3",{children:["Late ",y==="checkin"?"Check-in":"Check-out"]}),e.jsx("button",{className:"modal-close",onClick:()=>{u(!1),j(""),b("")},children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[y==="checkin"?e.jsxs(e.Fragment,{children:[e.jsx("p",{style:{marginBottom:"15px",color:"#666"},children:"Please provide a reason for your late check-in:"}),e.jsx("textarea",{value:d,onChange:M=>j(M.target.value),placeholder:"Enter reason for late check-in...",className:"form-textarea",rows:"4",style:{width:"100%",marginBottom:"20px"}})]}):e.jsx("p",{style:{marginBottom:"20px",color:"#666"},children:"Are you sure you want to submit a late check-out?"}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>{u(!1),j(""),b("")},className:"btn btn-secondary",disabled:i,children:"Cancel"}),e.jsx("button",{onClick:I,className:"btn btn-warning",disabled:i||y==="checkin"&&!d.trim(),children:i?"Processing...":`Submit Late ${y==="checkin"?"Check-in":"Check-out"}`})]})]})]})})]})]})},Fo=()=>{const{userProfile:t}=Q(),[s,n]=x.useState({}),[a,o]=x.useState(!1),[i,r]=x.useState(!1),[l,u]=x.useState(!0),[d,j]=x.useState(null),y=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];x.useEffect(()=>{t&&(p(),b(),be.checkAndPerformWeeklyReset())},[t]);const b=async()=>{try{const m=await be.canEmployeeEditTimetable(t.uid);u(m.canEdit),j(m)}catch(m){console.error("Error checking edit permissions:",m),u(!1)}},p=async()=>{try{const m=await z.getWhere(L.TIME_TABLES,"userId","==",t.uid);if(m.length>0)n(m[0].schedule||{});else{const N={};y.forEach(k=>{N[k]=[]}),n(N)}}catch(m){console.error("Error loading timetable:",m)}},w=m=>{n(N=>({...N,[m]:[...N[m]||[],{checkIn:"",checkOut:""}]}))},v=(m,N)=>{n(k=>({...k,[m]:k[m].filter((O,S)=>S!==N)}))},g=(m,N,k,O)=>{n(S=>({...S,[m]:S[m].map((_,P)=>P===N?{..._,[k]:O}:_)}))},f=(m,N)=>{if(!m||!N)return"00:00:00";const k=new Date(`2000-01-01T${m}`),O=new Date(`2000-01-01T${N}`);if(O<=k)return"00:00:00";const S=O-k,_=Math.floor(S/1e3),P=Math.floor(_/3600),T=Math.floor(_%3600/60),h=_%60;return`${P.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}:${h.toString().padStart(2,"0")}`},E=m=>{const N=s[m]||[];let k=0;N.forEach(P=>{if(P.checkIn&&P.checkOut){const T=new Date(`2000-01-01T${P.checkIn}`),h=new Date(`2000-01-01T${P.checkOut}`);if(h>T){const c=h-T;k+=Math.floor(c/1e3)}}});const O=Math.floor(k/3600),S=Math.floor(k%3600/60),_=k%60;return`${O.toString().padStart(2,"0")}:${S.toString().padStart(2,"0")}:${_.toString().padStart(2,"0")}`},D=async()=>{o(!0);try{await be.saveEmployeeTimetable(t.uid,{employeeId:t.employeeId,name:t.name},s),R.success("Time table saved successfully!"),r(!1),await b()}catch(m){console.error("Error saving timetable:",m),R.error(m.message||"Failed to save time table")}finally{o(!1)}};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsxs("div",{children:[e.jsx("h2",{style:{color:"#333",margin:"0 0 5px 0"},children:"Set Your Time Table"}),e.jsxs("p",{style:{color:"#666",fontSize:"14px",margin:0},children:["Current Week: ",be.getCurrentWeekId()]})]}),e.jsx("div",{className:"flex gap-10",children:i?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>r(!1),className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:D,className:"btn btn-success",disabled:a,children:a?"Saving...":"Save Changes"})]}):e.jsx("button",{onClick:()=>r(!0),className:"btn btn-primary",disabled:!l,style:{opacity:l?1:.6,cursor:l?"pointer":"not-allowed"},children:l?"Edit Time Table":"Cannot Edit"})})]}),!l&&d&&e.jsx("div",{className:"card",style:{marginBottom:"20px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderLeft:"4px solid #f39c12"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",padding:"15px"},children:[e.jsx(cn,{size:24,style:{color:"#f39c12"}}),e.jsxs("div",{children:[e.jsx("h4",{style:{margin:"0 0 5px 0",color:"#856404"},children:"Timetable Editing Restricted"}),e.jsx("p",{style:{margin:0,color:"#856404",fontSize:"14px"},children:d.message||"You cannot edit your timetable at this time."}),e.jsx("p",{style:{margin:"5px 0 0 0",color:"#856404",fontSize:"12px",fontStyle:"italic"},children:"You can set a new timetable at the beginning of next week."})]})]})}),l&&d&&d.reason==="CARRIED_OVER_SCHEDULE"&&e.jsx("div",{className:"card",style:{marginBottom:"20px",backgroundColor:"#d1ecf1",border:"1px solid #bee5eb",borderLeft:"4px solid #17a2b8"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",padding:"15px"},children:[e.jsx(ae,{size:24,style:{color:"#17a2b8"}}),e.jsxs("div",{children:[e.jsx("h4",{style:{margin:"0 0 5px 0",color:"#0c5460"},children:"Previous Week Schedule Carried Over"}),e.jsx("p",{style:{margin:0,color:"#0c5460",fontSize:"14px"},children:"Your previous week's schedule has been carried over. You can modify it or save as-is."}),e.jsx("p",{style:{margin:"5px 0 0 0",color:"#0c5460",fontSize:"12px",fontStyle:"italic"},children:"Once you save, you won't be able to modify it again this week."})]})]})}),e.jsx("div",{className:"card",children:y.map(m=>e.jsxs("div",{style:{marginBottom:"30px",paddingBottom:"20px",borderBottom:"1px solid #eee"},children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h3",{style:{color:"#333",margin:0},children:m}),e.jsxs("div",{className:"flex align-center gap-10",children:[e.jsxs("span",{style:{color:"#666",fontSize:"14px"},children:["Total: ",E(m)]}),i&&e.jsxs("button",{onClick:()=>w(m),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},children:[e.jsx(ge,{size:14})," Add Time Slot"]})]})]}),s[m]&&s[m].length>0?e.jsx("div",{style:{display:"grid",gap:"10px"},children:s[m].map((N,k)=>e.jsxs("div",{className:"flex align-center gap-10",style:{padding:"10px",background:"#f8f9fa",borderRadius:"4px"},children:[e.jsxs("div",{className:"flex align-center gap-10",style:{flex:1},children:[e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"Check In"}),e.jsx("input",{type:"time",value:N.checkIn,onChange:O=>g(m,k,"checkIn",O.target.value),className:"form-input",style:{width:"120px"},disabled:!i})]}),e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"Check Out"}),e.jsx("input",{type:"time",value:N.checkOut,onChange:O=>g(m,k,"checkOut",O.target.value),className:"form-input",style:{width:"120px"},disabled:!i})]}),e.jsxs("div",{style:{minWidth:"80px",textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:"Duration"}),e.jsx("div",{style:{fontWeight:"bold",color:"#3498db",fontFamily:"monospace"},children:f(N.checkIn,N.checkOut)})]})]}),i&&e.jsx("button",{onClick:()=>v(m,k),className:"btn btn-danger",style:{padding:"5px",minWidth:"auto"},children:e.jsx(De,{size:14})})]},k))}):e.jsxs("div",{style:{textAlign:"center",padding:"20px",color:"#666",background:"#f8f9fa",borderRadius:"4px"},children:[e.jsx(se,{size:24,style:{opacity:.3,marginBottom:"5px"}}),e.jsxs("p",{style:{fontSize:"14px"},children:["No time slots set for ",m]}),i&&e.jsxs("button",{onClick:()=>w(m),className:"btn btn-primary",style:{marginTop:"10px",padding:"5px 15px",fontSize:"12px"},children:[e.jsx(ge,{size:14})," Add Time Slot"]})]})]},m))})]})};class Ho{async fileToBase64(s){return new Promise((n,a)=>{const o=new FileReader;o.readAsDataURL(s),o.onload=()=>n(o.result),o.onerror=i=>a(i)})}async processFile(s){try{const n=await this.fileToBase64(s);return{success:!0,fileName:s.name,fileSize:s.size,fileType:s.type,fileData:n,uploadedAt:new Date().toISOString()}}catch(n){throw console.error("Error processing file:",n),new Error(`Failed to process file: ${n.message}`)}}createDownloadLink(s,n){try{if(console.log("Creating download link for:",s),console.log("Base64 data length:",n==null?void 0:n.length),!n||!s)throw new Error("Missing file data or filename");const a=n.includes(",")?n.split(",")[1]:n,o=atob(a),i=new Array(o.length);for(let y=0;y<o.length;y++)i[y]=o.charCodeAt(y);const r=new Uint8Array(i);let l="application/octet-stream";if(n.startsWith("data:"))l=n.substring(5,n.indexOf(";"));else{const y=s.split(".").pop().toLowerCase();l={pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",txt:"text/plain",jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png"}[y]||"application/octet-stream"}const u=new Blob([r],{type:l}),d=URL.createObjectURL(u),j=document.createElement("a");return j.href=d,j.download=s,j.style.display="none",document.body.appendChild(j),j.click(),document.body.removeChild(j),setTimeout(()=>URL.revokeObjectURL(d),1e3),console.log("Download initiated successfully for:",s),{success:!0}}catch(a){throw console.error("Error creating download link:",a),new Error(`Failed to download file: ${a.message}`)}}generateProgressReportPath(s,n){const a=Date.now(),o=n.replace(/[^a-zA-Z0-9.-]/g,"_");return`progress-reports/${s}/${a}_${o}`}generateFilePath(s,n,a){const o=Date.now(),i=a.replace(/[^a-zA-Z0-9.-]/g,"_");return`${s}/${n}/${o}_${i}`}getFileExtension(s){return s.split(".").pop().toLowerCase()}isValidFileType(s,n=[]){n.length===0&&(n=["pdf","doc","docx","txt","jpg","jpeg","png","gif"]);const a=this.getFileExtension(s.name);return n.includes(a)}isValidFileSize(s,n=5){return s.size/1048576<=n}formatFileSize(s){if(s===0)return"0 Bytes";const n=1024,a=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(s)/Math.log(n));return parseFloat((s/Math.pow(n,o)).toFixed(2))+" "+a[o]}}const Ye=new Ho,Fe=({isOpen:t,onClose:s,title:n,content:a,type:o="text",employeeName:i="",date:r="",time:l=""})=>{if(!t)return null;const u=()=>{switch(o){case"remarks":return e.jsx(xe,{size:24,style:{color:"#3498db"}});case"details":return e.jsx(he,{size:24,style:{color:"#27ae60"}});default:return e.jsx(fe,{size:24,style:{color:"#666"}})}},d=()=>{if(n)return n;switch(o){case"remarks":return"Admin Remarks";case"details":return"Task Details";default:return"View Details"}};return e.jsxs("div",{className:"modal-overlay",onClick:s,style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3,padding:"20px"},children:[e.jsxs("div",{className:"modal-content",onClick:j=>j.stopPropagation(),style:{backgroundColor:"white",borderRadius:"12px",width:"100%",maxWidth:"600px",maxHeight:"80vh",overflow:"hidden",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.15)",animation:"modalSlideIn 0.3s ease-out"},children:[e.jsx("div",{className:"modal-header",style:{padding:"24px 24px 0 24px",borderBottom:"1px solid #eee",marginBottom:"0"},children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"20px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px",flex:1},children:[u(),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,color:"#333",fontSize:"18px",fontWeight:"600"},children:d()}),i&&e.jsxs("p",{style:{margin:"4px 0 0 0",color:"#666",fontSize:"14px"},children:[i,r&&` • ${r}`,l&&` • ${l}`]})]})]}),e.jsx("button",{className:"modal-close",onClick:s,style:{background:"none",border:"none",cursor:"pointer",color:"#666",padding:"8px",borderRadius:"6px",transition:"background-color 0.2s",display:"flex",alignItems:"center",justifyContent:"center"},onMouseEnter:j=>j.target.style.backgroundColor="#f5f5f5",onMouseLeave:j=>j.target.style.backgroundColor="transparent",children:e.jsx(De,{size:20})})]})}),e.jsx("div",{className:"modal-body",style:{padding:"24px",overflowY:"auto",maxHeight:"calc(80vh - 120px)"},children:e.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"8px",padding:"16px",minHeight:"120px",lineHeight:"1.6",fontSize:"14px",color:"#333",whiteSpace:"pre-wrap",wordWrap:"break-word",fontFamily:"inherit"},children:a||e.jsxs("span",{style:{color:"#999",fontStyle:"italic"},children:["No ",o==="remarks"?"remarks":"content"," available"]})})}),e.jsx("div",{style:{padding:"16px 24px 24px 24px",borderTop:"1px solid #eee",display:"flex",justifyContent:"flex-end"},children:e.jsx("button",{onClick:s,className:"btn btn-secondary",style:{padding:"10px 20px",fontSize:"14px",fontWeight:"500"},children:"Close"})})]}),e.jsx("style",{jsx:!0,children:`
        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
      `})]})},Uo=()=>{const{currentUser:t,userProfile:s}=Q(),[n,a]=x.useState([]),[o,i]=x.useState(!1),[r,l]=x.useState({tasksCompleted:"",file:null}),[u,d]=x.useState(!1),[j,y]=x.useState(!1),[b,p]=x.useState({});x.useEffect(()=>{w()},[s]);const w=async()=>{try{const m=await z.getWhere(L.PROGRESS_REPORTS,"userId","==",s.uid);a(m)}catch(m){console.error("Error loading progress reports:",m)}},v=m=>{if(m.target.name==="file"){const N=m.target.files[0];if(N){const k=N.size/1048576,O=5;if(k>O){R.error(`File size must be less than ${O}MB`),m.target.value="";return}if(!Ye.isValidFileType(N)){R.error("Invalid file type. Please upload PDF, DOC, DOCX, TXT, JPG, or PNG files."),m.target.value="";return}l({...r,file:N})}}else l({...r,[m.target.name]:m.target.value})},g=async m=>{if(m.preventDefault(),!r.tasksCompleted.trim()){R.error("Please describe the tasks you completed today");return}if(!s){R.error("User profile not loaded. Please refresh the page.");return}d(!0);try{const N=new Date;console.log("User Profile:",s);let k={fileName:"",fileSize:0,fileType:"",fileData:"",hasFile:!1};if(r.file)try{console.log("Processing file for Firestore storage...");const P=r.file.size/(1024*1024),T=5;if(P>T)console.warn(`File too large: ${P.toFixed(2)}MB (max: ${T}MB)`),R.error(`File too large (${P.toFixed(2)}MB). Maximum size is ${T}MB.`),k={fileName:r.file.name,fileSize:r.file.size,fileType:r.file.type,fileData:"",hasFile:!0,uploadedAt:new Date().toISOString(),uploadError:"File too large for upload"};else{const h=await Ye.processFile(r.file);k={fileName:h.fileName,fileSize:h.fileSize,fileType:h.fileType,fileData:h.fileData,hasFile:!0,uploadedAt:h.uploadedAt},console.log("File processed successfully:",{name:h.fileName,size:h.fileSize}),R.success("File processed successfully!")}}catch(P){console.error("Error processing file:",P),R.error("Failed to process file. Report will be saved without attachment."),k={fileName:r.file.name,fileSize:r.file.size,fileType:r.file.type,fileData:"",hasFile:!0,uploadedAt:new Date().toISOString(),uploadError:P.message}}const O={userId:s.uid||s.id||"",employeeId:s.employeeId||s.uid||"",employeeName:s.name||s.displayName||s.email||"Unknown User",date:W(N,"yyyy-MM-dd"),time:W(N,"HH:mm:ss"),tasksCompleted:r.tasksCompleted.trim(),...k,status:"submitted",adminRemarks:"",createdAt:N.toISOString()};console.log("Current User:",t),console.log("User Profile:",s),console.log("Report Data:",O);const{createdAt:S,..._}=O;await z.create(L.PROGRESS_REPORTS,_),R.success("Progress report submitted successfully!"),l({tasksCompleted:"",file:null}),i(!1),w()}catch(N){console.error("Error submitting progress report:",N),R.error("Failed to submit progress report")}finally{d(!1)}},f=m=>{const N={submitted:"status-pending",reviewed:"status-approved",rejected:"status-rejected"};return e.jsx("span",{className:`status-badge ${N[m]||"status-pending"}`,children:m.charAt(0).toUpperCase()+m.slice(1)})},E=m=>{p({title:"Tasks Completed",content:m.tasksCompleted,type:"details",employeeName:m.employeeName,date:m.date,time:m.time}),y(!0)},D=m=>{p({title:"Admin Remarks",content:m.adminRemarks,type:"remarks",employeeName:m.employeeName,date:m.date,time:m.time}),y(!0)};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Submit Daily Progress"}),e.jsx("button",{onClick:()=>i(!o),className:"btn btn-primary",children:o?"Cancel":"Submit Progress"})]}),o&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Daily Progress Report"}),e.jsxs("form",{onSubmit:g,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Tasks Completed Today *"}),e.jsx("textarea",{name:"tasksCompleted",value:r.tasksCompleted,onChange:v,className:"form-textarea",placeholder:"Describe your daily progress, tasks completed, achievements, etc...",required:!0,style:{minHeight:"120px"}})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Attach File (Optional)"}),e.jsxs("div",{style:{position:"relative"},children:[e.jsx("input",{type:"file",name:"file",onChange:v,className:"form-input",accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"}),e.jsx("div",{style:{fontSize:"12px",color:"#666",marginTop:"5px"},children:"Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG (Max 5MB)"})]}),r.file&&e.jsxs("div",{style:{marginTop:"10px",padding:"10px",background:"#f8f9fa",borderRadius:"4px",display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(dn,{size:16,style:{color:"#27ae60"}}),e.jsxs("span",{style:{fontSize:"14px"},children:[r.file.name," (",(r.file.size/1024/1024).toFixed(2)," MB)"]})]})]}),e.jsxs("div",{className:"flex justify-between align-center",children:[e.jsxs("div",{style:{color:"#666",fontSize:"14px"},children:["Report Date: ",W(new Date,"MMMM do, yyyy")]}),e.jsx("button",{type:"submit",className:"btn btn-success",disabled:u,children:u?"Submitting...":"Submit Progress"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Your Previous Reports"}),n.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Date"}),e.jsx("th",{style:{width:"80px",minWidth:"80px"},children:"Time"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Tasks Completed"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"File"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Admin Remarks"})]})}),e.jsx("tbody",{children:n.map(m=>e.jsxs("tr",{children:[e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:m.date}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:m.time}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>E(m),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{style:{padding:"8px",textAlign:"center"},children:m.fileName?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"4px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",marginBottom:"2px"},children:[e.jsx(he,{size:14,style:{color:"#3498db"}}),e.jsx("span",{style:{fontSize:"11px",fontWeight:"bold",maxWidth:"80px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:m.fileName})]}),m.fileData?e.jsxs("button",{onClick:()=>{try{Ye.createDownloadLink(m.fileName,m.fileData),R.success("File downloaded!")}catch(N){console.error("Download error:",N),R.error("Failed to download file")}},style:{background:"#27ae60",color:"white",border:"none",borderRadius:"4px",padding:"4px 8px",fontSize:"10px",cursor:"pointer",display:"flex",alignItems:"center",gap:"4px"},title:"Download file",children:[e.jsx(ft,{size:12}),"Download"]}):e.jsx("span",{style:{color:m.uploadError?"#e74c3c":"#ccc",fontSize:"10px",fontStyle:"italic"},children:m.uploadError?"Too large":"Not available"})]}):e.jsx("span",{style:{color:"#999",fontSize:"12px",fontStyle:"italic"},children:"No file"})}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:f(m.status)}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>D(m),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:m.adminRemarks?1:.6},title:m.adminRemarks?"View admin remarks":"No remarks available",children:[e.jsx(xe,{size:14}),m.adminRemarks?"View Remarks":"No Remarks"]})})]},m.id))})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(he,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No progress reports submitted yet"})]})]}),e.jsx(Fe,{isOpen:j,onClose:()=>y(!1),title:b.title,content:b.content,type:b.type,employeeName:b.employeeName,date:b.date,time:b.time})]})},Wo=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState(!1),[i,r]=x.useState({startDate:"",endDate:"",reason:""}),[l,u]=x.useState(!1);x.useEffect(()=>{d()},[t]);const d=async()=>{try{const w=await pt.getUserRequests(t.uid);n(w)}catch(w){console.error("Error loading holiday requests:",w)}},j=w=>{r({...i,[w.target.name]:w.target.value})},y=()=>{if(i.startDate&&i.endDate){const w=new Date(i.startDate),v=new Date(i.endDate),g=un(v,w)+1;return g>0?g:0}return 0},b=async w=>{w.preventDefault(),u(!0);try{const v=y();if(v<=0){R.error("End date must be after start date");return}const g={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,startDate:i.startDate,endDate:i.endDate,days:v,reason:i.reason,status:"pending",adminRemarks:"",requestDate:W(new Date,"yyyy-MM-dd")};await pt.createRequest(g);const E=(await z.getAll(L.USERS)).filter(D=>D.role==="admin");for(const D of E)await zo(D.uid,t.name,i.startDate,i.endDate);R.success("Holiday request submitted successfully!"),r({startDate:"",endDate:"",reason:""}),o(!1),d()}catch(v){console.error("Error submitting holiday request:",v),R.error("Failed to submit holiday request")}finally{u(!1)}},p=w=>{const v={pending:"status-pending",approved:"status-approved",rejected:"status-rejected"};return e.jsx("span",{className:`status-badge ${v[w]||"status-pending"}`,children:w.charAt(0).toUpperCase()+w.slice(1)})};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Your Holiday Requests"}),e.jsxs("button",{onClick:()=>o(!a),className:"btn btn-primary",children:[e.jsx(ge,{size:16}),a?"Cancel":"Request Holiday"]})]}),a&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"New Holiday Request"}),e.jsxs("form",{onSubmit:b,children:[e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Start Date"}),e.jsx("input",{type:"date",name:"startDate",value:i.startDate,onChange:j,className:"form-input",required:!0,min:W(new Date,"yyyy-MM-dd")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"End Date"}),e.jsx("input",{type:"date",name:"endDate",value:i.endDate,onChange:j,className:"form-input",required:!0,min:i.startDate||W(new Date,"yyyy-MM-dd")})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Reason for Holiday"}),e.jsx("textarea",{name:"reason",value:i.reason,onChange:j,className:"form-textarea",placeholder:"Please provide the reason for your holiday request...",required:!0})]}),e.jsxs("div",{className:"flex justify-between align-center",children:[e.jsx("div",{style:{color:"#666"},children:i.startDate&&i.endDate&&e.jsxs("span",{children:["Total Days: ",e.jsx("strong",{children:y()})]})}),e.jsx("button",{type:"submit",className:"btn btn-success",disabled:l||y()<=0,children:l?"Submitting...":"Submit Request"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Request History"}),s.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Start Date"}),e.jsx("th",{children:"End Date"}),e.jsx("th",{children:"Days"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Admin Remarks"}),e.jsx("th",{children:"Request Date"})]})}),e.jsx("tbody",{children:s.map(w=>e.jsxs("tr",{children:[e.jsx("td",{children:w.startDate}),e.jsx("td",{children:w.endDate}),e.jsx("td",{children:e.jsx("span",{style:{fontWeight:"bold",color:"#3498db",padding:"2px 8px",background:"#e3f2fd",borderRadius:"12px",fontSize:"12px"},children:w.days})}),e.jsx("td",{style:{maxWidth:"200px"},children:w.reason.length>50?`${w.reason.substring(0,50)}...`:w.reason}),e.jsx("td",{children:p(w.status)}),e.jsx("td",{style:{maxWidth:"150px"},children:w.adminRemarks||e.jsx("span",{style:{color:"#999",fontStyle:"italic"},children:"Pending review"})}),e.jsx("td",{children:w.requestDate})]},w.id))})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(se,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No holiday requests submitted yet"})]})]})]})},$o=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState([]),[i,r]=x.useState({status:"all",date:"all"}),[l,u]=x.useState(null),[d,j]=x.useState(!1),[y,b]=x.useState({status:"",reason:""}),[p,w]=x.useState(!1),[v,g]=x.useState(!1),[f,E]=x.useState({});x.useEffect(()=>{D()},[t]),x.useEffect(()=>{m()},[s,i]);const D=async()=>{try{const h=await Te.getUserTasks(t.uid);n(h)}catch(h){console.error("Error loading tasks:",h)}},m=()=>{let h=[...s];i.status!=="all"&&(h=h.filter(F=>F.status===i.status));const c=W(new Date,"yyyy-MM-dd"),I=new Date,A=new Date(I.setDate(I.getDate()-I.getDay())),B=new Date(I.getFullYear(),I.getMonth(),1);switch(i.date){case"today":h=h.filter(F=>F.date===c);break;case"week":h=h.filter(F=>new Date(F.date)>=A);break;case"month":h=h.filter(F=>new Date(F.date)>=B);break}o(h)},N=(h,c)=>{r(I=>({...I,[h]:c}))},k=h=>{u(h),b({status:h.status,reason:h.reason||""}),j(!0)},O=async()=>{if(y.status==="not-achieved"&&!y.reason.trim()){R.error("Please provide a reason for not completing the task");return}w(!0);try{await Te.updateTaskStatus(l.id,y.status,y.reason,y.reason),y.status==="completed"?await Oo(l.assignedBy,l.description,t.name):await Mo(l.assignedBy,l.description,t.name,y.status),R.success("Task updated successfully!"),j(!1),u(null),b({status:"",reason:""}),D()}catch(h){console.error("Error updating task:",h),R.error("Failed to update task")}finally{w(!1)}},S=h=>{const c={pending:{class:"status-pending",icon:V,text:"Pending"},completed:{class:"status-approved",icon:te,text:"Completed"},"not-achieved":{class:"status-rejected",icon:re,text:"Not Achieved"}},I=c[h]||c.pending,A=I.icon;return e.jsxs("span",{className:`status-badge ${I.class}`,children:[e.jsx(A,{size:12,style:{marginRight:"4px"}}),I.text]})},_=h=>{switch(h){case"high":return"#e74c3c";case"medium":return"#f39c12";case"low":return"#27ae60";default:return"#95a5a6"}},P=h=>{E({title:"Task Description",content:`${h.description}

${h.details||""}`.trim(),type:"details",employeeName:`Task for ${t.name}`,date:h.date,time:h.day}),g(!0)},T=h=>{E({title:"Task Reason/Comments",content:h.reason,type:"remarks",employeeName:`Task for ${t.name}`,date:h.date,time:h.day}),g(!0)};return e.jsxs("div",{className:"content",children:[e.jsx("h2",{style:{marginBottom:"30px",color:"#333"},children:"My Tasks"}),e.jsxs("div",{className:"card mb-20",children:[e.jsxs("div",{className:"flex align-center gap-10 mb-20",children:[e.jsx(dt,{size:20,style:{color:"#666"}}),e.jsx("h3",{style:{margin:0,color:"#333"},children:"Filters"})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px"},children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:i.status,onChange:h=>N("status",h.target.value),className:"form-select",children:[e.jsx("option",{value:"all",children:"All Tasks"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"not-achieved",children:"Not Achieved"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Date Range"}),e.jsxs("select",{value:i.date,onChange:h=>N("date",h.target.value),className:"form-select",children:[e.jsx("option",{value:"all",children:"All Dates"}),e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"})]})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Tasks (",a.length,")"]}),a.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Day"}),e.jsx("th",{children:"Description"}),e.jsx("th",{children:"Priority"}),e.jsx("th",{children:"File"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:a.map(h=>{var c;return e.jsxs("tr",{children:[e.jsx("td",{children:h.date}),e.jsx("td",{children:h.day}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>P(h),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task description and details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:_(h.priority)},children:((c=h.priority)==null?void 0:c.toUpperCase())||"NORMAL"})}),e.jsx("td",{children:h.fileName?e.jsxs("span",{style:{fontSize:"12px",color:"#3498db"},children:["📎 ",h.fileName]}):e.jsx("span",{style:{color:"#999"},children:"No file"})}),e.jsx("td",{children:S(h.status)}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>T(h),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:h.reason?1:.6},title:h.reason?"View reason/comments":"No reason provided",children:[e.jsx(xe,{size:14}),h.reason?"View Reason":"No Reason"]})}),e.jsx("td",{children:e.jsx("button",{onClick:()=>k(h),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},children:"Update"})})]},h.id)})})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(ke,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No tasks found matching the current filters"})]})]}),d&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0,0,0,0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:e.jsxs("div",{style:{background:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"500px"},children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Update Task Status"}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Task Description"}),e.jsx("div",{style:{padding:"10px",background:"#f8f9fa",borderRadius:"4px",marginBottom:"15px"},children:l==null?void 0:l.description})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:y.status,onChange:h=>b(c=>({...c,status:h.target.value})),className:"form-select",children:[e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"not-achieved",children:"Not Achieved"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{className:"form-label",children:[y.status==="not-achieved"?"Reason for Not Completing (Required)":"Reason/Comments",y.status==="not-achieved"&&e.jsx("span",{style:{color:"#e74c3c"},children:" *"})]}),e.jsx("textarea",{value:y.reason,onChange:h=>b(c=>({...c,reason:h.target.value})),className:"form-textarea",placeholder:y.status==="not-achieved"?"Please explain why the task could not be completed...":"Provide reason for status change or additional comments...",required:y.status==="not-achieved",style:{borderColor:y.status==="not-achieved"&&!y.reason.trim()?"#e74c3c":void 0}}),y.status==="not-achieved"&&!y.reason.trim()&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:"Reason is required when marking task as not achieved"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("button",{onClick:()=>j(!1),className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:O,className:"btn btn-primary",disabled:p||y.status==="not-achieved"&&!y.reason.trim(),children:p?"Updating...":"Update Task"})]})]})}),e.jsx(Fe,{isOpen:v,onClose:()=>g(!1),title:f.title,content:f.content,type:f.type,employeeName:f.employeeName,date:f.date,time:f.time})]})},$t=({onNavigate:t})=>{const{userProfile:s}=Q(),[n,a]=x.useState({totalEmployees:0,totalAttendance:0,pendingLateCheckins:0,pendingReports:0,pendingHolidays:0,totalTasks:0}),[o,i]=x.useState([]),[r,l]=x.useState(!0);x.useEffect(()=>{(s==null?void 0:s.role)==="admin"&&(u(),d())},[s]);const u=async()=>{try{const[p,w,v,g,f]=await Promise.all([z.getAll(L.USERS),z.getAll(L.ATTENDANCE),z.getAll(L.PROGRESS_REPORTS),z.getAll(L.HOLIDAY_REQUESTS),z.getAll(L.TASKS)]),E=p.filter(N=>N.role==="employee"),D=v.filter(N=>N.status==="submitted"),m=g.filter(N=>N.status==="pending");a({totalEmployees:E.length,totalAttendance:w.length,pendingLateCheckins:0,pendingReports:D.length,pendingHolidays:m.length,totalTasks:f.length})}catch(p){console.error("Error loading admin stats:",p)}finally{l(!1)}},d=async()=>{try{const[p,w]=await Promise.all([z.getAll(L.PROGRESS_REPORTS),z.getAll(L.HOLIDAY_REQUESTS)]),v=[...p.map(g=>({...g,type:"progress-report",title:"Progress Report",description:`${g.employeeName} submitted progress report`})),...w.map(g=>({...g,type:"holiday-request",title:"Holiday Request",description:`${g.employeeName} requested ${g.days} days leave`}))].sort((g,f)=>{var E,D;return new Date(((E=f.createdAt)==null?void 0:E.seconds)*1e3)-new Date(((D=g.createdAt)==null?void 0:D.seconds)*1e3)}).slice(0,10);i(v)}catch(p){console.error("Error loading recent activity:",p)}},j=({icon:p,title:w,value:v,color:g="#3498db",trend:f})=>e.jsxs("div",{className:"stat-card",children:[e.jsxs("div",{className:"flex justify-between align-center mb-10",children:[e.jsx(p,{size:32,style:{color:g}}),f&&e.jsx(hn,{size:16,style:{color:"#27ae60"}})]}),e.jsx("div",{className:"stat-number",style:{color:g},children:r?"...":v}),e.jsx("div",{className:"stat-label",children:w})]}),y=p=>{switch(p){case"progress-report":return e.jsx(he,{size:16,style:{color:"#3498db"}});case"holiday-request":return e.jsx(se,{size:16,style:{color:"#9b59b6"}});default:return e.jsx(V,{size:16,style:{color:"#95a5a6"}})}},b=p=>{switch(p){case"pending":return"#f39c12";case"approved":return"#27ae60";case"rejected":return"#e74c3c";default:return"#95a5a6"}};return(s==null?void 0:s.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx(XCircle,{size:64,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{style:{color:"#e74c3c"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to access the admin panel."})]})}):e.jsxs("div",{className:"content",children:[e.jsx("h2",{style:{marginBottom:"30px",color:"#333"},children:"Admin Dashboard"}),e.jsxs("div",{className:"stats-grid",children:[e.jsx(j,{icon:ue,title:"Total Employees",value:n.totalEmployees,color:"#27ae60"}),e.jsx(j,{icon:V,title:"Total Attendance Records",value:n.totalAttendance,color:"#3498db"}),e.jsx(j,{icon:Ae,title:"Pending Late Check-ins",value:n.pendingLateCheckins,color:"#f39c12"}),e.jsx(j,{icon:he,title:"Pending Reports",value:n.pendingReports,color:"#9b59b6"}),e.jsx(j,{icon:se,title:"Pending Holidays",value:n.pendingHolidays,color:"#e74c3c"}),e.jsx(j,{icon:ke,title:"Total Tasks",value:n.totalTasks,color:"#34495e"})]}),e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Quick Actions"}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px"},children:[e.jsxs("button",{className:"btn btn-primary",style:{padding:"15px"},onClick:()=>t&&t("employees"),children:[e.jsx(ue,{size:18,style:{marginRight:"8px"}}),"Manage Employees"]}),e.jsxs("button",{className:"btn btn-info",style:{padding:"15px"},onClick:()=>t&&t("admin-timetable"),children:[e.jsx(se,{size:18,style:{marginRight:"8px"}}),"Manage Time Tables"]}),e.jsxs("button",{className:"btn btn-warning",style:{padding:"15px"},onClick:()=>t&&t("late-records"),children:[e.jsx(Ae,{size:18,style:{marginRight:"8px"}}),"Review Late Check-ins"]}),e.jsxs("button",{className:"btn btn-success",style:{padding:"15px"},onClick:()=>t&&t("holiday-management"),children:[e.jsx(se,{size:18,style:{marginRight:"8px"}}),"Approve Holidays"]}),e.jsxs("button",{className:"btn btn-info",style:{padding:"15px"},onClick:()=>t&&t("admin-progress-reports"),children:[e.jsx(he,{size:18,style:{marginRight:"8px"}}),"Progress Reports"]}),e.jsxs("button",{className:"btn btn-secondary",style:{padding:"15px"},onClick:()=>t&&t("task-management"),children:[e.jsx(ke,{size:18,style:{marginRight:"8px"}}),"Assign Tasks"]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Recent Activity"}),o.length>0?e.jsx("div",{style:{maxHeight:"400px",overflowY:"auto"},children:o.map((p,w)=>{var v;return e.jsxs("div",{style:{padding:"15px",borderBottom:"1px solid #eee",display:"flex",alignItems:"center",gap:"15px"},children:[e.jsx("div",{children:y(p.type)}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:"500",marginBottom:"4px"},children:p.title}),e.jsx("div",{style:{fontSize:"14px",color:"#666"},children:p.description})]}),e.jsxs("div",{style:{textAlign:"right"},children:[e.jsx("div",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:b(p.status),marginBottom:"4px"},children:(v=p.status)==null?void 0:v.toUpperCase()}),e.jsx("div",{style:{fontSize:"12px",color:"#999"},children:p.createdAt?new Date(p.createdAt.seconds*1e3).toLocaleDateString():"Recently"})]})]},w)})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No recent activity"})]})]})]})},qo=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState(!1),[i,r]=x.useState(!1),[l,u]=x.useState(""),[d,j]=x.useState({email:"",password:"",name:"",employeeId:"",role:"employee",isActive:!0}),[y,b]=x.useState(!1),[p,w]=x.useState({}),[v,g]=x.useState(null),[f,E]=x.useState({});x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&D()},[t]);const D=async()=>{try{const c=await z.getAll(L.USERS);n(c)}catch(c){console.error("Error loading employees:",c),R.error("Failed to load employees")}},m=c=>{j({...d,[c.target.name]:c.target.value}),p[c.target.name]&&w({...p,[c.target.name]:""})},N=()=>{const c={};return d.email?/\S+@\S+\.\S+/.test(d.email)||(c.email="Email is invalid"):c.email="Email is required",d.password?d.password.length<6&&(c.password="Password must be at least 6 characters"):c.password="Password is required",d.name||(c.name="Name is required"),d.employeeId||(c.employeeId="Employee ID is required"),w(c),Object.keys(c).length===0},k=async c=>{if(c.preventDefault(),!!N()){r(!0);try{console.log("Creating Firebase Auth user with secondary auth...");const A=(await Ct(Me,d.email,d.password)).user;console.log("Firebase Auth user created:",A.uid);const B={uid:A.uid,email:d.email,name:d.name,employeeId:d.employeeId,role:d.role,isActive:d.isActive,createdAt:new Date().toISOString(),createdBy:t.uid};console.log("Creating Firestore profile..."),await je.createUser(B),console.log("Firestore profile created successfully"),console.log("Signing out from secondary auth..."),await Me.signOut(),g({id:A.uid,name:d.name,email:d.email,password:d.password,employeeId:d.employeeId,role:d.role}),R.success(`Employee ${d.name} added successfully! They can now login with their credentials.`),j({email:"",password:"",name:"",employeeId:"",role:"employee",isActive:!0}),o(!1),D()}catch(I){console.error("Error adding employee:",I);let A="Failed to add employee";switch(I.code){case"auth/email-already-in-use":A="Email is already registered";break;case"auth/weak-password":A="Password must be at least 6 characters";break;case"auth/invalid-email":A="Invalid email address";break;default:A=I.message}R.error(A),w({submit:A})}finally{r(!1)}}},O=async(c,I)=>{try{await z.update(L.USERS,c,{isActive:!I}),R.success(`Employee ${I?"deactivated":"activated"} successfully`),D()}catch(A){console.error("Error updating employee status:",A),R.error("Failed to update employee status")}},S=async c=>{if(!c.password){R.error("Cannot migrate: Password not available. Please create a new account.");return}E(I=>({...I,[c.id]:"migrating"}));try{const A=(await Ct(Me,c.email,c.password)).user;await z.update(L.USERS,c.id,{uid:A.uid,migratedAt:new Date().toISOString(),migratedBy:t.uid});const B={...c,uid:A.uid,migratedAt:new Date().toISOString(),migratedBy:t.uid};await je.createUser(B),await Me.signOut(),E(F=>({...F,[c.id]:"success"})),R.success(`Employee ${c.name} migrated successfully!`),D()}catch(I){console.error("Migration error:",I),E(B=>({...B,[c.id]:"failed"}));let A="Migration failed";I.code==="auth/email-already-in-use"&&(A="Email already has a Firebase account"),R.error(A)}},_=c=>!c.uid||c.uid.startsWith("emp_"),P=c=>{if(v&&v.id===c.id){T(v);return}const I={name:c.name||"Employee",email:c.email,password:"[Contact Admin for Password]",employeeId:c.employeeId||"N/A",role:c.role||"employee"};T(I)},T=c=>{const I=encodeURIComponent("Your Employee Login Credentials - Employee Management System"),A=encodeURIComponent(`Dear ${c.name},

Welcome to the Employee Management System!

Your login credentials:

📧 Email: ${c.email}
🔑 Password: ${c.password}
👤 Employee ID: ${c.employeeId}
🎯 Role: ${c.role.charAt(0).toUpperCase()+c.role.slice(1)}

For security reasons:
1. Please change your password after your first login
2. Keep your credentials secure and do not share them
3. Contact the administrator if you face any login issues

Please use these credentials to log into the system at: ${window.location.origin}

Best regards,
Admin Team
Employee Management System`),B=`mailto:${c.email}?subject=${I}&body=${A}`,F=document.createElement("a");F.href=B,F.target="_blank",F.rel="noopener noreferrer",document.body.appendChild(F),F.click(),document.body.removeChild(F),R.success(`Email client opened with credentials for ${c.name}`)},h=s.filter(c=>{var I,A,B;return((I=c.name)==null?void 0:I.toLowerCase().includes(l.toLowerCase()))||((A=c.email)==null?void 0:A.toLowerCase().includes(l.toLowerCase()))||((B=c.employeeId)==null?void 0:B.toLowerCase().includes(l.toLowerCase()))});return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx(ue,{size:64,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{style:{color:"#e74c3c"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to manage employees."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Employee Management"}),e.jsxs("button",{onClick:()=>o(!a),className:"btn btn-primary",children:[e.jsx(ge,{size:16}),a?"Cancel":"Add Employee"]})]}),a&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Add New Employee"}),e.jsxs("form",{onSubmit:k,children:[e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Full Name *"}),e.jsx("input",{type:"text",name:"name",value:d.name,onChange:m,className:"form-input",placeholder:"Enter full name"}),p.name&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.name})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Employee ID *"}),e.jsx("input",{type:"text",name:"employeeId",value:d.employeeId,onChange:m,className:"form-input",placeholder:"e.g., EMP001"}),p.employeeId&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.employeeId})]})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Email Address *"}),e.jsx("input",{type:"email",name:"email",value:d.email,onChange:m,className:"form-input",placeholder:"Enter email address"}),p.email&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.email})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Password *"}),e.jsxs("div",{style:{position:"relative"},children:[e.jsx("input",{type:y?"text":"password",name:"password",value:d.password,onChange:m,className:"form-input",placeholder:"Enter password",style:{paddingRight:"40px"}}),e.jsx("button",{type:"button",onClick:()=>b(!y),style:{position:"absolute",right:"10px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#666"},children:y?e.jsx(Zt,{size:16}):e.jsx(fe,{size:16})})]}),p.password&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.password})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Role"}),e.jsxs("select",{name:"role",value:d.role,onChange:m,className:"form-select",style:{maxWidth:"200px"},children:[e.jsx("option",{value:"employee",children:"Employee"}),e.jsx("option",{value:"admin",children:"Administrator"})]})]}),p.submit&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"14px",marginBottom:"15px",padding:"10px",background:"#fdf2f2",border:"1px solid #fecaca",borderRadius:"4px"},children:p.submit}),e.jsxs("div",{className:"flex justify-between align-center",children:[e.jsx("div",{style:{color:"#666",fontSize:"14px"},children:"* Required fields"}),e.jsx("button",{type:"submit",className:"btn btn-success",disabled:i,children:i?"Adding Employee...":"Add Employee"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsxs("h3",{style:{color:"#333"},children:["All Employees (",h.length,")"]}),e.jsxs("div",{style:{position:"relative",width:"300px"},children:[e.jsx(Qe,{size:16,style:{position:"absolute",left:"10px",top:"50%",transform:"translateY(-50%)",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search employees...",value:l,onChange:c=>u(c.target.value),className:"form-input",style:{paddingLeft:"35px"}})]})]}),h.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Employee ID"}),e.jsx("th",{children:"Name"}),e.jsx("th",{children:"Email"}),e.jsx("th",{children:"Role"}),e.jsx("th",{children:"Account Type"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:h.map(c=>{var I;return e.jsxs("tr",{children:[e.jsx("td",{style:{fontWeight:"bold",color:"#3498db"},children:c.employeeId||"N/A"}),e.jsx("td",{children:c.name||"N/A"}),e.jsx("td",{children:c.email}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:c.role==="admin"?"#e74c3c":"#3498db"},children:((I=c.role)==null?void 0:I.toUpperCase())||"EMPLOYEE"})}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:_(c)?"#f39c12":"#27ae60"},children:_(c)?"LEGACY":"FIREBASE"})}),e.jsx("td",{children:e.jsx("span",{className:`status-badge ${c.isActive!==!1?"status-approved":"status-rejected"}`,children:c.isActive!==!1?"Active":"Inactive"})}),e.jsx("td",{children:e.jsxs("div",{className:"flex gap-10",style:{flexWrap:"wrap"},children:[e.jsx("button",{onClick:()=>P(c),className:"btn btn-info",style:{padding:"5px 10px",fontSize:"12px"},title:"Send Credentials via Email",children:e.jsx(pn,{size:14})}),_(c)&&e.jsx("button",{onClick:()=>S(c),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},title:"Migrate to Firebase Auth",disabled:f[c.id]==="migrating",children:f[c.id]==="migrating"?"...":"🔄"}),e.jsx("button",{onClick:()=>O(c.id,c.isActive),className:`btn ${c.isActive!==!1?"btn-warning":"btn-success"}`,style:{padding:"5px 10px",fontSize:"12px"},title:c.isActive!==!1?"Deactivate":"Activate",children:c.isActive!==!1?e.jsx(mn,{size:14}):e.jsx(xn,{size:14})})]})})]},c.id)})})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(ue,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No employees found"})]})]})]})},Vo=()=>{var k,O;const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState(""),[i,r]=x.useState({}),[l,u]=x.useState(!1),[d,j]=x.useState(!1),[y,b]=x.useState(!1),p=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&w()},[t]),x.useEffect(()=>{a?v():(r({}),j(!1))},[a]);const w=async()=>{try{u(!0);const _=(await z.getAll(L.USERS)).filter(P=>P.role==="employee");n(_)}catch(S){console.error("Error loading employees:",S),R.error("Failed to load employees")}finally{u(!1)}},v=async()=>{try{u(!0);const S=await z.getWhere(L.TIME_TABLES,"userId","==",a);if(S.length>0)r(S[0].schedule||{});else{const _={};p.forEach(P=>{_[P]=[]}),r(_)}}catch(S){console.error("Error loading employee timetable:",S),R.error("Failed to load employee timetable")}finally{u(!1)}},g=S=>{r(_=>({..._,[S]:[..._[S]||[],{checkIn:"",checkOut:""}]}))},f=(S,_)=>{r(P=>({...P,[S]:P[S].filter((T,h)=>h!==_)}))},E=(S,_,P,T)=>{r(h=>({...h,[S]:h[S].map((c,I)=>I===_?{...c,[P]:T}:c)}))},D=S=>{if(!i[S]||i[S].length===0)return"00:00:00";let _=0;i[S].forEach(c=>{if(c.checkIn&&c.checkOut){const I=new Date(`2000-01-01T${c.checkIn}`),A=new Date(`2000-01-01T${c.checkOut}`);if(A>I){const B=A-I;_+=Math.floor(B/1e3)}}});const P=Math.floor(_/3600),T=Math.floor(_%3600/60),h=_%60;return`${P.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}:${h.toString().padStart(2,"0")}`},m=()=>{let S=0;p.forEach(h=>{i[h]&&i[h].length>0&&i[h].forEach(c=>{if(c.checkIn&&c.checkOut){const I=new Date(`2000-01-01T${c.checkIn}`),A=new Date(`2000-01-01T${c.checkOut}`);if(A>I){const B=A-I;S+=Math.floor(B/1e3)}}})});const _=Math.floor(S/3600),P=Math.floor(S%3600/60),T=S%60;return`${_.toString().padStart(2,"0")}:${P.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}`},N=async()=>{if(!a){R.error("Please select an employee first");return}try{b(!0);const S=s.find(_=>_.uid===a);await be.saveAdminTimetable(a,{employeeId:(S==null?void 0:S.employeeId)||"Unknown",name:(S==null?void 0:S.name)||"Unknown"},i,t.uid),R.success("Time table saved successfully! Employee has been notified."),j(!1)}catch(S){console.error("Error saving timetable:",S),R.error("Failed to save time table")}finally{b(!1)}};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[e.jsx(ae,{size:48,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsxs("div",{children:[e.jsx("h2",{style:{margin:"0 0 5px 0",color:"#333"},children:"Employee Time Table Management"}),e.jsxs("p",{style:{color:"#666",fontSize:"14px",margin:0},children:["Current Week: ",be.getCurrentWeekId()," | Admin can modify any timetable"]})]}),a&&e.jsx("div",{style:{display:"flex",gap:"10px"},children:d?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>j(!1),className:"btn btn-secondary",disabled:y,children:[e.jsx(re,{size:16,style:{marginRight:"5px"}}),"Cancel"]}),e.jsxs("button",{onClick:N,className:"btn btn-success",disabled:y,children:[e.jsx(gn,{size:16,style:{marginRight:"5px"}}),y?"Saving...":"Save Changes"]})]}):e.jsxs("button",{onClick:()=>j(!0),className:"btn btn-primary",disabled:l,children:[e.jsx(es,{size:16,style:{marginRight:"5px"}}),"Edit Time Table"]})})]}),e.jsxs("div",{className:"card",style:{marginBottom:"20px"},children:[e.jsx("h3",{style:{marginBottom:"15px",color:"#333"},children:"Select Employee"}),e.jsxs("div",{style:{display:"flex",gap:"20px",alignItems:"end"},children:[e.jsxs("div",{style:{minWidth:"300px"},children:[e.jsx("label",{style:{display:"block",marginBottom:"5px",fontWeight:"bold"},children:"Employee"}),e.jsxs("select",{value:a,onChange:S=>o(S.target.value),className:"form-select",style:{width:"100%"},disabled:l,children:[e.jsx("option",{value:"",children:"Select an employee..."}),s.map(S=>e.jsxs("option",{value:S.uid,children:[S.name," (",S.employeeId,")"]},S.uid))]})]}),a&&e.jsxs("div",{style:{padding:"10px 15px",backgroundColor:"#e8f5e8",borderRadius:"5px",border:"1px solid #c3e6c3"},children:[e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:"Total Weekly Hours"}),e.jsx("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#27ae60",fontFamily:"monospace"},children:m()})]})]})]}),!a&&e.jsxs("div",{className:"card",style:{textAlign:"center",padding:"50px"},children:[e.jsx(ue,{size:48,style:{color:"#3498db",marginBottom:"20px",opacity:.5}}),e.jsx("h3",{style:{color:"#666",marginBottom:"10px"},children:"No Employee Selected"}),e.jsx("p",{style:{color:"#999",marginBottom:"0"},children:"Please select an employee from the dropdown above to view and manage their time table."})]}),a&&e.jsx("div",{className:"card",children:l?e.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[e.jsx(V,{size:48,style:{color:"#3498db",marginBottom:"20px"}}),e.jsx("h3",{children:"Loading Time Table"}),e.jsx("p",{children:"Loading employee time table..."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f8f9fa",borderRadius:"5px"},children:[e.jsxs("h3",{style:{margin:"0 0 5px 0",color:"#333"},children:["Time Table for ",(k=s.find(S=>S.uid===a))==null?void 0:k.name]}),e.jsxs("p",{style:{margin:0,color:"#666",fontSize:"14px"},children:["Employee ID: ",(O=s.find(S=>S.uid===a))==null?void 0:O.employeeId]})]}),p.map(S=>e.jsxs("div",{style:{marginBottom:"30px",paddingBottom:"20px",borderBottom:"1px solid #eee"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[e.jsx("h4",{style:{color:"#333",margin:0},children:S}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px"},children:[e.jsxs("span",{style:{color:"#666",fontSize:"14px",fontFamily:"monospace"},children:["Total: ",D(S)]}),d&&e.jsxs("button",{onClick:()=>g(S),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},children:[e.jsx(ge,{size:14,style:{marginRight:"3px"}}),"Add Slot"]})]})]}),i[S]&&i[S].length>0?e.jsx("div",{style:{display:"grid",gap:"10px"},children:i[S].map((_,P)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px",padding:"15px",backgroundColor:d?"#fff3cd":"#f8f9fa",borderRadius:"6px",border:d?"1px solid #ffeaa7":"1px solid #e9ecef"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px",flex:1},children:[e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666",display:"block",marginBottom:"3px"},children:"Check In"}),e.jsx("input",{type:"time",value:_.checkIn,onChange:T=>E(S,P,"checkIn",T.target.value),className:"form-input",style:{width:"130px"},disabled:!d})]}),e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666",display:"block",marginBottom:"3px"},children:"Check Out"}),e.jsx("input",{type:"time",value:_.checkOut,onChange:T=>E(S,P,"checkOut",T.target.value),className:"form-input",style:{width:"130px"},disabled:!d})]}),_.checkIn&&_.checkOut&&e.jsxs("div",{style:{padding:"8px 12px",backgroundColor:"#e8f5e8",borderRadius:"4px"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#666"},children:"Duration"}),e.jsx("div",{style:{fontSize:"13px",fontWeight:"bold",color:"#27ae60",fontFamily:"monospace"},children:(()=>{const T=new Date(`2000-01-01T${_.checkIn}`),h=new Date(`2000-01-01T${_.checkOut}`);if(h>T){const c=h-T,I=Math.floor(c/1e3),A=Math.floor(I/3600),B=Math.floor(I%3600/60),F=I%60;return`${A.toString().padStart(2,"0")}:${B.toString().padStart(2,"0")}:${F.toString().padStart(2,"0")}`}return"00:00:00"})()})]})]}),d&&e.jsx("button",{onClick:()=>f(S,P),className:"btn btn-danger",style:{padding:"8px",minWidth:"auto"},children:e.jsx(De,{size:16})})]},P))}):e.jsxs("div",{style:{textAlign:"center",padding:"30px",color:"#666",backgroundColor:"#f8f9fa",borderRadius:"6px",border:"2px dashed #dee2e6"},children:[e.jsx(se,{size:32,style:{opacity:.3,marginBottom:"10px"}}),e.jsxs("p",{style:{fontSize:"14px",margin:"0 0 10px 0"},children:["No time slots set for ",S]}),d&&e.jsxs("button",{onClick:()=>g(S),className:"btn btn-primary",style:{padding:"8px 15px",fontSize:"12px"},children:[e.jsx(ge,{size:14,style:{marginRight:"5px"}}),"Add First Time Slot"]})]})]},S))]})})]})},Yo="modulepreload",Ko=function(t){return"/"+t},qt={},Vt=function(s,n,a){let o=Promise.resolve();if(n&&n.length>0){document.getElementsByTagName("link");const r=document.querySelector("meta[property=csp-nonce]"),l=(r==null?void 0:r.nonce)||(r==null?void 0:r.getAttribute("nonce"));o=Promise.allSettled(n.map(u=>{if(u=Ko(u),u in qt)return;qt[u]=!0;const d=u.endsWith(".css"),j=d?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${u}"]${j}`))return;const y=document.createElement("link");if(y.rel=d?"stylesheet":Yo,d||(y.as="script"),y.crossOrigin="",y.href=u,l&&y.setAttribute("nonce",l),document.head.appendChild(y),d)return new Promise((b,p)=>{y.addEventListener("load",b),y.addEventListener("error",()=>p(new Error(`Unable to preload CSS for ${u}`)))})}))}function i(r){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=r,window.dispatchEvent(l),!l.defaultPrevented)throw r}return o.then(r=>{for(const l of r||[])l.status==="rejected"&&i(l.reason);return s().catch(i)})},Go=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState(!1),[i,r]=x.useState(""),[l,u]=x.useState("all"),[d,j]=x.useState(null),[y,b]=x.useState(""),[p,w]=x.useState(!1),[v,g]=x.useState(!1),[f,E]=x.useState({});x.useEffect(()=>{t&&(console.log("AdminProgressReports - User Profile:",t),console.log("AdminProgressReports - User Role:",t.role),t.role==="admin"?D():console.log("User is not admin, skipping report loading"))},[t]);const D=async()=>{o(!0);try{console.log("Admin loading progress reports..."),console.log("Current user profile:",t),console.log("User role:",t==null?void 0:t.role),console.log("User UID:",t==null?void 0:t.uid);let T=[];try{console.log("Attempting to fetch with default ordering..."),T=await z.getAll(L.PROGRESS_REPORTS),console.log("Success with default ordering. Fetched reports:",T.length)}catch{console.log("Default ordering failed, trying without ordering...");try{const{collection:I,getDocs:A}=await Vt(async()=>{const{collection:H,getDocs:K}=await import("./firebase-BfSkx5kM.js").then(Z=>Z.K);return{collection:H,getDocs:K}},[]),{db:B}=await Vt(async()=>{const{db:H}=await Promise.resolve().then(()=>vo);return{db:H}},void 0);T=(await A(I(B,L.PROGRESS_REPORTS))).docs.map(H=>({id:H.id,...H.data()})),console.log("Success without ordering. Fetched reports:",T.length)}catch(I){throw console.error("Simple query also failed:",I),I}}console.log("Raw fetched reports:",T);const h=T.sort((c,I)=>{try{const A=new Date(`${c.date} ${c.time}`);return new Date(`${I.date} ${I.time}`)-A}catch(A){return console.warn("Error sorting reports:",A),0}});n(h),h.length===0?(console.log("No progress reports found"),R.info("No progress reports found. Try submitting a test report first.")):(console.log(`Successfully loaded ${h.length} progress reports`),R.success(`Loaded ${h.length} progress reports`))}catch(T){console.error("Error loading progress reports:",T),console.error("Error details:",T.message),console.error("Error code:",T.code),T.message.includes("permission")||T.message.includes("insufficient")||T.code==="permission-denied"?(R.error("Permission denied. Firebase rules may need updating."),console.log("Permission denied - check Firebase rules for admin access")):T.message.includes("index")||T.code==="failed-precondition"?(R.error("Database index missing. Loading without sorting..."),console.log("Index error - trying alternative query method")):R.error("Failed to load progress reports: "+T.message)}finally{o(!1)}},m=T=>{j(T),b(T.adminRemarks||""),w(!0)},N=async()=>{if(d)try{const T={adminRemarks:y,status:"reviewed",reviewedBy:t.uid,reviewedAt:new Date().toISOString()};await z.update(L.PROGRESS_REPORTS,d.id,T),R.success("Remarks added successfully!"),w(!1),j(null),b(""),D()}catch(T){console.error("Error saving remarks:",T),R.error("Failed to save remarks")}},k=T=>{E({title:"Tasks Completed",content:T.tasksCompleted,type:"details",employeeName:T.employeeName,date:T.date,time:T.time}),g(!0)},O=s.filter(T=>{var I,A,B;const h=((I=T.employeeName)==null?void 0:I.toLowerCase().includes(i.toLowerCase()))||((A=T.employeeId)==null?void 0:A.toLowerCase().includes(i.toLowerCase()))||((B=T.tasksCompleted)==null?void 0:B.toLowerCase().includes(i.toLowerCase())),c=l==="all"||T.status===l;return h&&c}),S=T=>{const h={submitted:{class:"status-pending",icon:V,text:"Submitted"},reviewed:{class:"status-approved",icon:te,text:"Reviewed"},rejected:{class:"status-rejected",icon:Ae,text:"Rejected"}},c=h[T]||h.submitted,I=c.icon;return e.jsxs("span",{className:`status-badge ${c.class}`,style:{display:"flex",alignItems:"center",gap:"5px"},children:[e.jsx(I,{size:12}),c.text]})},P={total:s.length,submitted:s.filter(h=>h.status==="submitted").length,reviewed:s.filter(h=>h.status==="reviewed").length,rejected:s.filter(h=>h.status==="rejected").length};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card",children:[e.jsx("h2",{style:{color:"#e74c3c",marginBottom:"10px"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Employee Progress Reports"}),e.jsx("button",{onClick:D,className:"btn btn-primary",disabled:a,children:a?"Loading...":"Refresh"})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px",marginBottom:"20px"},children:[e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#3498db",margin:"0 0 5px 0"},children:P.total}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Total Reports"})]}),e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#f39c12",margin:"0 0 5px 0"},children:P.submitted}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Pending Review"})]}),e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#27ae60",margin:"0 0 5px 0"},children:P.reviewed}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Reviewed"})]}),e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#e74c3c",margin:"0 0 5px 0"},children:P.rejected}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Rejected"})]})]}),e.jsx("div",{className:"card mb-20",children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr auto",gap:"20px",alignItems:"center"},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Qe,{size:16,style:{position:"absolute",left:"10px",top:"50%",transform:"translateY(-50%)",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search by employee name, ID, or tasks...",value:i,onChange:T=>r(T.target.value),className:"form-input",style:{paddingLeft:"35px"}})]}),e.jsxs("select",{value:l,onChange:T=>u(T.target.value),className:"form-select",style:{minWidth:"150px"},children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"submitted",children:"Pending Review"}),e.jsx("option",{value:"reviewed",children:"Reviewed"}),e.jsx("option",{value:"rejected",children:"Rejected"})]})]})}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Progress Reports (",O.length,")"]}),O.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Date"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Employee"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Tasks Completed"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"File"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Actions"})]})}),e.jsx("tbody",{children:O.map(T=>e.jsxs("tr",{children:[e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(se,{size:14,style:{color:"#666"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px"},children:W(new Date(T.date),"MMM dd")}),e.jsx("div",{style:{fontSize:"11px",color:"#666"},children:T.time})]})]})}),e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(gt,{size:14,style:{color:"#3498db"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px",maxWidth:"120px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:T.employeeName}),e.jsxs("div",{style:{fontSize:"11px",color:"#666"},children:["ID: ",T.employeeId]})]})]})}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:e.jsxs("button",{onClick:()=>k(T),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{style:{padding:"12px 8px",textAlign:"center"},children:T.fileName?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"6px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"6px",marginBottom:"4px"},children:[e.jsx(he,{size:16,style:{color:"#3498db"}}),e.jsxs("div",{style:{textAlign:"left"},children:[e.jsx("div",{style:{fontSize:"12px",fontWeight:"bold",maxWidth:"120px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:T.fileName}),e.jsx("div",{style:{fontSize:"10px",color:"#666"},children:T.fileSize?`${(T.fileSize/1024/1024).toFixed(2)} MB`:""})]})]}),T.fileData?e.jsxs("button",{onClick:()=>{try{console.log("Admin downloading file:",T.fileName),Ye.createDownloadLink(T.fileName,T.fileData),R.success(`Downloaded: ${T.fileName}`)}catch(h){console.error("Admin download error:",h),R.error("Failed to download file: "+h.message)}},style:{background:"#27ae60",color:"white",border:"none",borderRadius:"4px",padding:"6px 12px",fontSize:"11px",cursor:"pointer",display:"flex",alignItems:"center",gap:"4px",transition:"background-color 0.2s"},onMouseOver:h=>h.target.style.backgroundColor="#219a52",onMouseOut:h=>h.target.style.backgroundColor="#27ae60",title:"Download file",children:[e.jsx(ft,{size:12}),"Download"]}):e.jsx("span",{style:{color:"#ccc",fontSize:"11px",fontStyle:"italic"},children:"File not available"})]}):e.jsx("span",{style:{color:"#999",fontSize:"12px",fontStyle:"italic"},children:"No file attached"})}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:S(T.status)}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:e.jsxs("button",{onClick:()=>m(T),className:"btn btn-secondary",style:{padding:"8px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"4px",margin:"0 auto"},title:"Add/Edit Remarks",children:[e.jsx(xe,{size:14}),"Remarks"]})})]},T.id))})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(he,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No progress reports found"})]})]}),p&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0,0,0,0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:e.jsxs("div",{style:{background:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"600px",maxHeight:"80vh",overflow:"auto"},children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Add Remarks - ",d==null?void 0:d.employeeName]}),e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",background:"#f8f9fa",borderRadius:"4px"},children:[e.jsx("h4",{style:{margin:"0 0 10px 0",color:"#555"},children:"Report Summary:"}),e.jsxs("p",{style:{margin:"5px 0",fontSize:"14px"},children:[e.jsx("strong",{children:"Date:"})," ",d==null?void 0:d.date," at ",d==null?void 0:d.time]}),e.jsxs("p",{style:{margin:"5px 0",fontSize:"14px"},children:[e.jsx("strong",{children:"Tasks Completed:"})," ",d==null?void 0:d.tasksCompleted]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Admin Remarks"}),e.jsx("textarea",{value:y,onChange:T=>b(T.target.value),className:"form-textarea",placeholder:"Add your remarks about this progress report...",style:{minHeight:"120px"}})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>w(!1),className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:N,className:"btn btn-success",children:"Save Remarks"})]})]})}),e.jsx(Fe,{isOpen:v,onClose:()=>g(!1),title:f.title,content:f.content,type:f.type,employeeName:f.employeeName,date:f.date,time:f.time})]})},Xo=()=>{const{user:t,userProfile:s}=Q(),[n,a]=x.useState([]),[o,i]=x.useState([]),[r,l]=x.useState(!0),[u,d]=x.useState("all"),[j,y]=x.useState("today"),[b,p]=x.useState(""),[w,v]=x.useState(""),[g,f]=x.useState({daily:{}}),[E,D]=x.useState(new Set),[m,N]=x.useState(!1),[k,O]=x.useState({reason:"",type:"",time:"",employee:""});x.useEffect(()=>{S()},[]),x.useEffect(()=>{o.length>0&&P()},[u,j,b,w,o]);const S=async()=>{try{const I=(await z.getAll(L.USERS)).filter(A=>A.role==="employee");i(I)}catch(c){console.error("Error loading employees:",c),R.error("Failed to load employees")}},_=()=>{const c=new Date;switch(j){case"today":return{start:We(c),end:Ue(c)};case"week":return{start:ct(c,{weekStartsOn:1}),end:Qt(c,{weekStartsOn:1})};case"month":return{start:yn(c),end:fn(c)};case"custom":return{start:b?We(new Date(b)):We(c),end:w?Ue(new Date(w)):Ue(c)};default:return{start:We(c),end:Ue(c)}}},P=async()=>{try{l(!0);const{start:c,end:I}=_();console.log("Loading attendance data with filters:",{selectedEmployee:u,dateRange:j,start:c.toISOString(),end:I.toISOString()});let A=[];u==="all"?A=await z.getAll(L.ATTENDANCE):A=await z.getWhere(L.ATTENDANCE,"userId","==",u),console.log("Raw attendance records from Firebase:",A.length);const B=A.filter(F=>{if(!F.date)return!1;let H;typeof F.date=="string"?H=new Date(F.date+"T00:00:00"):F.date.toDate?H=F.date.toDate():H=new Date(F.date);const K=new Date(H.getFullYear(),H.getMonth(),H.getDate()),Z=new Date(c.getFullYear(),c.getMonth(),c.getDate()),ie=new Date(I.getFullYear(),I.getMonth(),I.getDate(),23,59,59);return K>=Z&&K<=ie});console.log("Filtered attendance records:",B.length),a(B),T(B)}catch(c){console.error("Error loading attendance data:",c),R.error("Failed to load attendance data: "+c.message)}finally{l(!1)}},T=c=>{try{const I={};c.forEach(A=>{if(!A||typeof A!="object"||!A.userId||!A.date){console.warn("Invalid record found:",A);return}if(!A.type||A.type!=="checkin"&&A.type!=="checkout"){console.warn("Record missing type or invalid type:",A);return}const B=`${A.userId}_${A.date}`;if(!I[B]){const F=o.find(H=>H.uid===A.userId);I[B]={userId:A.userId,date:A.date,employeeName:(F==null?void 0:F.name)||"Unknown Employee",employee:F||{employeeId:"Unknown",name:"Unknown Employee"},records:[]}}I[B].records.push(A)}),Object.values(I).forEach(A=>{try{A.workingHours=No.calculateDayWorkingHours(A.records)}catch(B){console.error("Error calculating working hours for day:",A.date,B),A.workingHours={regularHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},lateHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},totalHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},status:"ERROR"}}}),f({daily:I})}catch(I){console.error("Error processing summary data:",I),f({daily:{}})}},h=()=>{if(n.length===0){R.error("No data to export");return}const c=n.map(H=>{const K=o.find(Z=>Z.uid===H.userId);return{"Employee Name":(K==null?void 0:K.name)||"Unknown","Employee ID":(K==null?void 0:K.employeeId)||"Unknown",Date:H.date,Day:W(He(H.date),"EEEE"),Type:H.type,Time:H.type==="checkin"?H.checkInTime:H.checkOutTime,"Scheduled Time":H.type==="checkin"?H.scheduledCheckIn:H.scheduledCheckOut,"Late Reason":H.lateReason||"",Status:H.lateReason?"Late":"On Time"}}),I=[Object.keys(c[0]).join(","),...c.map(H=>Object.values(H).map(K=>`"${K}"`).join(","))].join(`
`),A=new Blob([I],{type:"text/csv"}),B=window.URL.createObjectURL(A),F=document.createElement("a");F.href=B,F.download=`attendance_report_${W(new Date,"yyyy-MM-dd")}.csv`,F.click(),window.URL.revokeObjectURL(B),R.success("Attendance report exported successfully!")};return r&&o.length===0?e.jsx("div",{className:"content",children:e.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[e.jsx(V,{size:48,style:{color:"#3498db",marginBottom:"20px"}}),e.jsx("h3",{children:"Loading Attendance Management"}),e.jsx("p",{children:"Loading attendance data..."})]})}):(s==null?void 0:s.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[e.jsx(ae,{size:48,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsx("h2",{style:{margin:0,color:"#333"},children:"Attendance Management"}),e.jsxs("button",{onClick:h,className:"btn btn-primary",style:{display:"flex",alignItems:"center",gap:"8px"},disabled:n.length===0,children:[e.jsx(ft,{size:20}),"Export CSV"]})]}),o.length===0&&e.jsx("div",{className:"card",style:{marginBottom:"20px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7"},children:e.jsxs("div",{style:{padding:"15px",textAlign:"center"},children:[e.jsx(ae,{size:32,style:{color:"#f39c12",marginBottom:"10px"}}),e.jsx("h4",{style:{color:"#856404",margin:"0 0 10px 0"},children:"No Employees Found"}),e.jsx("p",{style:{color:"#856404",margin:0},children:"No employees are registered in the system. Please add employees first to view attendance records."})]})}),o.length>0&&e.jsxs("div",{className:"card",style:{marginBottom:"20px"},children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333",fontSize:"18px"},children:[e.jsx(dt,{size:20,style:{marginRight:"8px",verticalAlign:"middle"}}),"Filter Attendance Records"]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",alignItems:"end"},children:[e.jsxs("div",{children:[e.jsxs("label",{className:"form-label",children:[e.jsx(ue,{size:16,style:{marginRight:"6px",verticalAlign:"middle"}}),"Employee"]}),e.jsxs("select",{value:u,onChange:c=>d(c.target.value),className:"form-select",children:[e.jsx("option",{value:"all",children:"All Employees"}),o.map(c=>e.jsxs("option",{value:c.uid,children:[c.name," (",c.employeeId,")"]},c.uid))]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"form-label",children:[e.jsx(V,{size:16,style:{marginRight:"6px",verticalAlign:"middle"}}),"Date Range"]}),e.jsxs("select",{value:j,onChange:c=>y(c.target.value),className:"form-select",children:[e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),j==="custom"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Start Date"}),e.jsx("input",{type:"date",value:b,onChange:c=>p(c.target.value),className:"form-input"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"End Date"}),e.jsx("input",{type:"date",value:w,onChange:c=>v(c.target.value),className:"form-input"})]})]}),e.jsxs("button",{onClick:P,className:"btn btn-secondary",style:{display:"flex",alignItems:"center",gap:"8px",justifySelf:"start"},disabled:r,children:[e.jsx(dt,{size:18}),r?"Loading...":"Apply Filters"]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333",fontSize:"18px"},children:[e.jsx(V,{size:20,style:{marginRight:"8px",verticalAlign:"middle"}}),"Attendance Records (",Object.keys(g.daily).length,")"]}),n.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No attendance records found for the selected criteria"}),o.length===0&&e.jsx("p",{style:{fontSize:"14px",marginTop:"10px"},children:"No employees found. Please ensure employees are registered in the system."})]}):Object.keys(g.daily).length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"Processing attendance data..."})]}):e.jsx("div",{className:"table-container",style:{overflowX:"auto",width:"100%"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%",minWidth:"700px"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Employee"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Date"}),e.jsx("th",{style:{width:"80px",minWidth:"80px"},children:"Day"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Regular Check In/Out"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Late Check In/Out"}),e.jsx("th",{style:{width:"160px",minWidth:"160px"},children:"Working Hours"})]})}),e.jsx("tbody",{children:Object.values(g.daily).map(c=>{var ie;const I=`${c.userId}_${c.date}`,A=c.records.filter(C=>C.type==="checkin"),B=c.records.filter(C=>C.type==="checkout"),F=A.filter(C=>!C.isLate&&!C.lateReason),H=B.filter(C=>!C.isLate&&!C.lateReason),K=A.filter(C=>C.isLate||C.lateReason),Z=B.filter(C=>C.isLate||C.lateReason);return e.jsxs("tr",{style:{backgroundColor:"white"},children:[e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(ue,{size:14,style:{color:"#3498db",flexShrink:0}}),e.jsxs("div",{style:{minWidth:0},children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:c.employeeName}),e.jsxs("div",{style:{fontSize:"11px",color:"#666",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:["ID: ",(ie=c.employee)==null?void 0:ie.employeeId]})]})]})}),e.jsxs("td",{style:{padding:"12px 8px",fontSize:"12px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[e.jsx("div",{style:{fontWeight:"bold",marginBottom:"2px"},children:W(He(c.date),"MMM dd")}),e.jsx("div",{style:{fontSize:"11px",color:"#666"},children:W(He(c.date),"yyyy")})]}),e.jsx("td",{style:{padding:"12px 8px",fontSize:"11px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:W(He(c.date),"EEE")}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:F.length===0&&H.length===0?e.jsx("span",{style:{color:"#999",fontSize:"11px",fontStyle:"italic"},children:"No regular records"}):e.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[F.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(te,{size:12,style:{color:"#27ae60"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#27ae60",fontWeight:"bold"},children:["In: ",F.length]})]}),H.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(re,{size:12,style:{color:"#2980b9"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#2980b9",fontWeight:"bold"},children:["Out: ",H.length]})]}),(F.length>0||H.length>0)&&e.jsxs("button",{onClick:()=>{O({reason:`Regular Check-ins: ${F.length}, Check-outs: ${H.length}`,type:"Regular Records",time:c.date,employee:c.employeeName,records:[...F,...H]}),N(!0)},className:"btn btn-secondary",style:{padding:"2px 6px",fontSize:"9px",display:"flex",alignItems:"center",gap:"2px",margin:"0 auto",minWidth:"50px"},title:"View regular check-in/out details",children:[e.jsx(V,{size:10}),"Details"]})]})}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:K.length===0&&Z.length===0?e.jsx("span",{style:{color:"#999",fontSize:"11px",fontStyle:"italic"},children:"No late records"}):e.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[K.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(ae,{size:12,style:{color:"#f39c12"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#f39c12",fontWeight:"bold"},children:["Late In: ",K.length]})]}),Z.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(ae,{size:12,style:{color:"#e74c3c"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#e74c3c",fontWeight:"bold"},children:["Late Out: ",Z.length]})]}),(K.length>0||Z.length>0)&&e.jsxs("button",{onClick:()=>{O({reason:`Late Check-ins: ${K.length}, Late Check-outs: ${Z.length}`,type:"Late Records",time:c.date,employee:c.employeeName,records:[...K,...Z]}),N(!0)},className:"btn btn-warning",style:{padding:"2px 6px",fontSize:"9px",display:"flex",alignItems:"center",gap:"2px",margin:"0 auto",minWidth:"50px"},title:"View late check-in/out details and reasons",children:[e.jsx(ae,{size:10}),"Details"]})]})}),e.jsx("td",{style:{padding:"12px 8px"},children:e.jsxs("div",{style:{fontSize:"11px",textAlign:"center"},children:[c.workingHours.regularHours.totalMinutes>0&&e.jsxs("div",{style:{color:"#27ae60",marginBottom:"2px",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[e.jsx(te,{size:10}),e.jsxs("span",{style:{fontSize:"10px"},children:["R: ",c.workingHours.regularHours.display]})]}),c.workingHours.lateHours.totalMinutes>0&&e.jsxs("div",{style:{color:"#f39c12",marginBottom:"2px",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[e.jsx(ae,{size:10}),e.jsxs("span",{style:{fontSize:"10px"},children:["L: ",c.workingHours.lateHours.display]})]}),e.jsxs("div",{style:{fontWeight:"bold",borderTop:"1px solid #eee",paddingTop:"4px",marginTop:"4px",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[e.jsx(V,{size:12,style:{color:"#333"}}),e.jsx("span",{style:{fontSize:"11px"},children:c.workingHours.totalHours.display})]}),c.workingHours.status==="INCOMPLETE"&&e.jsxs("div",{style:{color:"#e74c3c",fontSize:"9px",marginTop:"2px",display:"flex",alignItems:"center",justifyContent:"center",gap:"2px"},children:[e.jsx(re,{size:8}),"Incomplete"]})]})})]},I)})})]})})]}),m&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e4},children:e.jsxs("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"30px",maxWidth:"600px",width:"95%",maxHeight:"85vh",overflowY:"auto",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"25px"},children:[e.jsxs("h3",{style:{margin:0,color:"#333",fontSize:"20px",display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(V,{size:24,style:{color:"#3498db"}}),k.type," Details"]}),e.jsx("button",{onClick:()=>N(!1),style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666",padding:"0",width:"30px",height:"30px",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:"50%",transition:"background 0.2s"},onMouseEnter:c=>c.target.style.background="#f8f9fa",onMouseLeave:c=>c.target.style.background="none",children:"×"})]}),e.jsx("div",{style:{marginBottom:"25px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"20px",padding:"20px",backgroundColor:"#f8f9fa",borderRadius:"8px"},children:[e.jsxs("div",{children:[e.jsxs("strong",{style:{color:"#666",fontSize:"14px",display:"flex",alignItems:"center",gap:"6px"},children:[e.jsx(ue,{size:16}),"Employee:"]}),e.jsx("div",{style:{color:"#333",fontSize:"16px",fontWeight:"bold"},children:k.employee})]}),e.jsxs("div",{children:[e.jsxs("strong",{style:{color:"#666",fontSize:"14px",display:"flex",alignItems:"center",gap:"6px"},children:[e.jsx(V,{size:16}),"Date:"]}),e.jsx("div",{style:{color:"#333",fontSize:"16px",fontWeight:"bold"},children:k.time})]})]})}),k.records&&k.records.length>0?e.jsxs("div",{style:{marginBottom:"25px"},children:[e.jsx("strong",{style:{color:"#666",fontSize:"16px",display:"block",marginBottom:"15px"},children:"Detailed Records:"}),e.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"8px",overflow:"hidden"},children:k.records.map((c,I)=>e.jsxs("div",{style:{padding:"15px",borderBottom:I<k.records.length-1?"1px solid #e9ecef":"none",display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"10px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[c.type==="checkin"?e.jsx(te,{size:16,style:{color:c.isLate?"#f39c12":"#27ae60"}}):e.jsx(re,{size:16,style:{color:c.isLate?"#e74c3c":"#2980b9"}}),e.jsxs("div",{children:[e.jsxs("div",{style:{fontWeight:"bold",fontSize:"14px"},children:[c.type==="checkin"?"Check In":"Check Out",c.isLate&&e.jsx("span",{style:{color:"#f39c12",marginLeft:"8px"},children:"(Late)"})]}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["Time: ",c.type==="checkin"?c.checkInTime:c.checkOutTime]})]})]}),c.lateReason&&e.jsxs("div",{style:{backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"4px",padding:"8px 12px",fontSize:"12px",color:"#856404",maxWidth:"300px"},children:[e.jsx("strong",{children:"Reason:"})," ",c.lateReason]})]},I))})]}):e.jsxs("div",{style:{marginBottom:"25px"},children:[e.jsx("strong",{style:{color:"#666",fontSize:"14px",display:"block",marginBottom:"8px"},children:"Summary:"}),e.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"6px",padding:"15px",color:"#333",fontSize:"15px",lineHeight:"1.5"},children:k.reason})]}),e.jsx("div",{style:{textAlign:"right"},children:e.jsxs("button",{onClick:()=>N(!1),className:"btn btn-primary",style:{padding:"12px 24px",display:"flex",alignItems:"center",gap:"8px",margin:"0 0 0 auto"},children:[e.jsx(te,{size:16}),"Close"]})})]})})]})},Jo=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,o]=x.useState(!1),[i,r]=x.useState(""),[l,u]=x.useState("all"),[d,j]=x.useState(null),[y,b]=x.useState(""),[p,w]=x.useState(!1),[v,g]=x.useState(!1),[f,E]=x.useState({});x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&D()},[t]);const D=async()=>{o(!0);try{console.log("Admin loading holiday requests...");const h=await z.getAll(L.HOLIDAY_REQUESTS);console.log("Fetched holiday requests:",h);const c=h.sort((I,A)=>new Date(A.requestDate)-new Date(I.requestDate));n(c),c.length===0?R.info("No holiday requests found"):R.success(`Loaded ${c.length} holiday requests`)}catch(h){console.error("Error loading holiday requests:",h),R.error("Failed to load holiday requests: "+h.message)}finally{o(!1)}},m=async(h,c)=>{try{await pt.updateRequestStatus(h,c,y);const I=s.find(A=>A.id===h);I&&I.userId&&(c==="approved"?await Lo(I.userId,I.startDate,I.endDate):c==="rejected"&&await _o(I.userId,I.startDate,I.endDate,y||"No reason provided")),R.success(`Holiday request ${c} successfully`),n(A=>A.map(B=>B.id===h?{...B,status:c,adminRemarks:y}:B)),w(!1),b(""),j(null)}catch(I){console.error("Error updating request status:",I),R.error("Failed to update request status")}},N=(h,c)=>{j({...h,action:c}),b(h.adminRemarks||""),w(!0)},k=h=>{E({title:"Holiday Request Reason",content:h.reason,type:"details",employeeName:h.employeeName,date:`${h.startDate} to ${h.endDate}`,time:`${h.days} days`}),g(!0)},O=h=>{E({title:"Admin Remarks",content:h.adminRemarks||"",type:"remarks",employeeName:h.employeeName,date:`${h.startDate} to ${h.endDate}`,time:`${h.days} days`}),g(!0)},S=s.filter(h=>{var A,B,F;const c=((A=h.employeeName)==null?void 0:A.toLowerCase().includes(i.toLowerCase()))||((B=h.employeeId)==null?void 0:B.toLowerCase().includes(i.toLowerCase()))||((F=h.reason)==null?void 0:F.toLowerCase().includes(i.toLowerCase())),I=l==="all"||h.status===l;return c&&I}),_=h=>{const c={pending:{color:"#f39c12",bg:"#fef9e7",text:"Pending"},approved:{color:"#27ae60",bg:"#eafaf1",text:"Approved"},rejected:{color:"#e74c3c",bg:"#fdedec",text:"Rejected"}},I=c[h]||c.pending;return e.jsx("span",{style:{padding:"4px 12px",borderRadius:"12px",fontSize:"12px",fontWeight:"bold",color:I.color,backgroundColor:I.bg,border:`1px solid ${I.color}20`},children:I.text})},T={total:s.length,pending:s.filter(c=>c.status==="pending").length,approved:s.filter(c=>c.status==="approved").length,rejected:s.filter(c=>c.status==="rejected").length};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card",children:[e.jsx("h2",{style:{color:"#e74c3c",marginBottom:"10px"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsx("h2",{style:{color:"#333",margin:0},children:"Holiday Request Management"}),e.jsx("button",{onClick:D,className:"btn btn-primary",disabled:a,children:a?"Loading...":"Refresh"})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"30px"},children:[e.jsxs("div",{className:"stat-card",children:[e.jsx(se,{size:32,style:{color:"#3498db",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#3498db"},children:T.total}),e.jsx("div",{className:"stat-label",children:"Total Requests"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(V,{size:32,style:{color:"#f39c12",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#f39c12"},children:T.pending}),e.jsx("div",{className:"stat-label",children:"Pending"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(te,{size:32,style:{color:"#27ae60",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#27ae60"},children:T.approved}),e.jsx("div",{className:"stat-label",children:"Approved"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(re,{size:32,style:{color:"#e74c3c",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#e74c3c"},children:T.rejected}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]}),e.jsx("div",{className:"card",style:{marginBottom:"20px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr auto",gap:"20px",alignItems:"center","@media (max-width: 768px)":{gridTemplateColumns:"1fr",gap:"15px"}},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Qe,{size:16,style:{position:"absolute",left:"10px",top:"50%",transform:"translateY(-50%)",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search by employee name, ID, or reason...",value:i,onChange:h=>r(h.target.value),className:"form-input",style:{paddingLeft:"35px"}})]}),e.jsxs("select",{value:l,onChange:h=>u(h.target.value),className:"form-select",style:{minWidth:"150px"},children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"})]})]})}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Holiday Requests (",S.length,")"]}),a?e.jsx("div",{style:{textAlign:"center",padding:"40px"},children:e.jsx("div",{style:{color:"#666"},children:"Loading holiday requests..."})}):S.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Employee"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Start Date"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"End Date"}),e.jsx("th",{style:{width:"70px",minWidth:"70px"},children:"Days"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Reason"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Admin Remarks"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Request Date"}),e.jsx("th",{style:{width:"180px",minWidth:"180px"},children:"Actions"})]})}),e.jsx("tbody",{children:S.map(h=>e.jsxs("tr",{children:[e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(gt,{size:14,style:{color:"#3498db"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px",maxWidth:"100px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:h.employeeName}),e.jsxs("div",{style:{fontSize:"11px",color:"#666"},children:["ID: ",h.employeeId]})]})]})}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"},children:h.startDate}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"},children:h.endDate}),e.jsx("td",{style:{textAlign:"center"},children:e.jsx("span",{style:{fontWeight:"bold",color:"#3498db",padding:"4px 8px",background:"#e3f2fd",borderRadius:"12px",fontSize:"12px",display:"inline-block"},children:h.days})}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>k(h),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View holiday reason",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:_(h.status)}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>O(h),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:h.adminRemarks?1:.6},title:h.adminRemarks?"View admin remarks":"No remarks available",children:[e.jsx(xe,{size:14}),h.adminRemarks?"View Remarks":"No Remarks"]})}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"},children:h.requestDate}),e.jsx("td",{style:{padding:"8px"},children:e.jsxs("div",{style:{display:"flex",gap:"4px",flexWrap:"wrap",justifyContent:"center"},children:[h.status==="pending"&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>N(h,"approve"),className:"btn btn-success",style:{fontSize:"11px",padding:"4px 8px",display:"flex",alignItems:"center",gap:"4px",minWidth:"70px"},title:"Approve request",children:[e.jsx(te,{size:12}),"Approve"]}),e.jsxs("button",{onClick:()=>N(h,"reject"),className:"btn btn-danger",style:{fontSize:"11px",padding:"4px 8px",display:"flex",alignItems:"center",gap:"4px",minWidth:"70px"},title:"Reject request",children:[e.jsx(re,{size:12}),"Reject"]})]}),e.jsxs("button",{onClick:()=>N(h,"remark"),className:"btn btn-secondary",style:{fontSize:"11px",padding:"4px 8px",display:"flex",alignItems:"center",gap:"4px",minWidth:"70px"},title:"Add/Edit remarks",children:[e.jsx(xe,{size:12}),"Remarks"]})]})})]},h.id))})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(se,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No holiday requests found"}),i&&e.jsx("p",{children:"Try adjusting your search criteria"})]})]}),p&&d&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:e.jsxs("div",{style:{backgroundColor:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"500px",maxHeight:"80vh",overflowY:"auto"},children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:d.action==="approve"?"Approve Request":d.action==="reject"?"Reject Request":"Add/Edit Remarks"}),e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f8f9fa",borderRadius:"4px"},children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Employee:"})," ",d.employeeName]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Dates:"})," ",d.startDate," to ",d.endDate]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Days:"})," ",d.days]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Reason:"})," ",d.reason]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Admin Remarks"}),e.jsx("textarea",{value:y,onChange:h=>b(h.target.value),className:"form-textarea",placeholder:"Add your remarks here...",rows:4})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>{w(!1),b(""),j(null)},className:"btn btn-secondary",children:"Cancel"}),d.action==="approve"&&e.jsxs("button",{onClick:()=>m(d.id,"approved"),className:"btn btn-success",children:[e.jsx(te,{size:16}),"Approve Request"]}),d.action==="reject"&&e.jsxs("button",{onClick:()=>m(d.id,"rejected"),className:"btn btn-danger",children:[e.jsx(re,{size:16}),"Reject Request"]}),d.action==="remark"&&e.jsxs("button",{onClick:()=>m(d.id,d.status),className:"btn btn-primary",children:[e.jsx(xe,{size:16}),"Save Remarks"]})]})]})}),e.jsx(Fe,{isOpen:v,onClose:()=>g(!1),title:f.title,content:f.content,type:f.type,employeeName:f.employeeName,date:f.date,time:f.time})]})},Qo=()=>{const{userProfile:t,loading:s}=Q(),[n,a]=x.useState([]),[o,i]=x.useState([]),[r,l]=x.useState(!1),[u,d]=x.useState(""),[j,y]=x.useState("all"),[b,p]=x.useState("all"),[w,v]=x.useState(!1),[g,f]=x.useState(null),[E,D]=x.useState(!1),[m,N]=x.useState({}),[k,O]=x.useState({title:"",description:"",details:"",assignedTo:"",assignedToName:"",priority:"medium",dueDate:"",status:"pending"});x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&(S(),_())},[t]);const S=async()=>{l(!0);try{console.log("Admin loading tasks...");const C=await Te.getAllTasks();console.log("Fetched tasks:",C);const X=C.sort(($,M)=>{var G,ne;const U=(G=$.createdAt)!=null&&G.seconds?new Date($.createdAt.seconds*1e3):new Date($.createdAt||0);return((ne=M.createdAt)!=null&&ne.seconds?new Date(M.createdAt.seconds*1e3):new Date(M.createdAt||0))-U});a(X),X.length===0?console.log("No tasks found"):console.log(`Loaded ${X.length} tasks`)}catch(C){console.error("Error loading tasks:",C),R.error("Failed to load tasks: "+C.message)}finally{l(!1)}},_=async()=>{try{const C=await je.getAllEmployees();console.log("Loaded employees:",C),i(C||[])}catch(C){console.error("Error loading employees:",C),R.error("Failed to load employees"),i([])}},P=C=>{const{name:X,value:$}=C.target;if(O(M=>({...M,[X]:$})),X==="assignedTo"){const M=o.find(U=>U.uid===$);O(U=>({...U,assignedToName:M?M.name:""}))}},T=async C=>{var X,$,M;C.preventDefault(),l(!0);try{if(s){R.error("Please wait while user profile loads..."),l(!1);return}if(!((X=k.title)!=null&&X.trim())){R.error("Task title is required"),l(!1);return}if(!(($=k.description)!=null&&$.trim())){R.error("Task description is required"),l(!1);return}if(!k.assignedTo){R.error("Please select an employee to assign the task"),l(!1);return}if(!k.dueDate){R.error("Due date is required"),l(!1);return}if(!t){R.error("User profile not loaded. Please refresh the page."),l(!1);return}const U=t.uid||t.id;if(!U){console.error("No user ID found in profile:",t),R.error("User ID not found. Please refresh the page."),l(!1);return}const q=t.name||t.displayName||t.email;if(!q){console.error("No user name found in profile:",t),R.error("User name not found. Please refresh the page."),l(!1);return}const G={title:k.title.trim(),description:k.description.trim(),details:((M=k.details)==null?void 0:M.trim())||"",assignedTo:k.assignedTo,assignedToName:k.assignedToName||"",assignedBy:U,assignedByName:q,priority:k.priority||"medium",dueDate:k.dueDate,date:k.dueDate,day:W(new Date(k.dueDate),"EEEE"),status:k.status||"pending",employeeRemarks:"",reason:""};console.log("Submitting task data:",G),g?(await Te.updateTask(g.id,G),R.success("Task updated successfully!")):(await Te.createTask(G),k.assignedTo&&k.assignedToName&&await Do(k.assignedTo,k.title,t.name),R.success("Task assigned successfully!")),I(),S()}catch(U){console.error("Error saving task:",U),R.error("Failed to save task: "+(U.message||"Unknown error"))}finally{l(!1)}},h=C=>{f(C),O({title:C.title||"",description:C.description||"",details:C.details||"",assignedTo:C.assignedTo||"",assignedToName:C.assignedToName||"",priority:C.priority||"medium",dueDate:C.dueDate||C.date||"",status:C.status||"pending"}),v(!0)},c=async C=>{if(window.confirm("Are you sure you want to delete this task?"))try{await Te.deleteTask(C),R.success("Task deleted successfully!"),S()}catch(X){console.error("Error deleting task:",X),R.error("Failed to delete task")}},I=()=>{O({title:"",description:"",details:"",assignedTo:"",assignedToName:"",priority:"medium",dueDate:"",status:"pending"}),f(null),v(!1)},A=(n||[]).filter(C=>{if(!C)return!1;const X=(C.title||"").toLowerCase().includes(u.toLowerCase())||(C.description||"").toLowerCase().includes(u.toLowerCase())||(C.assignedToName||"").toLowerCase().includes(u.toLowerCase()),$=j==="all"||C.status===j,M=b==="all"||C.assignedTo===b;return X&&$&&M}),B=C=>{const X={pending:{color:"#f39c12",bg:"#fef9e7",text:"Pending",icon:V},completed:{color:"#27ae60",bg:"#eafaf1",text:"Completed",icon:te},"not-achieved":{color:"#e74c3c",bg:"#fdedec",text:"Not Achieved",icon:re}},$=X[C]||X.pending,M=$.icon;return e.jsxs("span",{style:{padding:"4px 12px",borderRadius:"12px",fontSize:"12px",fontWeight:"bold",color:$.color,backgroundColor:$.bg,border:`1px solid ${$.color}20`,display:"inline-flex",alignItems:"center",gap:"4px"},children:[e.jsx(M,{size:12}),$.text]})},F=C=>{switch(C){case"high":return"#e74c3c";case"medium":return"#f39c12";case"low":return"#27ae60";default:return"#95a5a6"}},H=()=>{const C=n||[];return{total:C.length,pending:C.filter($=>$&&$.status==="pending").length,completed:C.filter($=>$&&$.status==="completed").length,notAchieved:C.filter($=>$&&$.status==="not-achieved").length}},K=C=>{N({title:"Task Description & Details",content:`${C.description||"No description"}

${C.details||""}`.trim(),type:"details",employeeName:`Assigned to: ${C.assignedToName||"Unknown Employee"}`,date:C.dueDate||C.date,time:C.day||""}),D(!0)},Z=C=>{N({title:"Employee Remarks",content:C.employeeRemarks||C.reason||"",type:"remarks",employeeName:`From: ${C.assignedToName||"Unknown Employee"}`,date:C.dueDate||C.date,time:C.day||""}),D(!0)},ie=H();return s?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx("h3",{children:"Loading..."}),e.jsx("p",{children:"Please wait while we load your profile."})]})}):!t||t.role!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card",children:[e.jsx("h2",{style:{color:"#e74c3c",marginBottom:"10px"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsx("h2",{style:{color:"#333",margin:0},children:"Task Management"}),e.jsxs("div",{style:{display:"flex",gap:"10px"},children:[e.jsx("button",{onClick:S,className:"btn btn-secondary",disabled:r,children:r?"Loading...":"Refresh"}),e.jsxs("button",{onClick:()=>v(!0),className:"btn btn-primary",disabled:s||!t,children:[e.jsx(ge,{size:16}),"Assign New Task"]})]})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"30px"},children:[e.jsxs("div",{className:"stat-card",children:[e.jsx(ke,{size:32,style:{color:"#3498db",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#3498db"},children:ie.total}),e.jsx("div",{className:"stat-label",children:"Total Tasks"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(V,{size:32,style:{color:"#f39c12",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#f39c12"},children:ie.pending}),e.jsx("div",{className:"stat-label",children:"Pending"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(te,{size:32,style:{color:"#27ae60",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#27ae60"},children:ie.completed}),e.jsx("div",{className:"stat-label",children:"Completed"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(re,{size:32,style:{color:"#e74c3c",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#e74c3c"},children:ie.notAchieved}),e.jsx("div",{className:"stat-label",children:"Not Achieved"})]})]}),e.jsx("div",{className:"card",style:{marginBottom:"20px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"2fr 1fr 1fr",gap:"20px",alignItems:"end"},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx("label",{className:"form-label",children:"Search Tasks"}),e.jsx(Qe,{size:20,style:{position:"absolute",left:"12px",bottom:"12px",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search by title, description, or employee...",value:u,onChange:C=>d(C.target.value),className:"form-input",style:{paddingLeft:"40px"}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Status Filter"}),e.jsxs("select",{value:j,onChange:C=>y(C.target.value),className:"form-input",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"not-achieved",children:"Not Achieved"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Employee Filter"}),e.jsxs("select",{value:b,onChange:C=>p(C.target.value),className:"form-input",children:[e.jsx("option",{value:"all",children:"All Employees"}),o.map(C=>e.jsxs("option",{value:C.uid,children:[C.name," (",C.employeeId,")"]},C.uid))]})]})]})}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Tasks (",A.length,")"]}),r?e.jsx("div",{style:{textAlign:"center",padding:"40px"},children:e.jsx("div",{style:{color:"#666"},children:"Loading tasks..."})}):A.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{minWidth:"150px"},children:"Title"}),e.jsx("th",{style:{minWidth:"150px"},children:"Description"}),e.jsx("th",{style:{minWidth:"120px"},children:"Assigned To"}),e.jsx("th",{style:{minWidth:"80px"},children:"Priority"}),e.jsx("th",{style:{minWidth:"100px"},children:"Due Date"}),e.jsx("th",{style:{minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{minWidth:"150px"},children:"Employee Remarks"}),e.jsx("th",{style:{minWidth:"150px"},children:"Actions"})]})}),e.jsx("tbody",{children:A.map(C=>{var X,$;return e.jsxs("tr",{children:[e.jsx("td",{style:{fontWeight:"500"},children:C.title||C.description||"Untitled Task"}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>K(C),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task description and details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsxs("td",{children:[e.jsx("div",{style:{fontSize:"14px",fontWeight:"500"},children:C.assignedToName||"Unknown Employee"}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:((X=o.find(M=>M.uid===C.assignedTo))==null?void 0:X.employeeId)||"N/A"})]}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:F(C.priority)},children:(($=C.priority)==null?void 0:$.toUpperCase())||"MEDIUM"})}),e.jsx("td",{children:(()=>{try{const M=C.dueDate||C.date;return M?new Date(M).toLocaleDateString():"No due date"}catch{return"Invalid date"}})()}),e.jsx("td",{children:B(C.status)}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>Z(C),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:C.employeeRemarks||C.reason?1:.6},title:C.employeeRemarks||C.reason?"View employee remarks":"No remarks available",children:[e.jsx(xe,{size:14}),C.employeeRemarks||C.reason?"View Remarks":"No Remarks"]})}),e.jsx("td",{children:e.jsxs("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap"},children:[e.jsxs("button",{onClick:()=>h(C),className:"btn btn-secondary",style:{fontSize:"12px",padding:"4px 8px"},children:[e.jsx(es,{size:14}),"Edit"]}),e.jsxs("button",{onClick:()=>c(C.id),className:"btn btn-danger",style:{fontSize:"12px",padding:"4px 8px"},children:[e.jsx(jn,{size:14}),"Delete"]})]})})]},C.id)})})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(ke,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No tasks found"}),u&&e.jsx("p",{children:"Try adjusting your search criteria"})]})]}),w&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:e.jsxs("div",{style:{backgroundColor:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"600px",maxHeight:"80vh",overflowY:"auto"},children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:g?"Edit Task":"Assign New Task"}),e.jsxs("form",{onSubmit:T,children:[e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Task Title *"}),e.jsx("input",{type:"text",name:"title",value:k.title,onChange:P,className:"form-input",placeholder:"Enter task title...",required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Assign To *"}),e.jsxs("select",{name:"assignedTo",value:k.assignedTo,onChange:P,className:"form-input",required:!0,children:[e.jsx("option",{value:"",children:"Select Employee"}),o.map(C=>e.jsxs("option",{value:C.uid,children:[C.name," (",C.employeeId,")"]},C.uid))]})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Task Description *"}),e.jsx("textarea",{name:"description",value:k.description,onChange:P,className:"form-textarea",placeholder:"Enter task description...",rows:3,required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Additional Details"}),e.jsx("textarea",{name:"details",value:k.details,onChange:P,className:"form-textarea",placeholder:"Enter additional task details...",rows:2})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Priority"}),e.jsxs("select",{name:"priority",value:k.priority,onChange:P,className:"form-input",children:[e.jsx("option",{value:"low",children:"Low"}),e.jsx("option",{value:"medium",children:"Medium"}),e.jsx("option",{value:"high",children:"High"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Due Date *"}),e.jsx("input",{type:"date",name:"dueDate",value:k.dueDate,onChange:P,className:"form-input",min:W(new Date,"yyyy-MM-dd"),required:!0})]})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end",marginTop:"20px"},children:[e.jsx("button",{type:"button",onClick:I,className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:r,children:r?"Saving...":g?"Update Task":"Assign Task"})]})]})]})}),e.jsx(Fe,{isOpen:E,onClose:()=>D(!1),title:m.title,content:m.content,type:m.type,employeeName:m.employeeName,date:m.date,time:m.time})]})},Zo=()=>{const{userProfile:t}=Q(),[s,n]=x.useState(!1),[a,o]=x.useState([]),[i,r]=x.useState(!1),[l,u]=x.useState([]),[d,j]=x.useState({date:"",hours:"",reason:""});x.useEffect(()=>{t&&(y(),b())},[t]);const y=async()=>{try{const f=await Ne.getUserCompensation(t.uid);o(f)}catch(f){console.error("Error loading compensations:",f),R.error("Failed to load compensation records")}},b=async()=>{try{const f=await Ne.checkMissedCheckIns(t.uid);u(f)}catch(f){console.error("Error checking missed check-ins:",f)}},p=f=>{const{name:E,value:D}=f.target;j(m=>({...m,[E]:D}))},w=async f=>{f.preventDefault(),n(!0);try{const E=parseFloat(d.hours);if(isNaN(E)||E<=0||E>24){R.error("Please enter valid hours (1-24)");return}await Ne.createCompensation({employeeId:t.uid,employeeName:t.name,date:d.date,hours:E,reason:d.reason,status:"pending"}),R.success("Compensation request submitted successfully"),r(!1),j({date:"",hours:"",reason:""}),y()}catch(E){console.error("Error submitting compensation:",E),R.error("Failed to submit compensation request")}finally{n(!1)}},v=async f=>{n(!0);try{await Ne.createCompensation({employeeId:t.uid,employeeName:t.name,date:f.date,hours:f.hours,reason:f.reason,status:"pending"}),R.success("Compensation request submitted successfully"),u(E=>E.filter(D=>D.date!==f.date)),y()}catch(E){console.error("Error submitting auto compensation:",E),R.error("Failed to submit compensation request")}finally{n(!1)}},g=f=>{const E={pending:{background:"#fff3cd",color:"#856404"},approved:{background:"#d4edda",color:"#155724"},rejected:{background:"#f8d7da",color:"#721c24"}};return e.jsx("span",{style:{padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500",...E[f]},children:f.charAt(0).toUpperCase()+f.slice(1)})};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Compensation Hours"}),e.jsxs("button",{onClick:()=>r(!0),className:"btn btn-primary",disabled:s,children:[e.jsx(ge,{size:16,style:{marginRight:"8px"}}),"Request Compensation"]})]}),l.length>0&&e.jsxs("div",{className:"card mb-20",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:[e.jsx(Ae,{size:20,style:{marginRight:"8px",color:"#e74c3c"}}),"Missed Check-ins"]}),e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Time Slot"}),e.jsx("th",{children:"Hours"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Action"})]})}),e.jsx("tbody",{children:l.map((f,E)=>e.jsxs("tr",{children:[e.jsx("td",{children:f.date?W(f.date.toDate?f.date.toDate():new Date(f.date),"MMM dd, yyyy"):"-"}),e.jsx("td",{children:f.startTime&&f.endTime?`${W(f.startTime.toDate?f.startTime.toDate():new Date(f.startTime),"HH:mm")} - ${W(f.endTime.toDate?f.endTime.toDate():new Date(f.endTime),"HH:mm")}`:"-"}),e.jsx("td",{children:f.hours.toFixed(1)}),e.jsx("td",{children:f.reason}),e.jsx("td",{children:e.jsx("button",{onClick:()=>v(f),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},disabled:s,children:"Request Compensation"})})]},E))})]})})]}),i&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Request Compensation Hours"}),e.jsxs("form",{onSubmit:w,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Date"}),e.jsx("input",{type:"date",name:"date",value:d.date,onChange:p,className:"form-input",required:!0,max:W(new Date,"yyyy-MM-dd")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Hours"}),e.jsx("input",{type:"number",name:"hours",value:d.hours,onChange:p,className:"form-input",required:!0,min:"0.5",max:"24",step:"0.5",placeholder:"Enter hours (0.5-24)"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Reason"}),e.jsx("textarea",{name:"reason",value:d.reason,onChange:p,className:"form-textarea",required:!0,placeholder:"Enter reason for compensation",rows:"4"})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{type:"button",onClick:()=>{r(!1),j({date:"",hours:"",reason:""})},className:"btn btn-secondary",disabled:s,children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:s,children:s?"Submitting...":"Submit Request"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"My Compensation Requests"}),a.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Hours"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Created At"}),e.jsx("th",{children:"Admin Remarks"})]})}),e.jsx("tbody",{children:a.map(f=>e.jsxs("tr",{children:[e.jsx("td",{children:f.date}),e.jsx("td",{children:f.hours}),e.jsx("td",{children:f.reason}),e.jsx("td",{children:g(f.status)}),e.jsx("td",{children:f.createdAt?W(f.createdAt.toDate?f.createdAt.toDate():new Date(f.createdAt),"MMM dd, yyyy HH:mm"):"-"}),e.jsx("td",{children:f.adminRemarks||"-"})]},f.id))})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No compensation requests found"})]})]})]})},ei=()=>{const{userProfile:t}=Q(),[s,n]=x.useState(!1),[a,o]=x.useState([]),[i,r]=x.useState(!1),[l,u]=x.useState(null),[d,j]=x.useState("");x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&y()},[t]);const y=async()=>{try{const v=await Ne.getAllCompensation();o(v)}catch(v){console.error("Error loading compensations:",v),R.error("Failed to load compensation records")}},b=async(v,g)=>{n(!0);try{await Ne.updateCompensationStatus(v,g,d),R.success(`Compensation request ${g} successfully`),r(!1),j(""),u(null),y()}catch(f){console.error("Error updating compensation status:",f),R.error("Failed to update compensation status")}finally{n(!1)}},p=v=>{u(v),j(""),r(!0)},w=v=>{const g={pending:{background:"#fff3cd",color:"#856404"},approved:{background:"#d4edda",color:"#155724"},rejected:{background:"#f8d7da",color:"#721c24"}};return e.jsx("span",{style:{padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500",...g[v]},children:v.charAt(0).toUpperCase()+v.slice(1)})};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx(V,{size:64,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{style:{color:"#e74c3c"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to manage compensation requests."})]})}):e.jsxs("div",{className:"content",children:[e.jsx("div",{className:"flex justify-between align-center mb-20",children:e.jsx("h2",{style:{color:"#333"},children:"Compensation Management"})}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"All Compensation Requests"}),a.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Employee"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Hours"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Created At"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:a.map(v=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold"},children:v.employeeName}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",v.employeeId]})]})}),e.jsx("td",{children:v.date}),e.jsx("td",{children:v.hours}),e.jsx("td",{children:v.reason}),e.jsx("td",{children:w(v.status)}),e.jsx("td",{children:v.createdAt?W(v.createdAt.toDate?v.createdAt.toDate():new Date(v.createdAt),"MMM dd, yyyy HH:mm"):"-"}),e.jsx("td",{children:v.status==="pending"&&e.jsxs("div",{className:"flex gap-10",children:[e.jsx("button",{onClick:()=>p(v),className:"btn btn-success",style:{padding:"5px 10px",fontSize:"12px"},title:"Approve Request",children:e.jsx(te,{size:14})}),e.jsx("button",{onClick:()=>p(v),className:"btn btn-danger",style:{padding:"5px 10px",fontSize:"12px"},title:"Reject Request",children:e.jsx(re,{size:14})})]})})]},v.id))})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No compensation requests found"})]})]}),i&&l&&e.jsx("div",{className:"modal-overlay",onClick:()=>{r(!1),j(""),u(null)},children:e.jsxs("div",{className:"modal-content",onClick:v=>v.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Update Compensation Request"}),e.jsx("button",{className:"modal-close",onClick:()=>{r(!1),j(""),u(null)},children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("div",{style:{marginBottom:"20px"},children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Employee:"})," ",l.employeeName]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Date:"})," ",l.date]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Hours:"})," ",l.hours]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Reason:"})," ",l.reason]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Admin Remarks"}),e.jsx("textarea",{value:d,onChange:v=>j(v.target.value),className:"form-textarea",placeholder:"Enter your remarks...",rows:"4"})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>{r(!1),j(""),u(null)},className:"btn btn-secondary",disabled:s,children:"Cancel"}),e.jsx("button",{onClick:()=>b(l.id,"approved"),className:"btn btn-success",disabled:s,children:s?"Processing...":"Approve"}),e.jsx("button",{onClick:()=>b(l.id,"rejected"),className:"btn btn-danger",disabled:s,children:s?"Processing...":"Reject"})]})]})]})})]})},ti=()=>{const{currentUser:t,userProfile:s}=Q(),[n,a]=x.useState(""),[o,i]=x.useState(!1);x.useEffect(()=>{s&&be.checkAndPerformWeeklyReset().catch(d=>{console.error("Error in weekly timetable reset:",d)})},[s]);const r=()=>{i(!o)},l=d=>{a(d),i(!1)};Yt.useEffect(()=>{s&&(s.role==="admin"?l("admin-dashboard"):l("dashboard"))},[s]);const u=()=>{switch(n){case"dashboard":return e.jsx(Wt,{});case"checkin":return e.jsx(Bo,{});case"timetable":return e.jsx(Fo,{});case"progress":return e.jsx(Uo,{});case"holidays":return e.jsx(Wo,{});case"tasks":return e.jsx($o,{});case"compensation":return e.jsx(Zo,{});case"admin-dashboard":return e.jsx($t,{onNavigate:a});case"employees":return e.jsx(qo,{});case"admin-timetable":return e.jsx(Vo,{});case"admin-progress-reports":return e.jsx(Go,{});case"attendance-records":return e.jsx(Xo,{});case"holiday-management":return e.jsx(Jo,{});case"task-management":return e.jsx(Qo,{});case"compensation-records":return e.jsx(ei,{});default:return(s==null?void 0:s.role)==="admin"?e.jsx($t,{}):e.jsx(Wt,{})}};return t?s?e.jsxs("div",{className:"app-layout",children:[e.jsx(Eo,{activeTab:n,setActiveTab:l,isMobileOpen:o,onMobileClose:()=>i(!1)}),e.jsxs("div",{className:"main-content",children:[e.jsx(To,{onMobileMenuToggle:r}),u()]}),o&&e.jsx("div",{className:"mobile-sidebar-overlay",onClick:()=>i(!1)})]}):e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",gap:"20px"},children:[e.jsx("div",{style:{width:"40px",height:"40px",border:"4px solid #f3f3f3",borderTop:"4px solid #3498db",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("p",{style:{color:"#666"},children:"Loading your profile..."})]}):e.jsx(Co,{})},si=()=>e.jsxs(So,{children:[e.jsx(ti,{}),e.jsx(bn,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,theme:{primary:"#4aed88"}}}})]});ut.createRoot(document.getElementById("root")).render(e.jsx(Yt.StrictMode,{children:e.jsx(si,{})}));
