import{r as x,a as As,R as Yt}from"./vendor-nf7bT_Uh.js";import{r as Re,_ as ze,C as Pe,a as Be,E as Kt,o as Ds,F as mt,L as Os,g as xt,i as Ms,b as Ls,v as _s,c as vt,d as Gt,e as zs,f as Ps,S as Bs,h as Fs,j as Hs,k as Xt,l as Us,m as Jt,n as qe,s as Ws,p as Le,q as Oe,t as nt,w as St,u as $s,x as qs,y as Vs,z as at,A as Ys,B as Ks,D as Gs,G as Xs,H as Js,I as Qs,J as Ct}from"./firebase-BfSkx5kM.js";import{V as R,f as W,s as ct,e as Qt,E as Zt,a as fe,L as Zs,b as en,B as It,C as tn,I as Tt,c as V,X as De,d as te,A as ae,M as sn,U as gt,g as nn,h as ue,i as se,F as he,j as ke,H as an,k as on,l as re,m as Ae,n as rn,o as ln,p as cn,P as ge,q as xe,r as dn,D as ft,t as un,u as dt,T as hn,S as Qe,v as pn,w as mn,x as xn,y as gn,z as es,G as He,J as Ue,K as We,N as fn,O as yn,Q as jn,R as bn}from"./utils-B8y0nSP-.js";(function(){const s=document.createElement("link").relList;if(s&&s.supports&&s.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))a(i);new MutationObserver(i=>{for(const o of i)if(o.type==="childList")for(const r of o.addedNodes)r.tagName==="LINK"&&r.rel==="modulepreload"&&a(r)}).observe(document,{childList:!0,subtree:!0});function n(i){const o={};return i.integrity&&(o.integrity=i.integrity),i.referrerPolicy&&(o.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?o.credentials="include":i.crossOrigin==="anonymous"?o.credentials="omit":o.credentials="same-origin",o}function a(i){if(i.ep)return;i.ep=!0;const o=n(i);fetch(i.href,o)}})();var ts={exports:{}},Ze={};/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var kn=x,wn=Symbol.for("react.element"),vn=Symbol.for("react.fragment"),Sn=Object.prototype.hasOwnProperty,Cn=kn.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,In={key:!0,ref:!0,__self:!0,__source:!0};function ss(t,s,n){var a,i={},o=null,r=null;n!==void 0&&(o=""+n),s.key!==void 0&&(o=""+s.key),s.ref!==void 0&&(r=s.ref);for(a in s)Sn.call(s,a)&&!In.hasOwnProperty(a)&&(i[a]=s[a]);if(t&&t.defaultProps)for(a in s=t.defaultProps,s)i[a]===void 0&&(i[a]=s[a]);return{$$typeof:wn,type:t,key:o,ref:r,props:i,_owner:Cn.current}}Ze.Fragment=vn;Ze.jsx=ss;Ze.jsxs=ss;ts.exports=Ze;var e=ts.exports,ut={},Et=As;ut.createRoot=Et.createRoot,ut.hydrateRoot=Et.hydrateRoot;const ns="@firebase/installations",yt="0.6.9";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const as=1e4,is=`w:${yt}`,os="FIS_v2",Tn="https://firebaseinstallations.googleapis.com/v1",En=60*60*1e3,Nn="installations",Rn="Installations";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const An={"missing-app-config-values":'Missing App configuration value: "{$valueName}"',"not-registered":"Firebase Installation is not registered.","installation-not-found":"Firebase Installation not found.","request-failed":'{$requestName} request failed with error "{$serverCode} {$serverStatus}: {$serverMessage}"',"app-offline":"Could not process request. Application offline.","delete-pending-registration":"Can't delete installation while there is a pending registration request."},we=new Kt(Nn,Rn,An);function rs(t){return t instanceof mt&&t.code.includes("request-failed")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ls({projectId:t}){return`${Tn}/projects/${t}/installations`}function cs(t){return{token:t.token,requestStatus:2,expiresIn:On(t.expiresIn),creationTime:Date.now()}}async function ds(t,s){const a=(await s.json()).error;return we.create("request-failed",{requestName:t,serverCode:a.code,serverMessage:a.message,serverStatus:a.status})}function us({apiKey:t}){return new Headers({"Content-Type":"application/json",Accept:"application/json","x-goog-api-key":t})}function Dn(t,{refreshToken:s}){const n=us(t);return n.append("Authorization",Mn(s)),n}async function hs(t){const s=await t();return s.status>=500&&s.status<600?t():s}function On(t){return Number(t.replace("s","000"))}function Mn(t){return`${os} ${t}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Ln({appConfig:t,heartbeatServiceProvider:s},{fid:n}){const a=ls(t),i=us(t),o=s.getImmediate({optional:!0});if(o){const d=await o.getHeartbeatsHeader();d&&i.append("x-firebase-client",d)}const r={fid:n,authVersion:os,appId:t.appId,sdkVersion:is},l={method:"POST",headers:i,body:JSON.stringify(r)},u=await hs(()=>fetch(a,l));if(u.ok){const d=await u.json();return{fid:d.fid||n,registrationStatus:2,refreshToken:d.refreshToken,authToken:cs(d.authToken)}}else throw await ds("Create Installation",u)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ps(t){return new Promise(s=>{setTimeout(s,t)})}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function _n(t){return btoa(String.fromCharCode(...t)).replace(/\+/g,"-").replace(/\//g,"_")}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const zn=/^[cdef][\w-]{21}$/,ht="";function Pn(){try{const t=new Uint8Array(17);(self.crypto||self.msCrypto).getRandomValues(t),t[0]=112+t[0]%16;const n=Bn(t);return zn.test(n)?n:ht}catch{return ht}}function Bn(t){return _n(t).substr(0,22)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function et(t){return`${t.appName}!${t.appId}`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ms=new Map;function xs(t,s){const n=et(t);gs(n,s),Fn(n,s)}function gs(t,s){const n=ms.get(t);if(n)for(const a of n)a(s)}function Fn(t,s){const n=Hn();n&&n.postMessage({key:t,fid:s}),Un()}let ye=null;function Hn(){return!ye&&"BroadcastChannel"in self&&(ye=new BroadcastChannel("[Firebase] FID Change"),ye.onmessage=t=>{gs(t.data.key,t.data.fid)}),ye}function Un(){ms.size===0&&ye&&(ye.close(),ye=null)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Wn="firebase-installations-database",$n=1,ve="firebase-installations-store";let it=null;function jt(){return it||(it=Ds(Wn,$n,{upgrade:(t,s)=>{switch(s){case 0:t.createObjectStore(ve)}}})),it}async function Ke(t,s){const n=et(t),i=(await jt()).transaction(ve,"readwrite"),o=i.objectStore(ve),r=await o.get(n);return await o.put(s,n),await i.done,(!r||r.fid!==s.fid)&&xs(t,s.fid),s}async function fs(t){const s=et(t),a=(await jt()).transaction(ve,"readwrite");await a.objectStore(ve).delete(s),await a.done}async function tt(t,s){const n=et(t),i=(await jt()).transaction(ve,"readwrite"),o=i.objectStore(ve),r=await o.get(n),l=s(r);return l===void 0?await o.delete(n):await o.put(l,n),await i.done,l&&(!r||r.fid!==l.fid)&&xs(t,l.fid),l}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function bt(t){let s;const n=await tt(t.appConfig,a=>{const i=qn(a),o=Vn(t,i);return s=o.registrationPromise,o.installationEntry});return n.fid===ht?{installationEntry:await s}:{installationEntry:n,registrationPromise:s}}function qn(t){const s=t||{fid:Pn(),registrationStatus:0};return ys(s)}function Vn(t,s){if(s.registrationStatus===0){if(!navigator.onLine){const i=Promise.reject(we.create("app-offline"));return{installationEntry:s,registrationPromise:i}}const n={fid:s.fid,registrationStatus:1,registrationTime:Date.now()},a=Yn(t,n);return{installationEntry:n,registrationPromise:a}}else return s.registrationStatus===1?{installationEntry:s,registrationPromise:Kn(t)}:{installationEntry:s}}async function Yn(t,s){try{const n=await Ln(t,s);return Ke(t.appConfig,n)}catch(n){throw rs(n)&&n.customData.serverCode===409?await fs(t.appConfig):await Ke(t.appConfig,{fid:s.fid,registrationStatus:0}),n}}async function Kn(t){let s=await Nt(t.appConfig);for(;s.registrationStatus===1;)await ps(100),s=await Nt(t.appConfig);if(s.registrationStatus===0){const{installationEntry:n,registrationPromise:a}=await bt(t);return a||n}return s}function Nt(t){return tt(t,s=>{if(!s)throw we.create("installation-not-found");return ys(s)})}function ys(t){return Gn(t)?{fid:t.fid,registrationStatus:0}:t}function Gn(t){return t.registrationStatus===1&&t.registrationTime+as<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function Xn({appConfig:t,heartbeatServiceProvider:s},n){const a=Jn(t,n),i=Dn(t,n),o=s.getImmediate({optional:!0});if(o){const d=await o.getHeartbeatsHeader();d&&i.append("x-firebase-client",d)}const r={installation:{sdkVersion:is,appId:t.appId}},l={method:"POST",headers:i,body:JSON.stringify(r)},u=await hs(()=>fetch(a,l));if(u.ok){const d=await u.json();return cs(d)}else throw await ds("Generate Auth Token",u)}function Jn(t,{fid:s}){return`${ls(t)}/${s}/authTokens:generate`}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function kt(t,s=!1){let n;const a=await tt(t.appConfig,o=>{if(!js(o))throw we.create("not-registered");const r=o.authToken;if(!s&&ea(r))return o;if(r.requestStatus===1)return n=Qn(t,s),o;{if(!navigator.onLine)throw we.create("app-offline");const l=sa(o);return n=Zn(t,l),l}});return n?await n:a.authToken}async function Qn(t,s){let n=await Rt(t.appConfig);for(;n.authToken.requestStatus===1;)await ps(100),n=await Rt(t.appConfig);const a=n.authToken;return a.requestStatus===0?kt(t,s):a}function Rt(t){return tt(t,s=>{if(!js(s))throw we.create("not-registered");const n=s.authToken;return na(n)?Object.assign(Object.assign({},s),{authToken:{requestStatus:0}}):s})}async function Zn(t,s){try{const n=await Xn(t,s),a=Object.assign(Object.assign({},s),{authToken:n});return await Ke(t.appConfig,a),n}catch(n){if(rs(n)&&(n.customData.serverCode===401||n.customData.serverCode===404))await fs(t.appConfig);else{const a=Object.assign(Object.assign({},s),{authToken:{requestStatus:0}});await Ke(t.appConfig,a)}throw n}}function js(t){return t!==void 0&&t.registrationStatus===2}function ea(t){return t.requestStatus===2&&!ta(t)}function ta(t){const s=Date.now();return s<t.creationTime||t.creationTime+t.expiresIn<s+En}function sa(t){const s={requestStatus:1,requestTime:Date.now()};return Object.assign(Object.assign({},t),{authToken:s})}function na(t){return t.requestStatus===1&&t.requestTime+as<Date.now()}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function aa(t){const s=t,{installationEntry:n,registrationPromise:a}=await bt(s);return a?a.catch(console.error):kt(s).catch(console.error),n.fid}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function ia(t,s=!1){const n=t;return await oa(n),(await kt(n,s)).token}async function oa(t){const{registrationPromise:s}=await bt(t);s&&await s}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ra(t){if(!t||!t.options)throw ot("App Configuration");if(!t.name)throw ot("App Name");const s=["projectId","apiKey","appId"];for(const n of s)if(!t.options[n])throw ot(n);return{appName:t.name,projectId:t.options.projectId,apiKey:t.options.apiKey,appId:t.options.appId}}function ot(t){return we.create("missing-app-config-values",{valueName:t})}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const bs="installations",la="installations-internal",ca=t=>{const s=t.getProvider("app").getImmediate(),n=ra(s),a=Be(s,"heartbeat");return{app:s,appConfig:n,heartbeatServiceProvider:a,_delete:()=>Promise.resolve()}},da=t=>{const s=t.getProvider("app").getImmediate(),n=Be(s,bs).getImmediate();return{getId:()=>aa(n),getToken:i=>ia(n,i)}};function ua(){ze(new Pe(bs,ca,"PUBLIC")),ze(new Pe(la,da,"PRIVATE"))}ua();Re(ns,yt);Re(ns,yt,"esm2017");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ge="analytics",ha="firebase_id",pa="origin",ma=60*1e3,xa="https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig",wt="https://www.googletagmanager.com/gtag/js";/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ie=new Os("@firebase/analytics");/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const ga={"already-exists":"A Firebase Analytics instance with the appId {$id}  already exists. Only one Firebase Analytics instance can be created for each appId.","already-initialized":"initializeAnalytics() cannot be called again with different options than those it was initially called with. It can be called again with the same options to return the existing instance, or getAnalytics() can be used to get a reference to the already-initialized instance.","already-initialized-settings":"Firebase Analytics has already been initialized.settings() must be called before initializing any Analytics instanceor it will have no effect.","interop-component-reg-failed":"Firebase Analytics Interop Component failed to instantiate: {$reason}","invalid-analytics-context":"Firebase Analytics is not supported in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","indexeddb-unavailable":"IndexedDB unavailable or restricted in this environment. Wrap initialization of analytics in analytics.isSupported() to prevent initialization in unsupported environments. Details: {$errorInfo}","fetch-throttle":"The config fetch request timed out while in an exponential backoff state. Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.","config-fetch-failed":"Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}","no-api-key":'The "apiKey" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid API key.',"no-app-id":'The "appId" field is empty in the local Firebase config. Firebase Analytics requires this field tocontain a valid app ID.',"no-client-id":'The "client_id" field is empty.',"invalid-gtag-resource":"Trusted Types detected an invalid gtag resource: {$gtagURL}."},le=new Kt("analytics","Analytics",ga);/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function fa(t){if(!t.startsWith(wt)){const s=le.create("invalid-gtag-resource",{gtagURL:t});return ie.warn(s.message),""}return t}function ks(t){return Promise.all(t.map(s=>s.catch(n=>n)))}function ya(t,s){let n;return window.trustedTypes&&(n=window.trustedTypes.createPolicy(t,s)),n}function ja(t,s){const n=ya("firebase-js-sdk-policy",{createScriptURL:fa}),a=document.createElement("script"),i=`${wt}?l=${t}&id=${s}`;a.src=n?n==null?void 0:n.createScriptURL(i):i,a.async=!0,document.head.appendChild(a)}function ba(t){let s=[];return Array.isArray(window[t])?s=window[t]:window[t]=s,s}async function ka(t,s,n,a,i,o){const r=a[i];try{if(r)await s[r];else{const u=(await ks(n)).find(d=>d.measurementId===i);u&&await s[u.appId]}}catch(l){ie.error(l)}t("config",i,o)}async function wa(t,s,n,a,i){try{let o=[];if(i&&i.send_to){let r=i.send_to;Array.isArray(r)||(r=[r]);const l=await ks(n);for(const u of r){const d=l.find(b=>b.measurementId===u),y=d&&s[d.appId];if(y)o.push(y);else{o=[];break}}}o.length===0&&(o=Object.values(s)),await Promise.all(o),t("event",a,i||{})}catch(o){ie.error(o)}}function va(t,s,n,a){async function i(o,...r){try{if(o==="event"){const[l,u]=r;await wa(t,s,n,l,u)}else if(o==="config"){const[l,u]=r;await ka(t,s,n,a,l,u)}else if(o==="consent"){const[l,u]=r;t("consent",l,u)}else if(o==="get"){const[l,u,d]=r;t("get",l,u,d)}else if(o==="set"){const[l]=r;t("set",l)}else t(o,...r)}catch(l){ie.error(l)}}return i}function Sa(t,s,n,a,i){let o=function(...r){window[a].push(arguments)};return window[i]&&typeof window[i]=="function"&&(o=window[i]),window[i]=va(o,t,s,n),{gtagCore:o,wrappedGtag:window[i]}}function Ca(t){const s=window.document.getElementsByTagName("script");for(const n of Object.values(s))if(n.src&&n.src.includes(wt)&&n.src.includes(t))return n;return null}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ia=30,Ta=1e3;class Ea{constructor(s={},n=Ta){this.throttleMetadata=s,this.intervalMillis=n}getThrottleMetadata(s){return this.throttleMetadata[s]}setThrottleMetadata(s,n){this.throttleMetadata[s]=n}deleteThrottleMetadata(s){delete this.throttleMetadata[s]}}const ws=new Ea;function Na(t){return new Headers({Accept:"application/json","x-goog-api-key":t})}async function Ra(t){var s;const{appId:n,apiKey:a}=t,i={method:"GET",headers:Na(a)},o=xa.replace("{app-id}",n),r=await fetch(o,i);if(r.status!==200&&r.status!==304){let l="";try{const u=await r.json();!((s=u.error)===null||s===void 0)&&s.message&&(l=u.error.message)}catch{}throw le.create("config-fetch-failed",{httpStatus:r.status,responseMessage:l})}return r.json()}async function Aa(t,s=ws,n){const{appId:a,apiKey:i,measurementId:o}=t.options;if(!a)throw le.create("no-app-id");if(!i){if(o)return{measurementId:o,appId:a};throw le.create("no-api-key")}const r=s.getThrottleMetadata(a)||{backoffCount:0,throttleEndTimeMillis:Date.now()},l=new Ma;return setTimeout(async()=>{l.abort()},ma),vs({appId:a,apiKey:i,measurementId:o},r,l,s)}async function vs(t,{throttleEndTimeMillis:s,backoffCount:n},a,i=ws){var o;const{appId:r,measurementId:l}=t;try{await Da(a,s)}catch(u){if(l)return ie.warn(`Timed out fetching this Firebase app's measurement ID from the server. Falling back to the measurement ID ${l} provided in the "measurementId" field in the local Firebase config. [${u==null?void 0:u.message}]`),{appId:r,measurementId:l};throw u}try{const u=await Ra(t);return i.deleteThrottleMetadata(r),u}catch(u){const d=u;if(!Oa(d)){if(i.deleteThrottleMetadata(r),l)return ie.warn(`Failed to fetch this Firebase app's measurement ID from the server. Falling back to the measurement ID ${l} provided in the "measurementId" field in the local Firebase config. [${d==null?void 0:d.message}]`),{appId:r,measurementId:l};throw u}const y=Number((o=d==null?void 0:d.customData)===null||o===void 0?void 0:o.httpStatus)===503?vt(n,i.intervalMillis,Ia):vt(n,i.intervalMillis),b={throttleEndTimeMillis:Date.now()+y,backoffCount:n+1};return i.setThrottleMetadata(r,b),ie.debug(`Calling attemptFetch again in ${y} millis`),vs(t,b,a,i)}}function Da(t,s){return new Promise((n,a)=>{const i=Math.max(s-Date.now(),0),o=setTimeout(n,i);t.addEventListener(()=>{clearTimeout(o),a(le.create("fetch-throttle",{throttleEndTimeMillis:s}))})})}function Oa(t){if(!(t instanceof mt)||!t.customData)return!1;const s=Number(t.customData.httpStatus);return s===429||s===500||s===503||s===504}class Ma{constructor(){this.listeners=[]}addEventListener(s){this.listeners.push(s)}abort(){this.listeners.forEach(s=>s())}}async function La(t,s,n,a,i){if(i&&i.global){t("event",n,a);return}else{const o=await s,r=Object.assign(Object.assign({},a),{send_to:o});t("event",n,r)}}/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */async function _a(){if(Ls())try{await _s()}catch(t){return ie.warn(le.create("indexeddb-unavailable",{errorInfo:t==null?void 0:t.toString()}).message),!1}else return ie.warn(le.create("indexeddb-unavailable",{errorInfo:"IndexedDB is not available in this environment."}).message),!1;return!0}async function za(t,s,n,a,i,o,r){var l;const u=Aa(t);u.then(p=>{n[p.measurementId]=p.appId,t.options.measurementId&&p.measurementId!==t.options.measurementId&&ie.warn(`The measurement ID in the local Firebase config (${t.options.measurementId}) does not match the measurement ID fetched from the server (${p.measurementId}). To ensure analytics events are always sent to the correct Analytics property, update the measurement ID field in the local config or remove it from the local config.`)}).catch(p=>ie.error(p)),s.push(u);const d=_a().then(p=>{if(p)return a.getId()}),[y,b]=await Promise.all([u,d]);Ca(o)||ja(o,y.measurementId),i("js",new Date);const j=(l=r==null?void 0:r.config)!==null&&l!==void 0?l:{};return j[pa]="firebase",j.update=!0,b!=null&&(j[ha]=b),i("config",y.measurementId,j),y.measurementId}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Pa{constructor(s){this.app=s}_delete(){return delete _e[this.app.options.appId],Promise.resolve()}}let _e={},At=[];const Dt={};let rt="dataLayer",Ba="gtag",Ot,Ss,Mt=!1;function Fa(){const t=[];if(Ms()&&t.push("This is a browser extension environment."),Ps()||t.push("Cookies are not available."),t.length>0){const s=t.map((a,i)=>`(${i+1}) ${a}`).join(" "),n=le.create("invalid-analytics-context",{errorInfo:s});ie.warn(n.message)}}function Ha(t,s,n){Fa();const a=t.options.appId;if(!a)throw le.create("no-app-id");if(!t.options.apiKey)if(t.options.measurementId)ie.warn(`The "apiKey" field is empty in the local Firebase config. This is needed to fetch the latest measurement ID for this Firebase app. Falling back to the measurement ID ${t.options.measurementId} provided in the "measurementId" field in the local Firebase config.`);else throw le.create("no-api-key");if(_e[a]!=null)throw le.create("already-exists",{id:a});if(!Mt){ba(rt);const{wrappedGtag:o,gtagCore:r}=Sa(_e,At,Dt,rt,Ba);Ss=o,Ot=r,Mt=!0}return _e[a]=za(t,At,Dt,s,Ot,rt,n),new Pa(t)}function Ua(t=Gt()){t=xt(t);const s=Be(t,Ge);return s.isInitialized()?s.getImmediate():Wa(t)}function Wa(t,s={}){const n=Be(t,Ge);if(n.isInitialized()){const i=n.getImmediate();if(zs(s,n.getOptions()))return i;throw le.create("already-initialized")}return n.initialize({options:s})}function $a(t,s,n,a){t=xt(t),La(Ss,_e[t.app.options.appId],s,n,a).catch(i=>ie.error(i))}const Lt="@firebase/analytics",_t="0.10.8";function qa(){ze(new Pe(Ge,(s,{options:n})=>{const a=s.getProvider("app").getImmediate(),i=s.getProvider("installations-internal").getImmediate();return Ha(a,i,n)},"PUBLIC")),ze(new Pe("analytics-internal",t,"PRIVATE")),Re(Lt,_t),Re(Lt,_t,"esm2017");function t(s){try{const n=s.getProvider(Ge).getImmediate();return{logEvent:(a,i,o)=>$a(n,a,i,o)}}catch(n){throw le.create("interop-component-reg-failed",{reason:n})}}}qa();/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Cs="firebasestorage.googleapis.com",Va="storageBucket",Ya=2*60*1e3,Ka=10*60*1e3;/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class me extends mt{constructor(s,n,a=0){super(lt(s),`Firebase Storage: ${n} (${lt(s)})`),this.status_=a,this.customData={serverResponse:null},this._baseMessage=this.message,Object.setPrototypeOf(this,me.prototype)}get status(){return this.status_}set status(s){this.status_=s}_codeEquals(s){return lt(s)===this.code}get serverResponse(){return this.customData.serverResponse}set serverResponse(s){this.customData.serverResponse=s,this.customData.serverResponse?this.message=`${this._baseMessage}
${this.customData.serverResponse}`:this.message=this._baseMessage}}var pe;(function(t){t.UNKNOWN="unknown",t.OBJECT_NOT_FOUND="object-not-found",t.BUCKET_NOT_FOUND="bucket-not-found",t.PROJECT_NOT_FOUND="project-not-found",t.QUOTA_EXCEEDED="quota-exceeded",t.UNAUTHENTICATED="unauthenticated",t.UNAUTHORIZED="unauthorized",t.UNAUTHORIZED_APP="unauthorized-app",t.RETRY_LIMIT_EXCEEDED="retry-limit-exceeded",t.INVALID_CHECKSUM="invalid-checksum",t.CANCELED="canceled",t.INVALID_EVENT_NAME="invalid-event-name",t.INVALID_URL="invalid-url",t.INVALID_DEFAULT_BUCKET="invalid-default-bucket",t.NO_DEFAULT_BUCKET="no-default-bucket",t.CANNOT_SLICE_BLOB="cannot-slice-blob",t.SERVER_FILE_WRONG_SIZE="server-file-wrong-size",t.NO_DOWNLOAD_URL="no-download-url",t.INVALID_ARGUMENT="invalid-argument",t.INVALID_ARGUMENT_COUNT="invalid-argument-count",t.APP_DELETED="app-deleted",t.INVALID_ROOT_OPERATION="invalid-root-operation",t.INVALID_FORMAT="invalid-format",t.INTERNAL_ERROR="internal-error",t.UNSUPPORTED_ENVIRONMENT="unsupported-environment"})(pe||(pe={}));function lt(t){return"storage/"+t}function Ga(){const t="An unknown error occurred, please check the error payload for server response.";return new me(pe.UNKNOWN,t)}function Xa(){return new me(pe.RETRY_LIMIT_EXCEEDED,"Max retry time for operation exceeded, please try again.")}function Ja(){return new me(pe.CANCELED,"User canceled the upload/download.")}function Qa(t){return new me(pe.INVALID_URL,"Invalid URL '"+t+"'.")}function Za(t){return new me(pe.INVALID_DEFAULT_BUCKET,"Invalid default bucket '"+t+"'.")}function zt(t){return new me(pe.INVALID_ARGUMENT,t)}function Is(){return new me(pe.APP_DELETED,"The Firebase app was deleted.")}function ei(t){return new me(pe.INVALID_ROOT_OPERATION,"The operation '"+t+"' cannot be performed on a root reference, create a non-root reference using child, such as .child('file.png').")}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ce{constructor(s,n){this.bucket=s,this.path_=n}get path(){return this.path_}get isRoot(){return this.path.length===0}fullServerUrl(){const s=encodeURIComponent;return"/b/"+s(this.bucket)+"/o/"+s(this.path)}bucketOnlyServerUrl(){return"/b/"+encodeURIComponent(this.bucket)+"/o"}static makeFromBucketSpec(s,n){let a;try{a=ce.makeFromUrl(s,n)}catch{return new ce(s,"")}if(a.path==="")return a;throw Za(s)}static makeFromUrl(s,n){let a=null;const i="([A-Za-z0-9.\\-_]+)";function o(m){m.path.charAt(m.path.length-1)==="/"&&(m.path_=m.path_.slice(0,-1))}const r="(/(.*))?$",l=new RegExp("^gs://"+i+r,"i"),u={bucket:1,path:3};function d(m){m.path_=decodeURIComponent(m.path)}const y="v[A-Za-z0-9_]+",b=n.replace(/[.]/g,"\\."),j="(/([^?#]*).*)?$",p=new RegExp(`^https?://${b}/${y}/b/${i}/o${j}`,"i"),k={bucket:1,path:3},w=n===Cs?"(?:storage.googleapis.com|storage.cloud.google.com)":n,g="([^?#]*)",f=new RegExp(`^https?://${w}/${i}/${g}`,"i"),D=[{regex:l,indices:u,postModify:o},{regex:p,indices:k,postModify:d},{regex:f,indices:{bucket:1,path:2},postModify:d}];for(let m=0;m<D.length;m++){const N=D[m],v=N.regex.exec(s);if(v){const O=v[N.indices.bucket];let S=v[N.indices.path];S||(S=""),a=new ce(O,S),N.postModify(a);break}}if(a==null)throw Qa(s);return a}}class ti{constructor(s){this.promise_=Promise.reject(s)}getPromise(){return this.promise_}cancel(s=!1){}}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function si(t,s,n){let a=1,i=null,o=null,r=!1,l=0;function u(){return l===2}let d=!1;function y(...g){d||(d=!0,s.apply(null,g))}function b(g){i=setTimeout(()=>{i=null,t(p,u())},g)}function j(){o&&clearTimeout(o)}function p(g,...f){if(d){j();return}if(g){j(),y.call(null,g,...f);return}if(u()||r){j(),y.call(null,g,...f);return}a<64&&(a*=2);let D;l===1?(l=2,D=0):D=(a+Math.random())*1e3,b(D)}let k=!1;function w(g){k||(k=!0,j(),!d&&(i!==null?(g||(l=2),clearTimeout(i),b(0)):g||(l=1)))}return b(0),o=setTimeout(()=>{r=!0,w(!0)},n),w}function ni(t){t(!1)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function ai(t){return t!==void 0}function Pt(t,s,n,a){if(a<s)throw zt(`Invalid value for '${t}'. Expected ${s} or greater.`);if(a>n)throw zt(`Invalid value for '${t}'. Expected ${n} or less.`)}function ii(t){const s=encodeURIComponent;let n="?";for(const a in t)if(t.hasOwnProperty(a)){const i=s(a)+"="+s(t[a]);n=n+i+"&"}return n=n.slice(0,-1),n}var Xe;(function(t){t[t.NO_ERROR=0]="NO_ERROR",t[t.NETWORK_ERROR=1]="NETWORK_ERROR",t[t.ABORT=2]="ABORT"})(Xe||(Xe={}));/**
 * @license
 * Copyright 2022 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function oi(t,s){const n=t>=500&&t<600,i=[408,429].indexOf(t)!==-1,o=s.indexOf(t)!==-1;return n||i||o}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class ri{constructor(s,n,a,i,o,r,l,u,d,y,b,j=!0){this.url_=s,this.method_=n,this.headers_=a,this.body_=i,this.successCodes_=o,this.additionalRetryCodes_=r,this.callback_=l,this.errorCallback_=u,this.timeout_=d,this.progressCallback_=y,this.connectionFactory_=b,this.retry=j,this.pendingConnection_=null,this.backoffId_=null,this.canceled_=!1,this.appDelete_=!1,this.promise_=new Promise((p,k)=>{this.resolve_=p,this.reject_=k,this.start_()})}start_(){const s=(a,i)=>{if(i){a(!1,new $e(!1,null,!0));return}const o=this.connectionFactory_();this.pendingConnection_=o;const r=l=>{const u=l.loaded,d=l.lengthComputable?l.total:-1;this.progressCallback_!==null&&this.progressCallback_(u,d)};this.progressCallback_!==null&&o.addUploadProgressListener(r),o.send(this.url_,this.method_,this.body_,this.headers_).then(()=>{this.progressCallback_!==null&&o.removeUploadProgressListener(r),this.pendingConnection_=null;const l=o.getErrorCode()===Xe.NO_ERROR,u=o.getStatus();if(!l||oi(u,this.additionalRetryCodes_)&&this.retry){const y=o.getErrorCode()===Xe.ABORT;a(!1,new $e(!1,null,y));return}const d=this.successCodes_.indexOf(u)!==-1;a(!0,new $e(d,o))})},n=(a,i)=>{const o=this.resolve_,r=this.reject_,l=i.connection;if(i.wasSuccessCode)try{const u=this.callback_(l,l.getResponse());ai(u)?o(u):o()}catch(u){r(u)}else if(l!==null){const u=Ga();u.serverResponse=l.getErrorText(),this.errorCallback_?r(this.errorCallback_(l,u)):r(u)}else if(i.canceled){const u=this.appDelete_?Is():Ja();r(u)}else{const u=Xa();r(u)}};this.canceled_?n(!1,new $e(!1,null,!0)):this.backoffId_=si(s,n,this.timeout_)}getPromise(){return this.promise_}cancel(s){this.canceled_=!0,this.appDelete_=s||!1,this.backoffId_!==null&&ni(this.backoffId_),this.pendingConnection_!==null&&this.pendingConnection_.abort()}}class $e{constructor(s,n,a){this.wasSuccessCode=s,this.connection=n,this.canceled=!!a}}function li(t,s){s!==null&&s.length>0&&(t.Authorization="Firebase "+s)}function ci(t,s){t["X-Firebase-Storage-Version"]="webjs/"+(s??"AppManager")}function di(t,s){s&&(t["X-Firebase-GMPID"]=s)}function ui(t,s){s!==null&&(t["X-Firebase-AppCheck"]=s)}function hi(t,s,n,a,i,o,r=!0){const l=ii(t.urlParams),u=t.url+l,d=Object.assign({},t.headers);return di(d,s),li(d,n),ci(d,o),ui(d,a),new ri(u,t.method,d,t.body,t.successCodes,t.additionalRetryCodes,t.handler,t.errorHandler,t.timeout,t.progressCallback,i,r)}/**
 * @license
 * Copyright 2017 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */function pi(t){if(t.length===0)return null;const s=t.lastIndexOf("/");return s===-1?"":t.slice(0,s)}function mi(t){const s=t.lastIndexOf("/",t.length-2);return s===-1?t:t.slice(s+1)}/**
 * @license
 * Copyright 2019 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */class Je{constructor(s,n){this._service=s,n instanceof ce?this._location=n:this._location=ce.makeFromUrl(n,s.host)}toString(){return"gs://"+this._location.bucket+"/"+this._location.path}_newRef(s,n){return new Je(s,n)}get root(){const s=new ce(this._location.bucket,"");return this._newRef(this._service,s)}get bucket(){return this._location.bucket}get fullPath(){return this._location.path}get name(){return mi(this._location.path)}get storage(){return this._service}get parent(){const s=pi(this._location.path);if(s===null)return null;const n=new ce(this._location.bucket,s);return new Je(this._service,n)}_throwIfRoot(s){if(this._location.path==="")throw ei(s)}}function Bt(t,s){const n=s==null?void 0:s[Va];return n==null?null:ce.makeFromBucketSpec(n,t)}function xi(t,s,n,a={}){t.host=`${s}:${n}`,t._protocol="http";const{mockUserToken:i}=a;i&&(t._overrideAuthToken=typeof i=="string"?i:Hs(i,t.app.options.projectId))}class gi{constructor(s,n,a,i,o){this.app=s,this._authProvider=n,this._appCheckProvider=a,this._url=i,this._firebaseVersion=o,this._bucket=null,this._host=Cs,this._protocol="https",this._appId=null,this._deleted=!1,this._maxOperationRetryTime=Ya,this._maxUploadRetryTime=Ka,this._requests=new Set,i!=null?this._bucket=ce.makeFromBucketSpec(i,this._host):this._bucket=Bt(this._host,this.app.options)}get host(){return this._host}set host(s){this._host=s,this._url!=null?this._bucket=ce.makeFromBucketSpec(this._url,s):this._bucket=Bt(s,this.app.options)}get maxUploadRetryTime(){return this._maxUploadRetryTime}set maxUploadRetryTime(s){Pt("time",0,Number.POSITIVE_INFINITY,s),this._maxUploadRetryTime=s}get maxOperationRetryTime(){return this._maxOperationRetryTime}set maxOperationRetryTime(s){Pt("time",0,Number.POSITIVE_INFINITY,s),this._maxOperationRetryTime=s}async _getAuthToken(){if(this._overrideAuthToken)return this._overrideAuthToken;const s=this._authProvider.getImmediate({optional:!0});if(s){const n=await s.getToken();if(n!==null)return n.accessToken}return null}async _getAppCheckToken(){const s=this._appCheckProvider.getImmediate({optional:!0});return s?(await s.getToken()).token:null}_delete(){return this._deleted||(this._deleted=!0,this._requests.forEach(s=>s.cancel()),this._requests.clear()),Promise.resolve()}_makeStorageReference(s){return new Je(this,s)}_makeRequest(s,n,a,i,o=!0){if(this._deleted)return new ti(Is());{const r=hi(s,this._appId,a,i,n,this._firebaseVersion,o);return this._requests.add(r),r.getPromise().then(()=>this._requests.delete(r),()=>this._requests.delete(r)),r}}async makeRequestWithTokens(s,n){const[a,i]=await Promise.all([this._getAuthToken(),this._getAppCheckToken()]);return this._makeRequest(s,n,a,i).getPromise()}}const Ft="@firebase/storage",Ht="0.13.2";/**
 * @license
 * Copyright 2020 Google LLC
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *   http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */const Ts="storage";function fi(t=Gt(),s){t=xt(t);const a=Be(t,Ts).getImmediate({identifier:s}),i=Fs("storage");return i&&yi(a,...i),a}function yi(t,s,n,a={}){xi(t,s,n,a)}function ji(t,{instanceIdentifier:s}){const n=t.getProvider("app").getImmediate(),a=t.getProvider("auth-internal"),i=t.getProvider("app-check-internal");return new gi(n,a,i,s,Bs)}function bi(){ze(new Pe(Ts,ji,"PUBLIC").setMultipleInstances(!0)),Re(Ft,Ht,""),Re(Ft,Ht,"esm2017")}bi();const Es={apiKey:"AIzaSyC8GW8S1UFOmCyrKV80n331G3Tm5tfW-fo",authDomain:"fspro-8f755.firebaseapp.com",projectId:"fspro-8f755",storageBucket:"fspro-8f755.firebasestorage.app",messagingSenderId:"83658549317",appId:"1:83658549317:web:fe0b8659df5015b7889b2c",measurementId:"G-301EY8V9S4"},st=Xt(Es),ki=Xt(Es,"secondary");Ua(st);const de=Us(st),Ve=Jt(st),Me=Jt(ki);fi(st);const wi=Object.freeze(Object.defineProperty({__proto__:null,auth:Ve,db:de,secondaryAuth:Me},Symbol.toStringTag,{value:"Module"})),L={USERS:"users",ATTENDANCE:"attendance",PROGRESS_REPORTS:"progressReports",HOLIDAY_REQUESTS:"holidayRequests",TASKS:"tasks",TIME_TABLES:"timeTables",SCHEDULES:"timeTables",COMPENSATION:"compensation",NOTIFICATIONS:"notifications"},z={async create(t,s){try{return(await Gs(Oe(de,t),{...s,createdAt:Le(),updatedAt:Le()})).id}catch(n){throw console.error("Error creating document:",n),n}},async getById(t,s){try{const n=qe(de,t,s),a=await Ks(n);return a.exists()?{id:a.id,...a.data()}:null}catch(n){throw console.error("Error getting document:",n),n}},async getAll(t,s="createdAt"){try{try{const n=nt(Oe(de,t),Ys(s,"desc"));return(await at(n)).docs.map(i=>({id:i.id,...i.data()}))}catch(n){return console.warn(`Ordering by ${s} failed, fetching without order:`,n.message),(await at(Oe(de,t))).docs.map(i=>({id:i.id,...i.data()}))}}catch(n){throw console.error("Error getting documents:",n),n}},async getWhere(t,s,n,a){try{const i=nt(Oe(de,t),St(s,n,a));return(await at(i)).docs.map(r=>({id:r.id,...r.data()}))}catch(i){throw console.error("Error getting documents with condition:",i),i}},async update(t,s,n){try{const a=qe(de,t,s);await Vs(a,{...n,updatedAt:Le()})}catch(a){throw console.error("Error updating document:",a),a}},async delete(t,s){try{const n=qe(de,t,s);await qs(n)}catch(n){throw console.error("Error deleting document:",n),n}},onSnapshot(t,s,n=[]){try{let a=Oe(de,t);return n.forEach(i=>{a=nt(a,St(i.field,i.operator,i.value))}),$s(a,i=>{const o=i.docs.map(r=>({id:r.id,...r.data()}));s(o)})}catch(a){throw console.error("Error setting up real-time listener:",a),a}}},je={async createUser(t){try{const s=qe(de,L.USERS,t.uid);return await Ws(s,{...t,createdAt:Le(),updatedAt:Le()}),t.uid}catch(s){throw console.error("Error creating user:",s),s}},async getUserByEmail(t){try{return(await z.getWhere(L.USERS,"email","==",t))[0]||null}catch(s){return console.error("Error getting user by email:",s),null}},async getUserByUid(t){try{return await z.getById(L.USERS,t)}catch(s){return console.error("Error getting user by UID:",s),null}},async getAllEmployees(){try{return await z.getWhere(L.USERS,"role","==","employee")}catch(t){return console.error("Error getting employees:",t),[]}}},Ut={async checkIn(t,s){return z.create(L.ATTENDANCE,{userId:t,...s,type:"checkin"})},async checkOut(t,s){return z.update(L.ATTENDANCE,t,{...s,type:"checkout"})},async getUserAttendance(t,s){return z.getWhere(L.ATTENDANCE,"userId","==",t)}},Te={async createTask(t){return z.create(L.TASKS,t)},async getUserTasks(t){return z.getWhere(L.TASKS,"assignedTo","==",t)},async getAllTasks(){return z.getAll(L.TASKS,"createdAt")},async updateTaskStatus(t,s,n="",a=""){const i={status:s,reason:n,employeeRemarks:a,updatedAt:new Date().toISOString()};return z.update(L.TASKS,t,i)},async updateTask(t,s){return z.update(L.TASKS,t,s)},async deleteTask(t){return z.delete(L.TASKS,t)}},pt={async createRequest(t){return z.create(L.HOLIDAY_REQUESTS,t)},async getUserRequests(t){return z.getWhere(L.HOLIDAY_REQUESTS,"userId","==",t)},async getAllRequests(){return z.getAll(L.HOLIDAY_REQUESTS,"requestDate")},async updateRequestStatus(t,s,n=""){return z.update(L.HOLIDAY_REQUESTS,t,{status:s,adminRemarks:n})}},Ee={async createNotification(t){return z.create(L.NOTIFICATIONS,t)},async getUserNotifications(t){return z.getWhere(L.NOTIFICATIONS,"userId","==",t)},async getTodayNotifications(t){const s=new Date().toISOString().split("T")[0];return(await z.getWhere(L.NOTIFICATIONS,"userId","==",t)).filter(a=>a.date===s).sort((a,i)=>{var o,r;return new Date(((o=i.createdAt)==null?void 0:o.seconds)*1e3)-new Date(((r=a.createdAt)==null?void 0:r.seconds)*1e3)})},async markAsRead(t){return z.update(L.NOTIFICATIONS,t,{isRead:!0,readAt:new Date().toISOString()})},async markAllAsRead(t){new Date().toISOString().split("T")[0];const a=(await this.getTodayNotifications(t)).filter(i=>!i.isRead).map(i=>this.markAsRead(i.id));return Promise.all(a)},async getUnreadCount(t){return(await this.getTodayNotifications(t)).filter(n=>!n.isRead).length}},Ne={async getUserCompensation(t){return z.getWhere(L.COMPENSATION,"employeeId","==",t)},async createCompensation(t){return z.create(L.COMPENSATION,t)},async checkMissedCheckIns(t){return[]},async getAllCompensation(){return z.getAll(L.COMPENSATION,"createdAt")},async updateCompensationStatus(t,s,n=""){return z.update(L.COMPENSATION,t,{status:s,adminRemarks:n})}},Ns=x.createContext(),Q=()=>{const t=x.useContext(Ns);if(!t)throw new Error("useAuth must be used within an AuthProvider");return t},vi=({children:t})=>{const[s,n]=x.useState(null),[a,i]=x.useState(null),[o,r]=x.useState(!0),[l,u]=x.useState(!1),d=async(p,k)=>{try{try{const g=(await Qs(Ve,p,k)).user,f=await je.getUserByUid(g.uid);if(!f)throw new Error("User profile not found");if(!f.isActive)throw new Error("Account is deactivated");return n(g),i(f),R.success("Logged in successfully!"),{user:g,profile:f}}catch(w){if(w.code==="auth/user-not-found"||w.code==="auth/wrong-password"){console.log("Firebase Auth failed, checking for legacy user...");const g=await je.getUserByEmail(p);if(g&&g.password===k){if(!g.isActive)throw new Error("Account is deactivated");const f={uid:g.uid||g.id,email:g.email};return n(f),i(g),R.success("Logged in successfully! (Legacy Account)"),R.info("Please contact admin to upgrade your account for better security."),{user:f,profile:g}}}throw w}}catch(w){console.error("Login error:",w);let g="Failed to sign in";switch(w.code){case"auth/user-not-found":g="No account found with this email";break;case"auth/wrong-password":g="Incorrect password";break;case"auth/invalid-email":g="Invalid email address";break;case"auth/user-disabled":g="Account has been disabled";break;case"auth/too-many-requests":g="Too many failed attempts. Please try again later";break;default:g=w.message}throw R.error(g),new Error(g)}},y=async()=>{try{await Js(Ve),n(null),i(null),R.success("Logged out successfully!")}catch(p){throw R.error("Error logging out"),p}},b=async p=>{if(p)try{let k=await je.getUserByUid(p.uid);if(k)i(k);else{console.error("No profile found for user:",p.uid),console.log("Creating default admin profile for authenticated user...");const w={uid:p.uid,email:p.email||"<EMAIL>",name:"System Administrator",employeeId:"ADMIN001",role:"admin",isActive:!0};try{await je.createUser(w),console.log("Default admin profile created successfully"),i(w),R.success("Welcome! Admin profile created successfully.")}catch(g){console.error("Error creating default profile:",g),i(null),R.error("Error creating user profile. Please contact support.")}}}catch(k){console.error("Error loading user profile:",k),i(null)}else i(null)};x.useEffect(()=>Xs(Ve,async k=>{l||(n(k),await b(k),r(!1))}),[l]);const j={currentUser:s,userProfile:a,login:d,logout:y,loading:o,setIgnoreAuthChanges:u};return e.jsx(Ns.Provider,{value:j,children:!o&&t})},be={getCurrentWeekId(){const s=ct(new Date,{weekStartsOn:1});return W(s,"yyyy-'W'II")},getCurrentWeekDates(){const t=new Date,s=ct(t,{weekStartsOn:1}),n=Qt(t,{weekStartsOn:1});return{start:s,end:n}},async canEmployeeEditTimetable(t){try{const s=this.getCurrentWeekId(),n=await z.getWhere(L.TIME_TABLES,"userId","==",t);if(n.length===0)return{canEdit:!0,reason:"NO_TIMETABLE"};const a=n[0];return a.weekId===s&&a.setByEmployee?{canEdit:!1,reason:"ALREADY_SET_THIS_WEEK",message:"You have already set your timetable for this week. You cannot modify it until next week."}:a.weekId===s&&a.lastModifiedBy==="system_reset"?{canEdit:!0,reason:"CARRIED_OVER_SCHEDULE",message:"Previous week schedule carried over. You can modify it or save as-is."}:{canEdit:!0,reason:"CAN_EDIT"}}catch(s){return console.error("Error checking timetable edit permission:",s),{canEdit:!1,reason:"ERROR",message:"Error checking permissions"}}},async saveEmployeeTimetable(t,s,n){try{const a=this.getCurrentWeekId(),i=await this.canEmployeeEditTimetable(t);if(!i.canEdit)throw new Error(i.message||"Cannot edit timetable");const o=await z.getWhere(L.TIME_TABLES,"userId","==",t),r={userId:t,employeeId:s.employeeId,employeeName:s.name,schedule:n,weekId:a,setByEmployee:!0,lastModifiedBy:"employee",lastModifiedAt:new Date().toISOString()};return o.length>0?await z.update(L.TIME_TABLES,o[0].id,r):await z.create(L.TIME_TABLES,r),{success:!0}}catch(a){throw console.error("Error saving employee timetable:",a),a}},async saveAdminTimetable(t,s,n,a){try{const i=this.getCurrentWeekId(),o=await z.getWhere(L.TIME_TABLES,"userId","==",t),r={userId:t,employeeId:s.employeeId,employeeName:s.name,schedule:n,weekId:i,setByEmployee:!1,lastModifiedBy:"admin",lastModifiedAt:new Date().toISOString(),modifiedByAdminId:a};let l=!1;return o.length>0?(await z.update(L.TIME_TABLES,o[0].id,r),l=!0):await z.create(L.TIME_TABLES,r),l&&await this.notifyEmployeeOfTimetableChange(t,s.name),{success:!0}}catch(i){throw console.error("Error saving admin timetable:",i),i}},async notifyEmployeeOfTimetableChange(t,s){try{const n={userId:t,title:"Timetable Updated by Admin",message:"Your timetable has been updated by the administrator. Please check your new schedule.",type:"timetable_update",date:W(new Date,"yyyy-MM-dd"),read:!1,priority:"medium"};await Ee.createNotification(n)}catch(n){console.error("Error creating timetable notification:",n)}},async resetTimetablesForNewWeek(){try{const t=this.getCurrentWeekId();console.log("Checking for timetable reset for week:",t);const s=await z.getAll(L.TIME_TABLES);let n=0;for(const a of s)if(a.weekId&&a.weekId!==t){const i={...a,weekId:t,setByEmployee:!1,lastModifiedBy:"system_reset",lastModifiedAt:new Date().toISOString(),schedule:a.schedule||{}};await z.update(L.TIME_TABLES,a.id,i),n++}return console.log(`Reset ${n} timetables for new week with previous schedules carried over`),{resetCount:n}}catch(t){throw console.error("Error resetting timetables for new week:",t),t}},async checkAndPerformWeeklyReset(){try{await this.resetTimetablesForNewWeek()}catch(t){console.error("Error in weekly reset check:",t)}}},Rs=({height:t="auto",width:s="200px",className:n="",style:a={},showFallback:i=!0})=>{const[o,r]=x.useState(!1),[l,u]=x.useState(!1),d=()=>{console.warn("Logo image failed to load, showing fallback"),r(!0)},y=()=>{u(!0)},b=()=>e.jsx("div",{style:{height:t==="auto"?"50px":t,width:s,backgroundColor:"#3498db",borderRadius:"8px",display:"flex",alignItems:"center",justifyContent:"center",color:"white",fontWeight:"bold",fontSize:"18px",fontFamily:"Arial, sans-serif",...a},className:n,title:"FSPro - Employee Management System",children:"FSPro"});return o&&i?e.jsx(b,{}):e.jsxs("div",{className:`logo-container ${n}`,style:{position:"relative",display:"inline-block",width:"100%",maxWidth:s,height:"auto"},children:[e.jsx("img",{src:"/images/fsprologo.png",alt:"FSPro - Employee Management System",style:{height:t,width:"100%",maxWidth:s,opacity:l?1:.7,transition:"opacity 0.3s ease, transform 0.2s ease",display:"block",objectFit:"contain",...a},className:n,onError:d,onLoad:y,loading:"lazy"}),!l&&!o&&e.jsx("div",{style:{position:"absolute",top:0,left:0,height:t==="auto"?"50px":t,width:s,backgroundColor:"#f8f9fa",borderRadius:"4px",display:"flex",alignItems:"center",justifyContent:"center",animation:"pulse 1.5s ease-in-out infinite"},children:e.jsx("div",{style:{width:"60%",height:"60%",backgroundColor:"#e9ecef",borderRadius:"2px"}})})]})},Si=()=>{const[t,s]=x.useState({email:"",password:""}),[n,a]=x.useState(!1),[i,o]=x.useState(!1),[r,l]=x.useState(""),[u,d]=x.useState(!1),{login:y}=Q(),b=p=>{s({...t,[p.target.name]:p.target.value})},j=async p=>{p.preventDefault(),a(!0),l(""),console.log("Login attempt:",t.email);try{const k=await y(t.email,t.password);console.log("Login successful:",k)}catch(k){console.error("Login failed:",k),l("Invalid email or password. Please try again.")}finally{a(!1)}};return e.jsxs("div",{className:"login-container",style:{position:"relative",overflow:"hidden"},children:[e.jsx("div",{style:{position:"absolute",top:0,left:0,right:0,bottom:0,display:"flex",alignItems:"center",justifyContent:"center",pointerEvents:"none",zIndex:1},children:e.jsx("span",{className:"fspro-bg-text",children:"FSPro"})}),e.jsxs("div",{className:"login-card",style:{boxShadow:"0 10px 32px rgba(52, 78, 123, 0.15)",position:"relative",zIndex:2,padding:"32px 28px 28px 28px",maxWidth:380,width:"100%",margin:"20px 0"},children:[e.jsxs("div",{className:"text-center mb-20",children:[e.jsx("div",{style:{display:"flex",justifyContent:"center",marginBottom:"20px",padding:"0 20px"},children:e.jsx(Rs,{height:"auto",width:"280px",style:{maxHeight:"90px",maxWidth:"100%",objectFit:"contain",filter:"drop-shadow(0 4px 12px rgba(0,0,0,0.15))"}})}),e.jsx("h1",{className:"login-title",style:{fontWeight:800,letterSpacing:1,fontSize:"2rem",marginBottom:6,marginTop:0},children:"Login"}),e.jsx("div",{style:{width:"60px",height:"3px",background:"linear-gradient(135deg, #000000 0%, #1a237e 50%, #1E88E5 100%)",borderRadius:2,margin:"0 auto 12px auto"}}),e.jsx("div",{style:{background:"linear-gradient(135deg, #000000 0%, #1a237e 50%, #1E88E5 100%)",color:"#ffffff !important",fontWeight:700,fontSize:"1rem",letterSpacing:"0.04em",marginBottom:20,textShadow:"0 2px 8px rgba(0,0,0,0.2)",textTransform:"uppercase",fontFamily:"Segoe UI, Arial, sans-serif",padding:"6px 12px",borderRadius:"6px",display:"inline-block"},children:e.jsx("span",{style:{color:"#ffffff"},children:"Employee Management System"})})]}),r&&e.jsx("div",{style:{background:"#ffeaea",color:"#c0392b",padding:"10px",borderRadius:"6px",marginBottom:"18px",textAlign:"center",fontSize:"14px"},children:r}),e.jsxs("form",{onSubmit:j,autoComplete:"on",children:[e.jsxs("div",{className:"form-group",style:{position:"relative",marginBottom:"20px"},children:[e.jsx("input",{type:"email",name:"email",value:t.email,onChange:b,className:"form-input",required:!0,placeholder:" ","aria-label":"Email",autoFocus:!0,style:{paddingTop:"18px",paddingBottom:"8px"},autoComplete:"email"}),e.jsx("label",{className:"form-label",htmlFor:"email",style:{position:"absolute",left:"12px",top:t.email?"2px":"14px",fontSize:t.email?"12px":"15px",color:t.email?"#1a237e":"#333",background:"white",padding:"0 4px",transition:"all 0.2s",pointerEvents:"none"},children:"Email"})]}),e.jsxs("div",{className:"form-group",style:{position:"relative",marginBottom:"16px"},children:[e.jsx("input",{type:i?"text":"password",name:"password",value:t.password,onChange:b,className:"form-input",required:!0,placeholder:" ","aria-label":"Password",style:{paddingTop:"18px",paddingBottom:"8px"},autoComplete:"current-password"}),e.jsx("label",{className:"form-label",htmlFor:"password",style:{position:"absolute",left:"12px",top:t.password?"2px":"14px",fontSize:t.password?"12px":"15px",color:t.password?"#1a237e":"#333",background:"white",padding:"0 4px",transition:"all 0.2s",pointerEvents:"none"},children:"Password"}),e.jsx("button",{type:"button","aria-label":i?"Hide password":"Show password",onClick:()=>o(p=>!p),style:{position:"absolute",right:"12px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#888",padding:0},tabIndex:-1,children:i?e.jsx(Zt,{size:20}):e.jsx(fe,{size:20})})]}),e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"18px"},children:[e.jsxs("label",{style:{display:"flex",alignItems:"center",fontSize:"14px",color:"#1a237e",fontWeight:600,cursor:"pointer"},children:[e.jsx("input",{type:"checkbox",checked:u,onChange:p=>d(p.target.checked),style:{marginRight:"7px"}}),"Remember me"]}),e.jsx("a",{href:"#",style:{color:"#1a237e",fontSize:"14px",textDecoration:"none"},tabIndex:0,children:"Forgot password?"})]}),e.jsxs("button",{type:"submit",className:"btn btn-primary modern-login-btn",style:{width:"100%",display:"flex",alignItems:"center",justifyContent:"center",gap:"8px",fontWeight:600,fontSize:"16px",letterSpacing:.5,marginTop:4},disabled:n,"aria-busy":n,children:[n?e.jsx(Zs,{size:20,className:"spin"}):e.jsx(en,{size:20}),n?"Logging in...":"Login"]})]})]})]})},Ci=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState(0),[o,r]=x.useState(!1),[l,u]=x.useState(!1),d=x.useRef(null);x.useEffect(()=>{t!=null&&t.uid&&(y(),b())},[t]),x.useEffect(()=>{const g=f=>{d.current&&!d.current.contains(f.target)&&r(!1)};return document.addEventListener("mousedown",g),()=>document.removeEventListener("mousedown",g)},[]);const y=async()=>{try{u(!0);const g=await Ee.getTodayNotifications(t.uid);n(g)}catch(g){console.error("Error loading notifications:",g)}finally{u(!1)}},b=async()=>{try{const g=await Ee.getUnreadCount(t.uid);i(g)}catch(g){console.error("Error loading unread count:",g)}},j=async g=>{try{await Ee.markAsRead(g),n(f=>f.map(E=>E.id===g?{...E,isRead:!0}:E)),i(f=>Math.max(0,f-1))}catch(f){console.error("Error marking notification as read:",f),R.error("Failed to mark notification as read")}},p=async()=>{try{await Ee.markAllAsRead(t.uid),n(g=>g.map(f=>({...f,isRead:!0}))),i(0),R.success("All notifications marked as read")}catch(g){console.error("Error marking all notifications as read:",g),R.error("Failed to mark all notifications as read")}},k=g=>{switch(g){case"task_assigned":return e.jsx(ae,{size:16,style:{color:"#f39c12"}});case"task_completed":return e.jsx(te,{size:16,style:{color:"#27ae60"}});case"holiday_approved":return e.jsx(te,{size:16,style:{color:"#27ae60"}});case"holiday_rejected":return e.jsx(De,{size:16,style:{color:"#e74c3c"}});case"report_submitted":return e.jsx(Tt,{size:16,style:{color:"#3498db"}});case"late_checkin":return e.jsx(V,{size:16,style:{color:"#f39c12"}});default:return e.jsx(Tt,{size:16,style:{color:"#3498db"}})}},w=g=>{if(!g)return"";const f=g.seconds?new Date(g.seconds*1e3):new Date(g);return W(f,"HH:mm")};return e.jsxs("div",{className:"notification-dropdown",ref:d,style:{position:"relative",display:"inline-block"},children:[e.jsxs("button",{onClick:()=>r(!o),style:{position:"relative",background:o?"#f0f0f0":"none",border:"1px solid #ddd",cursor:"pointer",padding:"8px",borderRadius:"6px",display:"flex",alignItems:"center",justifyContent:"center"},children:[e.jsx(It,{size:20,style:{color:"#666"}}),a>0&&e.jsx("span",{style:{position:"absolute",top:"2px",right:"2px",backgroundColor:"#e74c3c",color:"white",borderRadius:"50%",width:"18px",height:"18px",fontSize:"10px",display:"flex",alignItems:"center",justifyContent:"center",fontWeight:"bold"},children:a>9?"9+":a})]}),o&&e.jsxs("div",{style:{position:"absolute",top:"100%",right:"0",backgroundColor:"white",border:"2px solid #e74c3c",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0,0,0,0.3)",zIndex:99999,width:"350px",maxHeight:"400px",overflowY:"auto",marginTop:"5px"},onClick:g=>g.stopPropagation(),children:[e.jsxs("div",{style:{padding:"15px",borderBottom:"1px solid #eee",display:"flex",justifyContent:"space-between",alignItems:"center"},children:[e.jsx("h4",{style:{margin:0,fontSize:"16px",color:"#333"},children:"Today's Notifications"}),a>0&&e.jsxs("button",{onClick:p,style:{background:"none",border:"none",color:"#3498db",cursor:"pointer",fontSize:"12px",display:"flex",alignItems:"center",gap:"4px"},children:[e.jsx(tn,{size:14}),"Mark all read"]})]}),e.jsx("div",{style:{maxHeight:"300px",overflowY:"auto"},children:l?e.jsx("div",{style:{padding:"20px",textAlign:"center",color:"#666"},children:"Loading notifications..."}):s.length===0?e.jsxs("div",{style:{padding:"20px",textAlign:"center",color:"#666"},children:[e.jsx(It,{size:32,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{style:{margin:0,fontSize:"14px"},children:"No notifications today"})]}):s.map(g=>e.jsx("div",{style:{padding:"12px 15px",borderBottom:"1px solid #f5f5f5",backgroundColor:g.isRead?"white":"#f8f9ff",cursor:g.isRead?"default":"pointer"},onClick:()=>!g.isRead&&j(g.id),children:e.jsxs("div",{style:{display:"flex",alignItems:"flex-start",gap:"10px"},children:[e.jsx("div",{style:{marginTop:"2px"},children:k(g.type)}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontSize:"14px",fontWeight:g.isRead?"normal":"bold",color:"#333",marginBottom:"4px"},children:g.title}),e.jsx("div",{style:{fontSize:"12px",color:"#666",marginBottom:"4px"},children:g.message}),e.jsx("div",{style:{fontSize:"11px",color:"#999"},children:w(g.createdAt)})]}),!g.isRead&&e.jsx("div",{style:{width:"8px",height:"8px",backgroundColor:"#3498db",borderRadius:"50%",marginTop:"6px"}})]})},g.id))})]})]})},Ii=({onMobileMenuToggle:t})=>{const{userProfile:s}=Q();return e.jsxs("div",{className:"navbar",children:[e.jsxs("div",{className:"navbar-left",children:[e.jsx("button",{className:"mobile-menu-btn",onClick:t,"aria-label":"Toggle mobile menu",children:e.jsx(sn,{size:24})}),e.jsx("div",{className:"navbar-title",children:e.jsx("h1",{style:{fontSize:"24px",fontWeight:"600",color:"#333",margin:0},children:"Dashboard"})})]}),e.jsx("div",{className:"flex align-center gap-10",children:e.jsxs("div",{className:"flex align-center gap-10",children:[e.jsx(Ci,{}),e.jsxs("div",{className:"flex align-center gap-10",children:[e.jsx(gt,{size:20,style:{color:"#666"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontSize:"14px",fontWeight:"500"},children:(s==null?void 0:s.name)||(s==null?void 0:s.displayName)||(s==null?void 0:s.email)||"Unknown User"}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:(s==null?void 0:s.role)==="admin"?"Administrator":"Employee"})]})]})]})})]})},Ti=({activeTab:t,setActiveTab:s,isMobileOpen:n,onMobileClose:a})=>{const{userProfile:i,logout:o}=Q(),d=(i==null?void 0:i.role)==="admin"?[{id:"admin-dashboard",label:"Admin Dashboard",icon:nn},{id:"employees",label:"Employee Management",icon:ue},{id:"admin-timetable",label:"Time Table Management",icon:se},{id:"admin-progress-reports",label:"Progress Reports",icon:he},{id:"attendance-records",label:"Attendance Records",icon:V},{id:"holiday-management",label:"Holiday Management",icon:se},{id:"task-management",label:"Task Management",icon:ke},{id:"compensation-records",label:"Compensation Records",icon:V}]:[{id:"dashboard",label:"Dashboard",icon:an},{id:"checkin",label:"Check In/Out",icon:V},{id:"timetable",label:"Time Table",icon:se},{id:"progress",label:"Progress Report",icon:he},{id:"holidays",label:"Holiday Requests",icon:se},{id:"tasks",label:"My Tasks",icon:ke},{id:"compensation",label:"Compensate Hours",icon:V}],y=async()=>{try{await o()}catch(b){console.error("Logout error:",b)}};return e.jsxs("div",{className:`sidebar ${n?"mobile-open":""}`,children:[e.jsxs("div",{style:{padding:"0 15px",marginBottom:"20px"},children:[e.jsx("div",{style:{display:"flex",alignItems:"center",marginBottom:"0px",padding:"0px 0 0 0",justifyContent:"center"},children:e.jsx(Rs,{height:"auto",width:"240px",style:{maxHeight:"110px",maxWidth:"100%",objectFit:"contain",filter:"drop-shadow(0 2px 6px rgba(0,0,0,0.1))"}})}),e.jsxs("div",{style:{fontSize:"14px",opacity:.8,textAlign:"center",marginTop:"0px",paddingTop:"0px"},children:[i==null?void 0:i.name," | ID: ",i==null?void 0:i.employeeId]})]}),e.jsxs("nav",{children:[d.map(b=>{const j=b.icon;return e.jsx("div",{className:`nav-item ${t===b.id?"active":""}`,onClick:()=>s(b.id),children:e.jsxs("a",{href:"#",onClick:p=>p.preventDefault(),children:[e.jsx(j,{size:18}),b.label]})},b.id)}),e.jsx("div",{className:"nav-item",onClick:y,style:{marginTop:"20px"},children:e.jsxs("a",{href:"#",onClick:b=>b.preventDefault(),children:[e.jsx(on,{size:18}),"Logout"]})})]})]})},Wt=()=>{const{userProfile:t}=Q(),[s,n]=x.useState({totalCheckIns:0,totalCheckOuts:0,lateCheckIns:0,totalAbsences:0,unapprovedLeaves:0,totalEmployees:0}),[a,i]=x.useState(!0);x.useEffect(()=>{o()},[t]);const o=async()=>{try{if((t==null?void 0:t.role)==="admin"){const[l,u,d]=await Promise.all([z.getAll(L.ATTENDANCE),z.getAll(L.HOLIDAY_REQUESTS),z.getAll(L.USERS)]),y=l.filter(k=>k.type==="checkin").length,b=l.filter(k=>k.type==="checkout").length,j=u.filter(k=>k.status==="pending").length,p=d.filter(k=>k.role==="employee").length;n({totalCheckIns:y,totalCheckOuts:b,lateCheckIns:0,totalAbsences:0,unapprovedLeaves:j,totalEmployees:p})}else{const[l,u,d]=await Promise.all([z.getWhere(L.ATTENDANCE,"userId","==",t.uid),z.getWhere(L.HOLIDAY_REQUESTS,"userId","==",t.uid),z.getWhere(L.TASKS,"assignedTo","==",t.uid)]),y=l.filter(p=>p.type==="checkin").length,b=l.filter(p=>p.type==="checkout").length,j=u.filter(p=>p.status==="pending").length;n({totalCheckIns:y,totalCheckOuts:b,lateCheckIns:0,totalAbsences:0,unapprovedLeaves:j,totalTasks:d.length})}}catch(l){console.error("Error loading dashboard stats:",l)}finally{i(!1)}},r=({icon:l,title:u,value:d,color:y="#3498db"})=>e.jsxs("div",{className:"stat-card",children:[e.jsx(l,{size:32,style:{color:y,marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:y},children:a?"...":d}),e.jsx("div",{className:"stat-label",children:u})]});return e.jsxs("div",{className:"content",children:[e.jsx("h2",{style:{marginBottom:"30px",color:"#333"},children:(t==null?void 0:t.role)==="admin"?"Admin Dashboard":"Employee Dashboard"}),e.jsx("div",{className:"stats-grid",children:(t==null?void 0:t.role)==="admin"?e.jsxs(e.Fragment,{children:[e.jsx(r,{icon:ue,title:"Total Employees",value:s.totalEmployees,color:"#27ae60"}),e.jsx(r,{icon:te,title:"Total Check-Ins",value:s.totalCheckIns,color:"#3498db"}),e.jsx(r,{icon:re,title:"Total Check-Outs",value:s.totalCheckOuts,color:"#9b59b6"}),e.jsx(r,{icon:Ae,title:"Late Check-Ins",value:s.lateCheckIns,color:"#e74c3c"}),e.jsx(r,{icon:se,title:"Unapproved Leaves",value:s.unapprovedLeaves,color:"#f39c12"})]}):e.jsxs(e.Fragment,{children:[e.jsx(r,{icon:te,title:"Total Check-Ins",value:s.totalCheckIns,color:"#3498db"}),e.jsx(r,{icon:re,title:"Total Check-Outs",value:s.totalCheckOuts,color:"#9b59b6"}),e.jsx(r,{icon:Ae,title:"Late Check-Ins",value:s.lateCheckIns,color:"#e74c3c"}),e.jsx(r,{icon:se,title:"Pending Leaves",value:s.unapprovedLeaves,color:"#f39c12"})]})}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Recent Activity"}),e.jsxs("div",{style:{color:"#666",textAlign:"center",padding:"40px"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"Recent activity will appear here"})]})]})]})};class Y{static timeToMinutes(s){if(!s)return 0;const n=s.split(":"),a=parseInt(n[0])||0,i=parseInt(n[1])||0;return a*60+i}static minutesToTime(s){const n=Math.floor(s/60),a=s%60;return`${n.toString().padStart(2,"0")}:${a.toString().padStart(2,"0")}`}static getCurrentDay(){return W(new Date,"EEEE")}static getCurrentTime(){return W(new Date,"HH:mm:ss")}static hasSchedule(s){if(!s)return console.log("No schedule provided"),!1;const n=this.getCurrentDay();return console.log("Checking schedule for day:",{currentDay:n,scheduleKeys:Object.keys(s),scheduleForToday:s[n],hasSlots:s[n]&&s[n].length>0}),s[n]&&s[n].length>0}static getTodaySchedule(s){if(!s)return console.log("No schedule provided to getTodaySchedule"),[];const n=this.getCurrentDay(),a=s[n]||[];return console.log("Getting today's schedule:",{currentDay:n,scheduleKeys:Object.keys(s),todaySlots:a,slotsCount:a.length}),a}static getNextScheduledSlot(s,n){const a=this.getTodaySchedule(s),i=this.timeToMinutes(this.getCurrentTime());if(a.length===0)return null;const o=this.getCompletedSlots(n);for(let r=0;r<a.length;r++){const l=a[r],u=this.timeToMinutes(l.checkIn);this.timeToMinutes(l.checkOut);const d=o.some(w=>w.slotIndex===r),y=n.some(w=>w.type==="checkin"&&(w.slotIndex===r||Math.abs(this.timeToMinutes(w.scheduledCheckIn||w.checkInTime)-u)<15)&&!n.some(g=>g.type==="checkout"&&(g.slotIndex===r||g.checkInTime===w.checkInTime)&&new Date(g.timestamp)>new Date(w.timestamp))),b=u+120,j=i<=b,p=u+30,k=i>p;if(!d&&!y&&j)return{...l,slotIndex:r,scheduledCheckIn:l.checkIn,scheduledCheckOut:l.checkOut,hasMissedRegularWindow:k}}return null}static getCompletedSlots(s){const n=s.filter(o=>o.type==="checkin"),a=s.filter(o=>o.type==="checkout"),i=[];return n.forEach(o=>{const r=a.find(l=>o.slotIndex!==void 0&&l.slotIndex!==void 0?l.slotIndex===o.slotIndex&&new Date(l.timestamp)>new Date(o.timestamp):l.checkInTime===o.checkInTime);r&&i.push({slotIndex:o.slotIndex,scheduledCheckIn:o.scheduledCheckIn||o.checkInTime,scheduledCheckOut:r.scheduledCheckOut||r.checkOutTime,checkInTime:o.checkInTime,checkOutTime:r.checkOutTime})}),i}static isCurrentlyCheckedIn(s){const n=s.filter(i=>i.type==="checkin").length,a=s.filter(i=>i.type==="checkout").length;return n>a}static getLastCheckIn(s){const n=s.filter(a=>a.type==="checkin");return n.length>0?n[n.length-1]:null}static validateRegularCheckIn(s,n){if(!this.hasSchedule(s))return{allowed:!1,reason:"NO_SCHEDULE",message:"📅 Please set up your work schedule in the Timetable section first to start checking in"};if(this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"ALREADY_CHECKED_IN",message:"✅ You are already checked in for this session. Please check out first before checking in again"};if(this.getRecentCheckIns(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKIN",message:"⏰ Please wait 10 minutes between check-in attempts to prevent duplicate entries"};const i=this.getNextScheduledSlot(s,n);if(!i){const y=this.getTodaySchedule(s);return this.getCompletedSlots(n).length===y.length?{allowed:!1,reason:"ALL_SLOTS_COMPLETED",message:"🎯 All your scheduled work sessions for today are complete. Great job!"}:{allowed:!1,reason:"MISSED_SLOTS",message:"❌ You have missed your scheduled work sessions for today. Please contact your administrator if needed."}}if(n.find(y=>y.type==="checkin"&&y.isLate===!0&&y.slotIndex===i.slotIndex))return{allowed:!1,reason:"LATE_CHECKIN_DONE",message:"Late check-in already completed for this slot"};const r=this.timeToMinutes(this.getCurrentTime()),l=this.timeToMinutes(i.scheduledCheckIn),u=l-60,d=l+30;if(i.hasMissedRegularWindow)return{allowed:!1,reason:"MISSED_REGULAR_WINDOW",message:`You missed the regular check-in window for slot ${i.slotIndex+1}. Please use late check-in option.`};if(r<u)return{allowed:!1,reason:"TOO_EARLY",message:`⏰ Check-in window opens at ${this.minutesToTime(u)} (1 hour before your scheduled time)`};if(r>d){const y=r-l,b=Math.floor(y/60),j=y%60;return{allowed:!1,reason:"TOO_LATE",message:`🚨 You are ${b>0?`${b}h `:""}${j}m late. Please use the "Late Check-in" button and provide a reason`}}return{allowed:!0,slot:i,message:"Regular check-in allowed"}}static validateLateCheckIn(s,n){if(!this.hasSchedule(s))return{allowed:!1,reason:"NO_SCHEDULE",message:"📅 Please set up your work schedule in the Timetable section first to start checking in"};if(this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"ALREADY_CHECKED_IN",message:"You are already checked in"};if(this.getRecentCheckIns(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKIN",message:"Multiple check-ins within 10 minutes are not allowed"};const i=this.getNextScheduledSlot(s,n);if(!i){const d=this.getTodaySchedule(s);return this.getCompletedSlots(n).length===d.length?{allowed:!1,reason:"ALL_SLOTS_COMPLETED",message:"All your scheduled work sessions for today are complete"}:{allowed:!1,reason:"MISSED_SLOTS",message:"You have missed your scheduled work sessions for today"}}const o=this.timeToMinutes(this.getCurrentTime()),r=this.timeToMinutes(i.scheduledCheckIn),l=r+30,u=r+120;if(o>u)return{allowed:!1,reason:"TOO_LATE_FOR_LATE",message:"❌ Late check-in window has expired (maximum 2 hours after scheduled time). Please contact your administrator"};if(o<=l)return{allowed:!1,reason:"USE_REGULAR",message:"Use regular check-in option - you are not late yet"};if(o>l&&o<=u){const d=o-r,y=Math.floor(d/60),b=d%60;return{allowed:!0,slot:i,message:`Late check-in allowed (${y>0?`${y}h `:""}${b}m late)`}}return{allowed:!1,reason:"UNKNOWN",message:"Unable to determine late check-in eligibility"}}static validateRegularCheckOut(s,n){if(!this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"NOT_CHECKED_IN",message:"📝 Please check in first before you can check out"};const a=this.getLastCheckIn(n);if(!a)return{allowed:!1,reason:"NO_CHECKIN_FOUND",message:"No check-in record found"};if(this.getRecentCheckOuts(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKOUT",message:"⏰ Please wait 10 minutes between check-out attempts to prevent duplicate entries"};const o=this.timeToMinutes(this.getCurrentTime()),r=this.timeToMinutes(a.checkInTime);return o<=r?{allowed:!1,reason:"INVALID_TIME",message:"⚠️ Check-out time must be after your check-in time. Please wait a moment and try again"}:{allowed:!0,lastCheckIn:a,message:"Regular check-out allowed"}}static validateLateCheckOut(s,n){if(!this.isCurrentlyCheckedIn(n))return{allowed:!1,reason:"NOT_CHECKED_IN",message:"📝 Please check in first before you can check out"};const a=this.getLastCheckIn(n);if(!a)return{allowed:!1,reason:"NO_CHECKIN_FOUND",message:"No check-in record found"};if(this.getRecentCheckOuts(n,10).length>0)return{allowed:!1,reason:"RECENT_CHECKOUT",message:"Multiple check-outs within 10 minutes are not allowed"};const o=this.timeToMinutes(this.getCurrentTime()),r=this.timeToMinutes(a.checkInTime);if(o<=r)return{allowed:!1,reason:"INVALID_TIME",message:"Check-out time must be after check-in time"};const l=a.scheduledCheckOut;if(l){const d=this.timeToMinutes(l)+30;if(o<d)return{allowed:!1,reason:"NOT_LATE_YET",message:"✅ Please use the regular check-out button as you are not working overtime yet"}}return{allowed:!0,lastCheckIn:a,message:"Late check-out allowed"}}static getRecentCheckOuts(s,n){const a=this.timeToMinutes(this.getCurrentTime());return s.filter(o=>o.type==="checkout").filter(o=>{const r=this.timeToMinutes(o.checkOutTime);return a-r<=n})}static getRecentCheckIns(s,n){const a=this.timeToMinutes(this.getCurrentTime());return s.filter(o=>o.type==="checkin").filter(o=>{const r=this.timeToMinutes(o.checkInTime);return a-r<=n})}static calculateWorkingHours(s,n){if(!s||!n)return{hours:0,minutes:0,total:0,display:"Incomplete"};const a=this.timeToMinutes(s);let o=this.timeToMinutes(n)-a;if(o<0)return{hours:0,minutes:0,total:0,display:"Error: Invalid time range"};const r=Math.floor(o/60),l=o%60,u=o/60;return{hours:r,minutes:l,total:u,display:`${r}h ${l}m`}}static canUseCompensationHours(s,n){return W(new Date,"yyyy-MM-dd"),s.length>0?{allowed:!1,reason:"HAS_RECORDS",message:"Compensation hours can only be used for completely missed days"}:this.hasSchedule(n)?{allowed:!0,message:"Compensation hours can be used"}:{allowed:!1,reason:"NO_SCHEDULE",message:"No schedule set for today"}}}class Ei{static calculateDayWorkingHours(s){if(!s||s.length===0)return{regularHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},lateHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},totalHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},pairs:[],status:"NO_RECORDS"};const n=s.filter(j=>j.type==="checkin"),a=s.filter(j=>j.type==="checkout"),i=this.pairCheckInOuts(n,a),o=i.filter(j=>!j.isLate),r=i.filter(j=>j.isLate),l=this.calculatePairsHours(o),u=this.calculatePairsHours(r),d=l.totalMinutes+u.totalMinutes,y={hours:Math.floor(d/60),minutes:d%60,totalMinutes:d,display:`${Math.floor(d/60)}h ${d%60}m`};let b="COMPLETE";return(n.length!==a.length||i.some(j=>!j.complete))&&(b="INCOMPLETE"),{regularHours:l,lateHours:u,totalHours:y,pairs:i,status:b,breakdown:{regularPairs:o.length,latePairs:r.length,totalPairs:i.length}}}static pairCheckInOuts(s,n){const a=[],i=[...s].sort((r,l)=>Y.timeToMinutes(r.checkInTime)-Y.timeToMinutes(l.checkInTime)),o=[...n].sort((r,l)=>Y.timeToMinutes(r.checkOutTime)-Y.timeToMinutes(l.checkOutTime));for(let r=0;r<i.length;r++){const l=i[r],u=o[r];u?a.push({checkIn:l,checkOut:u,complete:!0,isLate:l.isLate||l.lateReason||u.isLate||u.lateReason,workingHours:Y.calculateWorkingHours(l.checkInTime,u.checkOutTime)}):a.push({checkIn:l,checkOut:null,complete:!1,isLate:l.isLate||l.lateReason,workingHours:{hours:0,minutes:0,total:0,display:"Incomplete"}})}return a}static calculatePairsHours(s){let n=0,a=0;return s.forEach(i=>{i.complete&&i.workingHours.total>0&&(n+=i.workingHours.hours*60+i.workingHours.minutes,a++)}),{hours:Math.floor(n/60),minutes:n%60,totalMinutes:n,display:`${Math.floor(n/60)}h ${n%60}m`,completePairs:a}}static formatWorkingHoursDisplay(s){const{regularHours:n,lateHours:a,totalHours:i,status:o}=s;return o==="NO_RECORDS"?"No records":o==="INCOMPLETE"?"Incomplete":a.totalMinutes===0?n.display:n.totalMinutes===0?`${a.display} (Late)`:`${n.display} + ${a.display} = ${i.display}`}static calculateScheduledHours(s,n){if(!s||!s[n])return{hours:0,minutes:0,display:"0h 0m"};const a=s[n];let i=0;return a.forEach(o=>{if(o.checkIn&&o.checkOut){const r=Y.calculateWorkingHours(o.checkIn,o.checkOut);r.total>0&&(i+=r.hours*60+r.minutes)}}),{hours:Math.floor(i/60),minutes:i%60,totalMinutes:i,display:`${Math.floor(i/60)}h ${i%60}m`}}static compareHours(s,n){const a=s.totalMinutes||0,i=n.totalMinutes||0,o=a-i;let r="EXACT";o>0?r="OVERTIME":o<0&&(r="UNDERTIME");const l=Math.floor(Math.abs(o)/60),u=Math.abs(o)%60;return{status:r,difference:{hours:l,minutes:u,totalMinutes:Math.abs(o),display:`${l}h ${u}m`},percentage:i>0?a/i*100:0}}static validateWorkingHours(s){const n=[],a=[];if(!s||s.length===0)return{valid:!0,errors:n,warnings:a};const i=s.filter(r=>r.type==="checkin"),o=s.filter(r=>r.type==="checkout");i.length!==o.length&&n.push("Unequal number of check-ins and check-outs"),i.forEach((r,l)=>{const u=o[l];if(u){const d=Y.timeToMinutes(r.checkInTime);Y.timeToMinutes(u.checkOutTime)<=d&&n.push(`Invalid time range: ${r.checkInTime} to ${u.checkOutTime}`)}});for(let r=0;r<i.length-1;r++){const l=Y.timeToMinutes(i[r].checkInTime),u=Y.timeToMinutes(i[r+1].checkInTime);Math.abs(u-l)<10&&a.push("Multiple check-ins within 10 minutes detected")}return{valid:n.length===0,errors:n,warnings:a}}}const Ni=({attendanceRecords:t,schedule:s})=>{const[n,a]=x.useState({}),[i,o]=x.useState(!1),[r,l]=x.useState({reason:"",type:"",time:""}),u=()=>{const E=t.reduce((m,N)=>{const v=N.date;return m[v]||(m[v]=[]),m[v].push(N),m},{}),D={};return Object.keys(E).forEach(m=>{const N=E[m];D[m]=d(N)}),D},d=E=>{const D=E.filter(O=>O.type==="checkin").sort((O,S)=>new Date(O.timestamp)-new Date(S.timestamp)),m=E.filter(O=>O.type==="checkout").sort((O,S)=>new Date(O.timestamp)-new Date(S.timestamp)),N=[],v=new Set;return D.forEach((O,S)=>{let _=null;for(let T=0;T<m.length;T++){const h=m[T];if(!v.has(T)&&new Date(h.timestamp)>new Date(O.timestamp)){_=h,v.add(T);break}}let P=null;_&&(P=Y.calculateWorkingHours(O.checkInTime,_.checkOutTime)),N.push({checkIn:O,checkOut:_,workingHours:P,pairIndex:S,isComplete:!!_,isLate:O.isLate||_&&_.isLate})}),m.forEach((O,S)=>{v.has(S)||N.push({checkIn:null,checkOut:O,workingHours:null,pairIndex:N.length,isComplete:!1,isLate:O.isLate})}),N.sort((O,S)=>{const _=O.checkIn?new Date(O.checkIn.timestamp):new Date(O.checkOut.timestamp);return(S.checkIn?new Date(S.checkIn.timestamp):new Date(S.checkOut.timestamp))-_})},y=E=>{a(D=>({...D,[E]:!D[E]}))},b=(E,D)=>{E&&E.lateReason&&(l({reason:E.lateReason,type:"Check-in",time:E.checkInTime}),o(!0))},j=E=>new Date(E).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}),p=E=>{if(!E)return"-";const D=E.split(":");return D.length===2?`${D[0]}:${D[1]}:00`:E},k=(E,D)=>{if(!E)return null;const m=E.isLate,N=m?"status-rejected":D==="checkin"?"status-approved":"status-pending",v=D==="checkin"?m?"Late Check In":"Check In":m?"Late Check Out":"Check Out";return e.jsxs("div",{children:[e.jsx("span",{className:`status-badge ${N}`,children:v}),m&&E.lateReason&&e.jsxs("div",{style:{fontSize:"11px",color:"#666",marginTop:"3px"},children:["Reason: ",E.lateReason]})]})},w=E=>{const D=E.filter(O=>O.isComplete);let m=0,N=0,v=0;return D.forEach(O=>{if(O.workingHours&&O.workingHours.total>0){const S=O.workingHours.hours*60+O.workingHours.minutes;m+=S,O.isLate?v+=S:N+=S}}),{totalHours:{hours:Math.floor(m/60),minutes:m%60,display:`${Math.floor(m/60)}h ${m%60}m`},regularHours:{hours:Math.floor(N/60),minutes:N%60,display:`${Math.floor(N/60)}h ${N%60}m`},lateHours:{hours:Math.floor(v/60),minutes:v%60,display:`${Math.floor(v/60)}h ${v%60}m`},completePairs:D.length,totalPairs:E.length}},g=u(),f=Object.keys(g).sort((E,D)=>new Date(D)-new Date(E));return f.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No attendance records found"})]}):e.jsxs("div",{className:"paired-attendance",children:[f.map(E=>{const D=g[E],m=n[E],N=w(D);return e.jsxs("div",{className:"date-group",style:{marginBottom:"20px"},children:[e.jsxs("div",{className:"date-header",style:{background:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"8px",padding:"15px",cursor:"pointer",display:"flex",justifyContent:"space-between",alignItems:"center",transition:"background 0.2s"},onClick:()=>y(E),onMouseEnter:v=>v.target.style.background="#e9ecef",onMouseLeave:v=>v.target.style.background="#f8f9fa",children:[e.jsxs("div",{children:[e.jsx("h4",{style:{margin:"0 0 5px 0",color:"#333"},children:j(E)}),e.jsxs("div",{style:{fontSize:"14px",color:"#666"},children:[e.jsxs("span",{style:{marginRight:"20px"},children:["Pairs: ",N.completePairs,"/",N.totalPairs]}),e.jsxs("span",{style:{marginRight:"20px"},children:["Total Hours: ",N.totalHours.display]}),N.lateHours.hours>0&&e.jsxs("span",{style:{color:"#e67e22"},children:["Late: ",N.lateHours.display]})]})]}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx("span",{style:{fontSize:"12px",color:"#666"},children:m?"Hide Details":"Show Details"}),m?e.jsx(rn,{size:20}):e.jsx(ln,{size:20})]})]}),m&&e.jsxs("div",{className:"date-records",style:{border:"1px solid #dee2e6",borderTop:"none",borderRadius:"0 0 8px 8px",background:"white"},children:[e.jsxs("table",{className:"table",style:{margin:0},children:[e.jsx("thead",{children:e.jsxs("tr",{style:{background:"#f8f9fa"},children:[e.jsx("th",{children:"Regular Check In"}),e.jsx("th",{children:"Regular Check Out"}),e.jsx("th",{children:"Late Check In"}),e.jsx("th",{children:"Late Check Out"}),e.jsx("th",{children:"Working Hours"}),e.jsx("th",{children:"Status"})]})}),e.jsx("tbody",{children:D.map((v,O)=>{var S,_;return e.jsxs("tr",{style:{background:v.isComplete?"white":"#fff3cd",borderLeft:v.isLate?"3px solid #e74c3c":"3px solid transparent"},children:[e.jsx("td",{children:v.checkIn&&!v.checkIn.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px"},children:p(v.checkIn.checkInTime)}),k(v.checkIn,"checkin")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{children:v.checkOut&&!v.checkOut.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px"},children:p(v.checkOut.checkOutTime)}),k(v.checkOut,"checkout")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{children:v.checkIn&&v.checkIn.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:"#f39c12"},children:p(v.checkIn.checkInTime)}),e.jsxs("button",{onClick:()=>b(v.checkIn),style:{background:"none",border:"none",color:"#f39c12",cursor:"pointer",fontSize:"11px",textDecoration:"underline",padding:"2px 0",display:"flex",alignItems:"center"},title:"Click to view late reason",children:[e.jsx(ae,{size:12,style:{marginRight:"3px"}}),"Late (View Reason)"]})]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{children:v.checkOut&&v.checkOut.isLate?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:"#f39c12"},children:p(v.checkOut.checkOutTime)}),e.jsxs("div",{style:{fontSize:"11px",color:"#f39c12",display:"flex",alignItems:"center"},children:[e.jsx(ae,{size:12,style:{marginRight:"3px"}}),"Late Check Out"]})]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{style:{fontFamily:"monospace",fontWeight:"bold"},children:v.isComplete?e.jsxs("div",{children:[e.jsx("div",{style:{color:"#007bff",fontWeight:"bold"},children:v.workingHours?v.workingHours.display:"0h 0m"}),(((S=v.checkIn)==null?void 0:S.isLate)||((_=v.checkOut)==null?void 0:_.isLate))&&e.jsx("div",{style:{fontSize:"11px",color:"#f39c12",marginTop:"2px"},children:"(Includes Late Hours)"})]}):e.jsx("span",{style:{color:"#ffc107"},children:"Incomplete"})}),e.jsx("td",{children:v.isComplete?e.jsxs("span",{style:{color:"#28a745",fontSize:"12px"},children:["✓ Complete ",v.isLate?"(Late)":""]}):e.jsx("span",{style:{color:"#ffc107",fontSize:"12px"},children:"⚠ Incomplete"})})]},O)})})]}),e.jsx("div",{style:{padding:"15px",background:"#f8f9fa",borderTop:"1px solid #dee2e6",borderRadius:"0 0 8px 8px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(150px, 1fr))",gap:"15px"},children:[e.jsxs("div",{children:[e.jsx("strong",{children:"Regular Hours:"}),e.jsx("br",{}),e.jsx("span",{style:{color:"#28a745"},children:N.regularHours.display})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Late Hours:"}),e.jsx("br",{}),e.jsx("span",{style:{color:"#ffc107"},children:N.lateHours.display})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Total Hours:"}),e.jsx("br",{}),e.jsx("span",{style:{color:"#007bff",fontWeight:"bold"},children:N.totalHours.display})]}),e.jsxs("div",{children:[e.jsx("strong",{children:"Completion:"}),e.jsx("br",{}),e.jsxs("span",{style:{color:"#6c757d"},children:[N.completePairs,"/",N.totalPairs," pairs"]})]})]})})]})]},E)}),i&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e4},children:e.jsxs("div",{style:{backgroundColor:"white",borderRadius:"8px",padding:"30px",maxWidth:"500px",width:"90%",maxHeight:"80vh",overflowY:"auto",boxShadow:"0 10px 30px rgba(0, 0, 0, 0.3)"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[e.jsxs("h3",{style:{margin:0,color:"#333",display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(ae,{size:24,style:{color:"#f39c12"}}),"Late ",r.type," Reason"]}),e.jsx("button",{onClick:()=>o(!1),style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666",padding:"0",width:"30px",height:"30px",display:"flex",alignItems:"center",justifyContent:"center"},children:e.jsx(De,{size:20})})]}),e.jsxs("div",{style:{marginBottom:"20px"},children:[e.jsxs("div",{style:{padding:"15px",backgroundColor:"#fff3cd",borderRadius:"6px",border:"1px solid #ffeaa7",marginBottom:"15px"},children:[e.jsxs("div",{style:{fontSize:"14px",color:"#856404",marginBottom:"5px"},children:[e.jsx("strong",{children:"Time:"})," ",r.time]}),e.jsxs("div",{style:{fontSize:"14px",color:"#856404"},children:[e.jsx("strong",{children:"Type:"})," Late ",r.type]})]}),e.jsxs("div",{children:[e.jsxs("label",{style:{display:"block",marginBottom:"8px",fontWeight:"bold",color:"#333"},children:["Reason for Late ",r.type,":"]}),e.jsx("div",{style:{padding:"15px",backgroundColor:"#f8f9fa",border:"1px solid #dee2e6",borderRadius:"6px",minHeight:"80px",fontSize:"14px",lineHeight:"1.5",color:"#333"},children:r.reason})]})]}),e.jsx("div",{style:{textAlign:"right"},children:e.jsx("button",{onClick:()=>o(!1),className:"btn btn-secondary",style:{padding:"10px 20px"},children:"Close"})})]})})]})},Ri=({todayAttendance:t})=>{const[s,n]=x.useState(!1),[a,i]=x.useState({reason:"",type:"",time:""}),o=j=>{const p=j.filter(f=>f.type==="checkin").sort((f,E)=>new Date(f.timestamp)-new Date(E.timestamp)),k=j.filter(f=>f.type==="checkout").sort((f,E)=>new Date(f.timestamp)-new Date(E.timestamp)),w=[],g=new Set;return p.forEach((f,E)=>{let D=null;for(let N=0;N<k.length;N++){const v=k[N];if(!g.has(N)&&new Date(v.timestamp)>new Date(f.timestamp)){D=v,g.add(N);break}}let m=null;D&&(m=Y.calculateWorkingHours(f.checkInTime,D.checkOutTime)),w.push({checkIn:f,checkOut:D,workingHours:m,pairIndex:E,isComplete:!!D,isLate:f.isLate||D&&D.isLate})}),k.forEach((f,E)=>{g.has(E)||w.push({checkIn:null,checkOut:f,workingHours:null,pairIndex:w.length,isComplete:!1,isLate:f.isLate})}),w.sort((f,E)=>{const D=f.checkIn?new Date(f.checkIn.timestamp):new Date(f.checkOut.timestamp),m=E.checkIn?new Date(E.checkIn.timestamp):new Date(E.checkOut.timestamp);return D-m})},r=j=>{const p=j.filter(f=>f.isComplete);let k=0,w=0,g=0;return p.forEach(f=>{if(f.workingHours&&f.workingHours.total>0){const E=f.workingHours.hours*60+f.workingHours.minutes;k+=E,f.isLate?g+=E:w+=E}}),{totalHours:{hours:Math.floor(k/60),minutes:k%60,display:`${Math.floor(k/60)}h ${k%60}m`},regularHours:{hours:Math.floor(w/60),minutes:w%60,display:`${Math.floor(w/60)}h ${w%60}m`},lateHours:{hours:Math.floor(g/60),minutes:g%60,display:`${Math.floor(g/60)}h ${g%60}m`},completePairs:p.length,totalPairs:j.length}},l=j=>{if(!j)return"-";const p=j.split(":");return p.length===2?`${p[0]}:${p[1]}:00`:j},u=(j,p)=>{j&&j.lateReason&&(i({reason:j.lateReason,type:p==="checkin"?"Check-in":"Check-out",time:p==="checkin"?j.checkInTime:j.checkOutTime}),n(!0))},d=(j,p)=>{if(!j)return null;const k=j.isLate,w=k?"status-rejected":p==="checkin"?"status-approved":"status-pending",g=p==="checkin"?k?"Late Check In":"Check In":k?"Late Check Out":"Check Out";return e.jsxs("div",{children:[e.jsx("span",{className:`status-badge ${w}`,style:{fontSize:"11px"},children:g}),k&&j.lateReason&&e.jsxs("button",{style:{fontSize:"10px",color:"#f39c12",marginLeft:"6px",background:"none",border:"none",cursor:"pointer",textDecoration:"underline"},onClick:()=>u(j,p),children:[e.jsx(ae,{size:12,style:{marginRight:"3px"}}),"View Reason"]})]})},y=o(t),b=r(y);return e.jsxs("div",{children:[e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%",borderCollapse:"collapse"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Check In"}),e.jsx("th",{children:"Check Out"}),e.jsx("th",{children:"Working Hours"}),e.jsx("th",{children:"Status"})]})}),e.jsx("tbody",{children:y.map((j,p)=>e.jsxs("tr",{style:{background:j.isComplete?"white":"#fff3cd",borderLeft:j.isLate?"3px solid #e74c3c":"3px solid transparent",height:"auto",verticalAlign:"top"},children:[e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:j.checkIn?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:j.checkIn.isLate?"#f39c12":void 0},children:l(j.checkIn.checkInTime)}),d(j.checkIn,"checkin")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:j.checkOut?e.jsxs("div",{children:[e.jsx("div",{style:{fontFamily:"monospace",fontWeight:"bold",marginBottom:"4px",color:j.checkOut.isLate?"#f39c12":void 0},children:l(j.checkOut.checkOutTime)}),d(j.checkOut,"checkout")]}):e.jsx("span",{style:{color:"#999"},children:"-"})}),e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:j.workingHours?j.workingHours.display:"-"}),e.jsx("td",{style:{padding:"12px",verticalAlign:"top",borderBottom:"1px solid #dee2e6"},children:j.isComplete?e.jsxs("span",{style:{color:j.isLate?"#f39c12":"#27ae60",fontWeight:500},children:["✓ Complete",j.isLate?" (Late)":""]}):e.jsx("span",{style:{color:"#999"},children:"Incomplete"})})]},p))})]}),e.jsxs("div",{style:{marginTop:"10px",fontWeight:500,color:"#333"},children:["Total Working Hours: ",b.totalHours.display,b.lateHours.totalMinutes>0&&e.jsxs("span",{style:{color:"#f39c12",marginLeft:"10px"},children:["(Late: ",b.lateHours.display,")"]})]}),s&&e.jsx("div",{className:"modal-overlay",onClick:()=>n(!1),children:e.jsxs("div",{className:"modal-content",onClick:j=>j.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h3",{children:["Late ",a.type," Reason"]}),e.jsx("button",{className:"modal-close",onClick:()=>n(!1),children:e.jsx(De,{size:18})})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Time:"})," ",a.time]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Reason:"})," ",a.reason]})]})]})})]})},Se=async(t,s,n,a,i={})=>{try{const o={userId:t,type:s,title:n,message:a,date:new Date().toISOString().split("T")[0],isRead:!1,...i};await Ee.createNotification(o)}catch(o){console.error("Error creating notification:",o)}},Ce={TASK_ASSIGNED:"task_assigned",TASK_COMPLETED:"task_completed",TASK_UPDATED:"task_updated",HOLIDAY_APPROVED:"holiday_approved",HOLIDAY_REJECTED:"holiday_rejected",HOLIDAY_SUBMITTED:"holiday_submitted",LATE_CHECKIN:"late_checkin"},Ai=async(t,s,n)=>{await Se(t,Ce.TASK_ASSIGNED,"New Task Assigned",`You have been assigned a new task: "${s}" by ${n}`,{taskTitle:s,assignedBy:n})},Di=async(t,s,n)=>{await Se(t,Ce.TASK_COMPLETED,"Task Completed",`${n} has completed the task: "${s}"`,{taskTitle:s,employeeName:n})},Oi=async(t,s,n,a)=>{await Se(t,Ce.TASK_UPDATED,"Task Status Updated",`${n} has updated the task "${s}" status to: ${a}`,{taskTitle:s,employeeName:n,status:a})},Mi=async(t,s,n)=>{await Se(t,Ce.HOLIDAY_APPROVED,"Holiday Request Approved",`Your holiday request from ${s} to ${n} has been approved`,{startDate:s,endDate:n})},Li=async(t,s,n,a)=>{await Se(t,Ce.HOLIDAY_REJECTED,"Holiday Request Rejected",`Your holiday request from ${s} to ${n} has been rejected. Reason: ${a}`,{startDate:s,endDate:n,reason:a})},_i=async(t,s,n,a)=>{await Se(t,Ce.HOLIDAY_SUBMITTED,"New Holiday Request",`${s} has submitted a holiday request from ${n} to ${a}`,{employeeName:s,startDate:n,endDate:a})},zi=async(t,s,n,a)=>{await Se(t,Ce.LATE_CHECKIN,"Late Check-in Alert",`${s} checked in late at ${n}. Reason: ${a}`,{employeeName:s,time:n,reason:a})},Pi=()=>{const{userProfile:t}=Q(),[s,n]=x.useState(null),[a,i]=x.useState([]),[o,r]=x.useState(!1),[l,u]=x.useState(!1),[d,y]=x.useState(""),[b,j]=x.useState(""),[p,k]=x.useState(null),[w,g]=x.useState({}),[f,E]=x.useState([]),[D,m]=x.useState(!1),[N,v]=x.useState(new Date);x.useEffect(()=>{S(),O()},[t]),x.useEffect(()=>{p&&a&&_(a)},[p,a]),x.useEffect(()=>{const M=setInterval(()=>{v(new Date)},1e3);return()=>clearInterval(M)},[]);const O=async()=>{try{console.log("Loading schedule for user:",t.uid);const M=await z.getWhere(L.TIME_TABLES,"userId","==",t.uid);if(console.log("Found time tables:",M),M.length>0){const U=M[0].schedule||{};console.log("Setting schedule data:",U),k(U)}else console.log("No time tables found, setting empty schedule"),k({})}catch(M){console.error("Error loading schedule:",M),k({})}},S=async()=>{try{const M=W(new Date,"yyyy-MM-dd"),U=await z.getWhere(L.ATTENDANCE,"userId","==",t.uid),q=U.sort((J,ee)=>{const Ie=new Date(ee.date)-new Date(J.date);return Ie!==0?Ie:new Date(ee.timestamp)-new Date(J.timestamp)});E(q);const G=U.filter(J=>J.date===M).sort((J,ee)=>new Date(ee.timestamp)-new Date(J.timestamp));i(G);const ne=Y.isCurrentlyCheckedIn(G);n(ne?"checked-in":"not-checked-in"),p&&_(G)}catch(M){console.error("Error loading attendance:",M)}},_=M=>{if(!p)return;const U=Y.validateRegularCheckIn(p,M),q=Y.validateLateCheckIn(p,M),G=Y.validateRegularCheckOut(p,M),ne=Y.validateLateCheckOut(p,M);g({regularCheckIn:U,lateCheckIn:q,regularCheckOut:G,lateCheckOut:ne})},P=async()=>{const M=Y.validateRegularCheckIn(p,a);if(!M.allowed){R.error(M.message);return}if(Y.getRecentCheckIns(a,10).length>0){R.error("Multiple check-ins within 10 minutes are not allowed");return}r(!0);try{const q=new Date,G=M.slot,ne={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(q,"yyyy-MM-dd"),day:W(q,"EEEE"),checkInTime:W(q,"HH:mm:ss"),timestamp:q.toISOString(),type:"checkin",scheduledCheckIn:G.scheduledCheckIn,scheduledCheckOut:G.scheduledCheckOut,slotIndex:G.slotIndex};await Ut.checkIn(t.uid,ne),R.success("Checked in successfully!"),S()}catch(q){console.error("Check-in error:",q),R.error("Failed to check in")}finally{r(!1)}},T=async()=>{const M=Y.validateRegularCheckOut(p,a);if(!M.allowed){R.error(M.message);return}r(!0);try{const U=new Date,q=M.lastCheckIn,G=Y.calculateWorkingHours(q.checkInTime,W(U,"HH:mm:ss")),ne={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(U,"yyyy-MM-dd"),day:W(U,"EEEE"),checkOutTime:W(U,"HH:mm:ss"),workingHours:G.total,workingHoursDisplay:G.display,timestamp:U.toISOString(),type:"checkout",checkInTime:q.checkInTime,scheduledCheckIn:q.scheduledCheckIn,scheduledCheckOut:q.scheduledCheckOut,slotIndex:q.slotIndex};await z.create(L.ATTENDANCE,ne),R.success("Checked out successfully!"),S()}catch(U){console.error("Check-out error:",U),R.error("Failed to check out")}finally{r(!1)}},h=async()=>{if(!d.trim()){R.error("Please provide a reason for late check-in");return}const M=Y.validateLateCheckIn(p,a);if(!M.allowed){R.error(M.message);return}r(!0);try{const U=new Date,q=M.slot,G={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(U,"yyyy-MM-dd"),day:W(U,"EEEE"),checkInTime:W(U,"HH:mm:ss"),timestamp:U.toISOString(),type:"checkin",isLate:!0,lateReason:d.trim(),scheduledCheckIn:q.scheduledCheckIn,scheduledCheckOut:q.scheduledCheckOut,slotIndex:q.slotIndex};await Ut.checkIn(t.uid,G);const J=(await z.getAll(L.USERS)).filter(ee=>ee.role==="admin");for(const ee of J)await zi(ee.uid,t.name,W(U,"HH:mm:ss"),d.trim());R.success("Late check-in recorded successfully!"),u(!1),y(""),j(""),S()}catch(U){console.error("Late check-in error:",U),R.error("Failed to record late check-in")}finally{r(!1)}},c=async()=>{const M=Y.validateLateCheckOut(p,a);if(!M.allowed){R.error(M.message);return}r(!0);try{const U=new Date,q=M.lastCheckIn,G=Y.calculateWorkingHours(q.checkInTime,W(U,"HH:mm:ss")),ne={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,date:W(U,"yyyy-MM-dd"),day:W(U,"EEEE"),checkOutTime:W(U,"HH:mm:ss"),workingHours:G.total,workingHoursDisplay:G.display,timestamp:U.toISOString(),type:"checkout",checkInTime:q.checkInTime,scheduledCheckIn:q.scheduledCheckIn,scheduledCheckOut:q.scheduledCheckOut,slotIndex:q.slotIndex,isLate:!0};await z.create(L.ATTENDANCE,ne),R.success("Late check-out recorded successfully!"),u(!1),y(""),j(""),S()}catch(U){console.error("Late check-out error:",U),R.error("Failed to record late check-out")}finally{r(!1)}},I=()=>{b==="checkin"?h():b==="checkout"&&c()},B=(()=>{const M=s==="checked-in",U=()=>{var J;return!p||M?!1:((J=w.regularCheckIn)==null?void 0:J.allowed)===!0},q=()=>{var J,ee;return!p||M?!1:((J=w.regularCheckIn)==null?void 0:J.allowed)===!1&&((ee=w.regularCheckIn)==null?void 0:ee.reason)==="TOO_LATE"},G=()=>{var ee;if(!p||!M)return!1;const J=a.find(Ie=>Ie.type==="checkin");return J&&!J.isLate&&((ee=w.regularCheckOut)==null?void 0:ee.allowed)===!0},ne=()=>{var ee;if(!p||!M)return!1;const J=a.find(Ie=>Ie.type==="checkin");return J&&J.isLate&&((ee=w.regularCheckOut)==null?void 0:ee.allowed)===!1};return M?{text:"Currently Checked In",color:"#27ae60",icon:te,action:"Check Out",handler:T,canRegularCheckOut:G(),canLateCheckOut:ne()}:{text:"Not Checked In",color:"#f39c12",icon:V,action:"Check In",handler:P,canRegularCheckIn:U(),canLateCheckIn:q()}})(),F=B.icon,H=s==="checked-in",K=Y.validateRegularCheckIn(p,a),Z=Y.validateLateCheckIn(p,a),oe=Y.validateRegularCheckOut(p,a),C=Y.validateLateCheckOut(p,a),X=a.filter(M=>M.type==="checkin").sort((M,U)=>new Date(U.timestamp)-new Date(M.timestamp))[0],$=(X==null?void 0:X.isLate)===!0;return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"card text-center",style:{marginBottom:"20px",marginTop:"10px"},children:[e.jsx("h2",{style:{marginBottom:"15px",color:"#333",fontSize:"24px"},children:"Check In/Out"}),e.jsx(F,{size:48,style:{color:B.color,marginBottom:"15px"}}),e.jsx("h3",{style:{color:B.color,marginBottom:"15px",fontSize:"20px"},children:B.text}),e.jsxs("p",{style:{color:"#666",marginBottom:"15px",fontSize:"14px"},children:["Current Time: ",W(N,"HH:mm:ss - EEEE, MMMM do, yyyy")]}),!H&&!K.allowed&&!Z.allowed&&e.jsxs("div",{style:{background:"#f8d7da",border:"1px solid #f5c6cb",borderRadius:"4px",padding:"12px",marginBottom:"15px",color:"#721c24",fontSize:"14px",display:"flex",alignItems:"center",gap:"8px"},children:["❌ ",K.message||Z.message]}),H&&!oe.allowed&&!C.allowed&&e.jsxs("div",{style:{background:"#f8d7da",border:"1px solid #f5c6cb",borderRadius:"4px",padding:"8px",marginBottom:"15px",color:"#721c24",fontSize:"14px"},children:["❌ ",oe.message||C.message]}),e.jsxs("div",{style:{display:"flex",gap:"12px",justifyContent:"center",flexWrap:"wrap"},children:[!H&&K.allowed&&e.jsx("button",{onClick:P,disabled:o,className:"btn btn-success",style:{fontSize:"16px",padding:"12px 24px"},children:o?"Processing...":"Regular Check-in"}),!H&&!K.allowed&&Z.allowed&&e.jsxs("button",{onClick:()=>{j("checkin"),u(!0)},disabled:o,className:"btn btn-warning",style:{fontSize:"16px",padding:"12px 24px"},title:"Submit late check-in with reason",children:[e.jsx(ae,{size:16,style:{marginRight:"6px"}}),"Late Check-in"]}),H&&!$&&oe.allowed&&e.jsx("button",{onClick:T,disabled:o,className:"btn btn-danger",style:{fontSize:"16px",padding:"12px 24px"},children:o?"Processing...":"Regular Check-out"}),H&&$&&C.allowed&&e.jsxs("button",{onClick:()=>{j("checkout"),u(!0)},disabled:o,className:"btn btn-warning",style:{fontSize:"16px",padding:"12px 24px"},title:"Submit late check-out",children:[e.jsx(ae,{size:16,style:{marginRight:"6px"}}),"Late Check-out"]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"20px"},children:[e.jsx("h3",{style:{margin:0,color:"#333"},children:D?"All Attendance Records":"Today's Attendance"}),e.jsxs("div",{style:{display:"flex",gap:"10px"},children:[e.jsx("button",{onClick:()=>m(!1),className:`btn ${D?"btn-secondary":"btn-primary"}`,style:{padding:"8px 16px",fontSize:"14px"},children:"Today Only"}),e.jsx("button",{onClick:()=>m(!0),className:`btn ${D?"btn-primary":"btn-secondary"}`,style:{padding:"8px 16px",fontSize:"14px"},children:"All Records"})]})]}),D?e.jsx(Ni,{attendanceRecords:f,schedule:p}):e.jsx(e.Fragment,{children:a.length>0?e.jsx(e.Fragment,{children:e.jsx(Ri,{todayAttendance:a})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No attendance records for today"})]})}),l&&e.jsx("div",{className:"modal-overlay",onClick:()=>{u(!1),y(""),j("")},children:e.jsxs("div",{className:"modal-content",onClick:M=>M.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsxs("h3",{children:["Late ",b==="checkin"?"Check-in":"Check-out"]}),e.jsx("button",{className:"modal-close",onClick:()=>{u(!1),y(""),j("")},children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[b==="checkin"?e.jsxs(e.Fragment,{children:[e.jsx("p",{style:{marginBottom:"15px",color:"#666"},children:"Please provide a reason for your late check-in:"}),e.jsx("textarea",{value:d,onChange:M=>y(M.target.value),placeholder:"Enter reason for late check-in...",className:"form-textarea",rows:"4",style:{width:"100%",marginBottom:"20px"}})]}):e.jsx("p",{style:{marginBottom:"20px",color:"#666"},children:"Are you sure you want to submit a late check-out?"}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>{u(!1),y(""),j("")},className:"btn btn-secondary",disabled:o,children:"Cancel"}),e.jsx("button",{onClick:I,className:"btn btn-warning",disabled:o||b==="checkin"&&!d.trim(),children:o?"Processing...":`Submit Late ${b==="checkin"?"Check-in":"Check-out"}`})]})]})]})})]})]})},Bi=()=>{const{userProfile:t}=Q(),[s,n]=x.useState({}),[a,i]=x.useState(!1),[o,r]=x.useState(!1),[l,u]=x.useState(!0),[d,y]=x.useState(null),b=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];x.useEffect(()=>{t&&(p(),j(),be.checkAndPerformWeeklyReset())},[t]);const j=async()=>{try{const m=await be.canEmployeeEditTimetable(t.uid);u(m.canEdit),y(m)}catch(m){console.error("Error checking edit permissions:",m),u(!1)}},p=async()=>{try{const m=await z.getWhere(L.TIME_TABLES,"userId","==",t.uid);if(m.length>0)n(m[0].schedule||{});else{const N={};b.forEach(v=>{N[v]=[]}),n(N)}}catch(m){console.error("Error loading timetable:",m)}},k=m=>{n(N=>({...N,[m]:[...N[m]||[],{checkIn:"",checkOut:""}]}))},w=(m,N)=>{n(v=>({...v,[m]:v[m].filter((O,S)=>S!==N)}))},g=(m,N,v,O)=>{n(S=>({...S,[m]:S[m].map((_,P)=>P===N?{..._,[v]:O}:_)}))},f=(m,N)=>{if(!m||!N)return"00:00:00";const v=new Date(`2000-01-01T${m}`),O=new Date(`2000-01-01T${N}`);if(O<=v)return"00:00:00";const S=O-v,_=Math.floor(S/1e3),P=Math.floor(_/3600),T=Math.floor(_%3600/60),h=_%60;return`${P.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}:${h.toString().padStart(2,"0")}`},E=m=>{const N=s[m]||[];let v=0;N.forEach(P=>{if(P.checkIn&&P.checkOut){const T=new Date(`2000-01-01T${P.checkIn}`),h=new Date(`2000-01-01T${P.checkOut}`);if(h>T){const c=h-T;v+=Math.floor(c/1e3)}}});const O=Math.floor(v/3600),S=Math.floor(v%3600/60),_=v%60;return`${O.toString().padStart(2,"0")}:${S.toString().padStart(2,"0")}:${_.toString().padStart(2,"0")}`},D=async()=>{i(!0);try{await be.saveEmployeeTimetable(t.uid,{employeeId:t.employeeId,name:t.name},s),R.success("Time table saved successfully!"),r(!1),await j()}catch(m){console.error("Error saving timetable:",m),R.error(m.message||"Failed to save time table")}finally{i(!1)}};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsxs("div",{children:[e.jsx("h2",{style:{color:"#333",margin:"0 0 5px 0"},children:"Set Your Time Table"}),e.jsxs("p",{style:{color:"#666",fontSize:"14px",margin:0},children:["Current Week: ",be.getCurrentWeekId()]})]}),e.jsx("div",{className:"flex gap-10",children:o?e.jsxs(e.Fragment,{children:[e.jsx("button",{onClick:()=>r(!1),className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:D,className:"btn btn-success",disabled:a,children:a?"Saving...":"Save Changes"})]}):e.jsx("button",{onClick:()=>r(!0),className:"btn btn-primary",disabled:!l,style:{opacity:l?1:.6,cursor:l?"pointer":"not-allowed"},children:l?"Edit Time Table":"Cannot Edit"})})]}),!l&&d&&e.jsx("div",{className:"card",style:{marginBottom:"20px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderLeft:"4px solid #f39c12"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",padding:"15px"},children:[e.jsx(cn,{size:24,style:{color:"#f39c12"}}),e.jsxs("div",{children:[e.jsx("h4",{style:{margin:"0 0 5px 0",color:"#856404"},children:"Timetable Editing Restricted"}),e.jsx("p",{style:{margin:0,color:"#856404",fontSize:"14px"},children:d.message||"You cannot edit your timetable at this time."}),e.jsx("p",{style:{margin:"5px 0 0 0",color:"#856404",fontSize:"12px",fontStyle:"italic"},children:"You can set a new timetable at the beginning of next week."})]})]})}),l&&d&&d.reason==="CARRIED_OVER_SCHEDULE"&&e.jsx("div",{className:"card",style:{marginBottom:"20px",backgroundColor:"#d1ecf1",border:"1px solid #bee5eb",borderLeft:"4px solid #17a2b8"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px",padding:"15px"},children:[e.jsx(ae,{size:24,style:{color:"#17a2b8"}}),e.jsxs("div",{children:[e.jsx("h4",{style:{margin:"0 0 5px 0",color:"#0c5460"},children:"Previous Week Schedule Carried Over"}),e.jsx("p",{style:{margin:0,color:"#0c5460",fontSize:"14px"},children:"Your previous week's schedule has been carried over. You can modify it or save as-is."}),e.jsx("p",{style:{margin:"5px 0 0 0",color:"#0c5460",fontSize:"12px",fontStyle:"italic"},children:"Once you save, you won't be able to modify it again this week."})]})]})}),e.jsx("div",{className:"card",children:b.map(m=>e.jsxs("div",{style:{marginBottom:"30px",paddingBottom:"20px",borderBottom:"1px solid #eee"},children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h3",{style:{color:"#333",margin:0},children:m}),e.jsxs("div",{className:"flex align-center gap-10",children:[e.jsxs("span",{style:{color:"#666",fontSize:"14px"},children:["Total: ",E(m)]}),o&&e.jsxs("button",{onClick:()=>k(m),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},children:[e.jsx(ge,{size:14})," Add Time Slot"]})]})]}),s[m]&&s[m].length>0?e.jsx("div",{style:{display:"grid",gap:"10px"},children:s[m].map((N,v)=>e.jsxs("div",{className:"flex align-center gap-10",style:{padding:"10px",background:"#f8f9fa",borderRadius:"4px"},children:[e.jsxs("div",{className:"flex align-center gap-10",style:{flex:1},children:[e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"Check In"}),e.jsx("input",{type:"time",value:N.checkIn,onChange:O=>g(m,v,"checkIn",O.target.value),className:"form-input",style:{width:"120px"},disabled:!o})]}),e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666"},children:"Check Out"}),e.jsx("input",{type:"time",value:N.checkOut,onChange:O=>g(m,v,"checkOut",O.target.value),className:"form-input",style:{width:"120px"},disabled:!o})]}),e.jsxs("div",{style:{minWidth:"80px",textAlign:"center"},children:[e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:"Duration"}),e.jsx("div",{style:{fontWeight:"bold",color:"#3498db",fontFamily:"monospace"},children:f(N.checkIn,N.checkOut)})]})]}),o&&e.jsx("button",{onClick:()=>w(m,v),className:"btn btn-danger",style:{padding:"5px",minWidth:"auto"},children:e.jsx(De,{size:14})})]},v))}):e.jsxs("div",{style:{textAlign:"center",padding:"20px",color:"#666",background:"#f8f9fa",borderRadius:"4px"},children:[e.jsx(se,{size:24,style:{opacity:.3,marginBottom:"5px"}}),e.jsxs("p",{style:{fontSize:"14px"},children:["No time slots set for ",m]}),o&&e.jsxs("button",{onClick:()=>k(m),className:"btn btn-primary",style:{marginTop:"10px",padding:"5px 15px",fontSize:"12px"},children:[e.jsx(ge,{size:14})," Add Time Slot"]})]})]},m))})]})};class Fi{async fileToBase64(s){return new Promise((n,a)=>{const i=new FileReader;i.readAsDataURL(s),i.onload=()=>n(i.result),i.onerror=o=>a(o)})}async processFile(s){try{const n=await this.fileToBase64(s);return{success:!0,fileName:s.name,fileSize:s.size,fileType:s.type,fileData:n,uploadedAt:new Date().toISOString()}}catch(n){throw console.error("Error processing file:",n),new Error(`Failed to process file: ${n.message}`)}}createDownloadLink(s,n){try{if(console.log("Creating download link for:",s),console.log("Base64 data length:",n==null?void 0:n.length),!n||!s)throw new Error("Missing file data or filename");const a=n.includes(",")?n.split(",")[1]:n,i=atob(a),o=new Array(i.length);for(let b=0;b<i.length;b++)o[b]=i.charCodeAt(b);const r=new Uint8Array(o);let l="application/octet-stream";if(n.startsWith("data:"))l=n.substring(5,n.indexOf(";"));else{const b=s.split(".").pop().toLowerCase();l={pdf:"application/pdf",doc:"application/msword",docx:"application/vnd.openxmlformats-officedocument.wordprocessingml.document",txt:"text/plain",jpg:"image/jpeg",jpeg:"image/jpeg",png:"image/png"}[b]||"application/octet-stream"}const u=new Blob([r],{type:l}),d=URL.createObjectURL(u),y=document.createElement("a");return y.href=d,y.download=s,y.style.display="none",document.body.appendChild(y),y.click(),document.body.removeChild(y),setTimeout(()=>URL.revokeObjectURL(d),1e3),console.log("Download initiated successfully for:",s),{success:!0}}catch(a){throw console.error("Error creating download link:",a),new Error(`Failed to download file: ${a.message}`)}}generateProgressReportPath(s,n){const a=Date.now(),i=n.replace(/[^a-zA-Z0-9.-]/g,"_");return`progress-reports/${s}/${a}_${i}`}generateFilePath(s,n,a){const i=Date.now(),o=a.replace(/[^a-zA-Z0-9.-]/g,"_");return`${s}/${n}/${i}_${o}`}getFileExtension(s){return s.split(".").pop().toLowerCase()}isValidFileType(s,n=[]){n.length===0&&(n=["pdf","doc","docx","txt","jpg","jpeg","png","gif"]);const a=this.getFileExtension(s.name);return n.includes(a)}isValidFileSize(s,n=5){return s.size/1048576<=n}formatFileSize(s){if(s===0)return"0 Bytes";const n=1024,a=["Bytes","KB","MB","GB"],i=Math.floor(Math.log(s)/Math.log(n));return parseFloat((s/Math.pow(n,i)).toFixed(2))+" "+a[i]}}const Ye=new Fi,Fe=({isOpen:t,onClose:s,title:n,content:a,type:i="text",employeeName:o="",date:r="",time:l=""})=>{if(!t)return null;const u=()=>{switch(i){case"remarks":return e.jsx(xe,{size:24,style:{color:"#3498db"}});case"details":return e.jsx(he,{size:24,style:{color:"#27ae60"}});default:return e.jsx(fe,{size:24,style:{color:"#666"}})}},d=()=>{if(n)return n;switch(i){case"remarks":return"Admin Remarks";case"details":return"Task Details";default:return"View Details"}};return e.jsxs("div",{className:"modal-overlay",onClick:s,style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3,padding:"20px"},children:[e.jsxs("div",{className:"modal-content",onClick:y=>y.stopPropagation(),style:{backgroundColor:"white",borderRadius:"12px",width:"100%",maxWidth:"600px",maxHeight:"80vh",overflow:"hidden",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.15)",animation:"modalSlideIn 0.3s ease-out"},children:[e.jsx("div",{className:"modal-header",style:{padding:"24px 24px 0 24px",borderBottom:"1px solid #eee",marginBottom:"0"},children:e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"flex-start",marginBottom:"20px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"12px",flex:1},children:[u(),e.jsxs("div",{children:[e.jsx("h3",{style:{margin:0,color:"#333",fontSize:"18px",fontWeight:"600"},children:d()}),o&&e.jsxs("p",{style:{margin:"4px 0 0 0",color:"#666",fontSize:"14px"},children:[o,r&&` • ${r}`,l&&` • ${l}`]})]})]}),e.jsx("button",{className:"modal-close",onClick:s,style:{background:"none",border:"none",cursor:"pointer",color:"#666",padding:"8px",borderRadius:"6px",transition:"background-color 0.2s",display:"flex",alignItems:"center",justifyContent:"center"},onMouseEnter:y=>y.target.style.backgroundColor="#f5f5f5",onMouseLeave:y=>y.target.style.backgroundColor="transparent",children:e.jsx(De,{size:20})})]})}),e.jsx("div",{className:"modal-body",style:{padding:"24px",overflowY:"auto",maxHeight:"calc(80vh - 120px)"},children:e.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"8px",padding:"16px",minHeight:"120px",lineHeight:"1.6",fontSize:"14px",color:"#333",whiteSpace:"pre-wrap",wordWrap:"break-word",fontFamily:"inherit"},children:a||e.jsxs("span",{style:{color:"#999",fontStyle:"italic"},children:["No ",i==="remarks"?"remarks":"content"," available"]})})}),e.jsx("div",{style:{padding:"16px 24px 24px 24px",borderTop:"1px solid #eee",display:"flex",justifyContent:"flex-end"},children:e.jsx("button",{onClick:s,className:"btn btn-secondary",style:{padding:"10px 20px",fontSize:"14px",fontWeight:"500"},children:"Close"})})]}),e.jsx("style",{jsx:!0,children:`
        @keyframes modalSlideIn {
          from {
            opacity: 0;
            transform: translateY(-20px) scale(0.95);
          }
          to {
            opacity: 1;
            transform: translateY(0) scale(1);
          }
        }
      `})]})},Hi=()=>{const{currentUser:t,userProfile:s}=Q(),[n,a]=x.useState([]),[i,o]=x.useState(!1),[r,l]=x.useState({tasksCompleted:"",file:null}),[u,d]=x.useState(!1),[y,b]=x.useState(!1),[j,p]=x.useState({});x.useEffect(()=>{k()},[s]);const k=async()=>{try{const m=await z.getWhere(L.PROGRESS_REPORTS,"userId","==",s.uid);a(m)}catch(m){console.error("Error loading progress reports:",m)}},w=m=>{if(m.target.name==="file"){const N=m.target.files[0];if(N){const v=N.size/1048576,O=5;if(v>O){R.error(`File size must be less than ${O}MB`),m.target.value="";return}if(!Ye.isValidFileType(N)){R.error("Invalid file type. Please upload PDF, DOC, DOCX, TXT, JPG, or PNG files."),m.target.value="";return}l({...r,file:N})}}else l({...r,[m.target.name]:m.target.value})},g=async m=>{if(m.preventDefault(),!r.tasksCompleted.trim()){R.error("Please describe the tasks you completed today");return}if(!s){R.error("User profile not loaded. Please refresh the page.");return}d(!0);try{const N=new Date;console.log("User Profile:",s);let v={fileName:"",fileSize:0,fileType:"",fileData:"",hasFile:!1};if(r.file)try{console.log("Processing file for Firestore storage...");const P=r.file.size/(1024*1024),T=5;if(P>T)console.warn(`File too large: ${P.toFixed(2)}MB (max: ${T}MB)`),R.error(`File too large (${P.toFixed(2)}MB). Maximum size is ${T}MB.`),v={fileName:r.file.name,fileSize:r.file.size,fileType:r.file.type,fileData:"",hasFile:!0,uploadedAt:new Date().toISOString(),uploadError:"File too large for upload"};else{const h=await Ye.processFile(r.file);v={fileName:h.fileName,fileSize:h.fileSize,fileType:h.fileType,fileData:h.fileData,hasFile:!0,uploadedAt:h.uploadedAt},console.log("File processed successfully:",{name:h.fileName,size:h.fileSize}),R.success("File processed successfully!")}}catch(P){console.error("Error processing file:",P),R.error("Failed to process file. Report will be saved without attachment."),v={fileName:r.file.name,fileSize:r.file.size,fileType:r.file.type,fileData:"",hasFile:!0,uploadedAt:new Date().toISOString(),uploadError:P.message}}const O={userId:s.uid||s.id||"",employeeId:s.employeeId||s.uid||"",employeeName:s.name||s.displayName||s.email||"Unknown User",date:W(N,"yyyy-MM-dd"),time:W(N,"HH:mm:ss"),tasksCompleted:r.tasksCompleted.trim(),...v,status:"submitted",adminRemarks:"",createdAt:N.toISOString()};console.log("Current User:",t),console.log("User Profile:",s),console.log("Report Data:",O);const{createdAt:S,..._}=O;await z.create(L.PROGRESS_REPORTS,_),R.success("Progress report submitted successfully!"),l({tasksCompleted:"",file:null}),o(!1),k()}catch(N){console.error("Error submitting progress report:",N),R.error("Failed to submit progress report")}finally{d(!1)}},f=m=>{const N={submitted:"status-pending",reviewed:"status-approved",rejected:"status-rejected"};return e.jsx("span",{className:`status-badge ${N[m]||"status-pending"}`,children:m.charAt(0).toUpperCase()+m.slice(1)})},E=m=>{p({title:"Tasks Completed",content:m.tasksCompleted,type:"details",employeeName:m.employeeName,date:m.date,time:m.time}),b(!0)},D=m=>{p({title:"Admin Remarks",content:m.adminRemarks,type:"remarks",employeeName:m.employeeName,date:m.date,time:m.time}),b(!0)};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Submit Daily Progress"}),e.jsx("button",{onClick:()=>o(!i),className:"btn btn-primary",children:i?"Cancel":"Submit Progress"})]}),i&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Daily Progress Report"}),e.jsxs("form",{onSubmit:g,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Tasks Completed Today *"}),e.jsx("textarea",{name:"tasksCompleted",value:r.tasksCompleted,onChange:w,className:"form-textarea",placeholder:"Describe your daily progress, tasks completed, achievements, etc...",required:!0,style:{minHeight:"120px"}})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Attach File (Optional)"}),e.jsxs("div",{style:{position:"relative"},children:[e.jsx("input",{type:"file",name:"file",onChange:w,className:"form-input",accept:".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png"}),e.jsx("div",{style:{fontSize:"12px",color:"#666",marginTop:"5px"},children:"Supported formats: PDF, DOC, DOCX, TXT, JPG, PNG (Max 5MB)"})]}),r.file&&e.jsxs("div",{style:{marginTop:"10px",padding:"10px",background:"#f8f9fa",borderRadius:"4px",display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(dn,{size:16,style:{color:"#27ae60"}}),e.jsxs("span",{style:{fontSize:"14px"},children:[r.file.name," (",(r.file.size/1024/1024).toFixed(2)," MB)"]})]})]}),e.jsxs("div",{className:"flex justify-between align-center",children:[e.jsxs("div",{style:{color:"#666",fontSize:"14px"},children:["Report Date: ",W(new Date,"MMMM do, yyyy")]}),e.jsx("button",{type:"submit",className:"btn btn-success",disabled:u,children:u?"Submitting...":"Submit Progress"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Your Previous Reports"}),n.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Date"}),e.jsx("th",{style:{width:"80px",minWidth:"80px"},children:"Time"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Tasks Completed"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"File"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Admin Remarks"})]})}),e.jsx("tbody",{children:n.map(m=>e.jsxs("tr",{children:[e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:m.date}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:m.time}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>E(m),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{style:{padding:"8px",textAlign:"center"},children:m.fileName?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"4px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",marginBottom:"2px"},children:[e.jsx(he,{size:14,style:{color:"#3498db"}}),e.jsx("span",{style:{fontSize:"11px",fontWeight:"bold",maxWidth:"80px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:m.fileName})]}),m.fileData?e.jsxs("button",{onClick:()=>{try{Ye.createDownloadLink(m.fileName,m.fileData),R.success("File downloaded!")}catch(N){console.error("Download error:",N),R.error("Failed to download file")}},style:{background:"#27ae60",color:"white",border:"none",borderRadius:"4px",padding:"4px 8px",fontSize:"10px",cursor:"pointer",display:"flex",alignItems:"center",gap:"4px"},title:"Download file",children:[e.jsx(ft,{size:12}),"Download"]}):e.jsx("span",{style:{color:m.uploadError?"#e74c3c":"#ccc",fontSize:"10px",fontStyle:"italic"},children:m.uploadError?"Too large":"Not available"})]}):e.jsx("span",{style:{color:"#999",fontSize:"12px",fontStyle:"italic"},children:"No file"})}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:f(m.status)}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>D(m),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:m.adminRemarks?1:.6},title:m.adminRemarks?"View admin remarks":"No remarks available",children:[e.jsx(xe,{size:14}),m.adminRemarks?"View Remarks":"No Remarks"]})})]},m.id))})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(he,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No progress reports submitted yet"})]})]}),e.jsx(Fe,{isOpen:y,onClose:()=>b(!1),title:j.title,content:j.content,type:j.type,employeeName:j.employeeName,date:j.date,time:j.time})]})},Ui=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState(!1),[o,r]=x.useState({startDate:"",endDate:"",reason:""}),[l,u]=x.useState(!1);x.useEffect(()=>{d()},[t]);const d=async()=>{try{const k=await pt.getUserRequests(t.uid);n(k)}catch(k){console.error("Error loading holiday requests:",k)}},y=k=>{r({...o,[k.target.name]:k.target.value})},b=()=>{if(o.startDate&&o.endDate){const k=new Date(o.startDate),w=new Date(o.endDate),g=un(w,k)+1;return g>0?g:0}return 0},j=async k=>{k.preventDefault(),u(!0);try{const w=b();if(w<=0){R.error("End date must be after start date");return}const g={userId:t.uid,employeeId:t.employeeId,employeeName:t.name,startDate:o.startDate,endDate:o.endDate,days:w,reason:o.reason,status:"pending",adminRemarks:"",requestDate:W(new Date,"yyyy-MM-dd")};await pt.createRequest(g);const E=(await z.getAll(L.USERS)).filter(D=>D.role==="admin");for(const D of E)await _i(D.uid,t.name,o.startDate,o.endDate);R.success("Holiday request submitted successfully!"),r({startDate:"",endDate:"",reason:""}),i(!1),d()}catch(w){console.error("Error submitting holiday request:",w),R.error("Failed to submit holiday request")}finally{u(!1)}},p=k=>{const w={pending:"status-pending",approved:"status-approved",rejected:"status-rejected"};return e.jsx("span",{className:`status-badge ${w[k]||"status-pending"}`,children:k.charAt(0).toUpperCase()+k.slice(1)})};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Your Holiday Requests"}),e.jsxs("button",{onClick:()=>i(!a),className:"btn btn-primary",children:[e.jsx(ge,{size:16}),a?"Cancel":"Request Holiday"]})]}),a&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"New Holiday Request"}),e.jsxs("form",{onSubmit:j,children:[e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Start Date"}),e.jsx("input",{type:"date",name:"startDate",value:o.startDate,onChange:y,className:"form-input",required:!0,min:W(new Date,"yyyy-MM-dd")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"End Date"}),e.jsx("input",{type:"date",name:"endDate",value:o.endDate,onChange:y,className:"form-input",required:!0,min:o.startDate||W(new Date,"yyyy-MM-dd")})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Reason for Holiday"}),e.jsx("textarea",{name:"reason",value:o.reason,onChange:y,className:"form-textarea",placeholder:"Please provide the reason for your holiday request...",required:!0})]}),e.jsxs("div",{className:"flex justify-between align-center",children:[e.jsx("div",{style:{color:"#666"},children:o.startDate&&o.endDate&&e.jsxs("span",{children:["Total Days: ",e.jsx("strong",{children:b()})]})}),e.jsx("button",{type:"submit",className:"btn btn-success",disabled:l||b()<=0,children:l?"Submitting...":"Submit Request"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Request History"}),s.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Start Date"}),e.jsx("th",{children:"End Date"}),e.jsx("th",{children:"Days"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Admin Remarks"}),e.jsx("th",{children:"Request Date"})]})}),e.jsx("tbody",{children:s.map(k=>e.jsxs("tr",{children:[e.jsx("td",{children:k.startDate}),e.jsx("td",{children:k.endDate}),e.jsx("td",{children:e.jsx("span",{style:{fontWeight:"bold",color:"#3498db",padding:"2px 8px",background:"#e3f2fd",borderRadius:"12px",fontSize:"12px"},children:k.days})}),e.jsx("td",{style:{maxWidth:"200px"},children:k.reason.length>50?`${k.reason.substring(0,50)}...`:k.reason}),e.jsx("td",{children:p(k.status)}),e.jsx("td",{style:{maxWidth:"150px"},children:k.adminRemarks||e.jsx("span",{style:{color:"#999",fontStyle:"italic"},children:"Pending review"})}),e.jsx("td",{children:k.requestDate})]},k.id))})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(se,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No holiday requests submitted yet"})]})]})]})},Wi=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState([]),[o,r]=x.useState({status:"all",date:"all"}),[l,u]=x.useState(null),[d,y]=x.useState(!1),[b,j]=x.useState({status:"",reason:""}),[p,k]=x.useState(!1),[w,g]=x.useState(!1),[f,E]=x.useState({});x.useEffect(()=>{D()},[t]),x.useEffect(()=>{m()},[s,o]);const D=async()=>{try{const h=await Te.getUserTasks(t.uid);n(h)}catch(h){console.error("Error loading tasks:",h)}},m=()=>{let h=[...s];o.status!=="all"&&(h=h.filter(F=>F.status===o.status));const c=W(new Date,"yyyy-MM-dd"),I=new Date,A=new Date(I.setDate(I.getDate()-I.getDay())),B=new Date(I.getFullYear(),I.getMonth(),1);switch(o.date){case"today":h=h.filter(F=>F.date===c);break;case"week":h=h.filter(F=>new Date(F.date)>=A);break;case"month":h=h.filter(F=>new Date(F.date)>=B);break}i(h)},N=(h,c)=>{r(I=>({...I,[h]:c}))},v=h=>{u(h),j({status:h.status,reason:h.reason||""}),y(!0)},O=async()=>{if(b.status==="not-achieved"&&!b.reason.trim()){R.error("Please provide a reason for not completing the task");return}k(!0);try{await Te.updateTaskStatus(l.id,b.status,b.reason,b.reason),b.status==="completed"?await Di(l.assignedBy,l.description,t.name):await Oi(l.assignedBy,l.description,t.name,b.status),R.success("Task updated successfully!"),y(!1),u(null),j({status:"",reason:""}),D()}catch(h){console.error("Error updating task:",h),R.error("Failed to update task")}finally{k(!1)}},S=h=>{const c={pending:{class:"status-pending",icon:V,text:"Pending"},completed:{class:"status-approved",icon:te,text:"Completed"},"not-achieved":{class:"status-rejected",icon:re,text:"Not Achieved"}},I=c[h]||c.pending,A=I.icon;return e.jsxs("span",{className:`status-badge ${I.class}`,children:[e.jsx(A,{size:12,style:{marginRight:"4px"}}),I.text]})},_=h=>{switch(h){case"high":return"#e74c3c";case"medium":return"#f39c12";case"low":return"#27ae60";default:return"#95a5a6"}},P=h=>{E({title:"Task Description",content:`${h.description}

${h.details||""}`.trim(),type:"details",employeeName:`Task for ${t.name}`,date:h.date,time:h.day}),g(!0)},T=h=>{E({title:"Task Reason/Comments",content:h.reason,type:"remarks",employeeName:`Task for ${t.name}`,date:h.date,time:h.day}),g(!0)};return e.jsxs("div",{className:"content",children:[e.jsx("h2",{style:{marginBottom:"30px",color:"#333"},children:"My Tasks"}),e.jsxs("div",{className:"card mb-20",children:[e.jsxs("div",{className:"flex align-center gap-10 mb-20",children:[e.jsx(dt,{size:20,style:{color:"#666"}}),e.jsx("h3",{style:{margin:0,color:"#333"},children:"Filters"})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px"},children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:o.status,onChange:h=>N("status",h.target.value),className:"form-select",children:[e.jsx("option",{value:"all",children:"All Tasks"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"not-achieved",children:"Not Achieved"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Date Range"}),e.jsxs("select",{value:o.date,onChange:h=>N("date",h.target.value),className:"form-select",children:[e.jsx("option",{value:"all",children:"All Dates"}),e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"})]})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Tasks (",a.length,")"]}),a.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Day"}),e.jsx("th",{children:"Description"}),e.jsx("th",{children:"Priority"}),e.jsx("th",{children:"File"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:a.map(h=>{var c;return e.jsxs("tr",{children:[e.jsx("td",{children:h.date}),e.jsx("td",{children:h.day}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>P(h),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task description and details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:_(h.priority)},children:((c=h.priority)==null?void 0:c.toUpperCase())||"NORMAL"})}),e.jsx("td",{children:h.fileName?e.jsxs("span",{style:{fontSize:"12px",color:"#3498db"},children:["📎 ",h.fileName]}):e.jsx("span",{style:{color:"#999"},children:"No file"})}),e.jsx("td",{children:S(h.status)}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>T(h),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:h.reason?1:.6},title:h.reason?"View reason/comments":"No reason provided",children:[e.jsx(xe,{size:14}),h.reason?"View Reason":"No Reason"]})}),e.jsx("td",{children:e.jsx("button",{onClick:()=>v(h),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},children:"Update"})})]},h.id)})})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(ke,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No tasks found matching the current filters"})]})]}),d&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0,0,0,0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:e.jsxs("div",{style:{background:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"500px"},children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Update Task Status"}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Task Description"}),e.jsx("div",{style:{padding:"10px",background:"#f8f9fa",borderRadius:"4px",marginBottom:"15px"},children:l==null?void 0:l.description})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Status"}),e.jsxs("select",{value:b.status,onChange:h=>j(c=>({...c,status:h.target.value})),className:"form-select",children:[e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"not-achieved",children:"Not Achieved"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsxs("label",{className:"form-label",children:[b.status==="not-achieved"?"Reason for Not Completing (Required)":"Reason/Comments",b.status==="not-achieved"&&e.jsx("span",{style:{color:"#e74c3c"},children:" *"})]}),e.jsx("textarea",{value:b.reason,onChange:h=>j(c=>({...c,reason:h.target.value})),className:"form-textarea",placeholder:b.status==="not-achieved"?"Please explain why the task could not be completed...":"Provide reason for status change or additional comments...",required:b.status==="not-achieved",style:{borderColor:b.status==="not-achieved"&&!b.reason.trim()?"#e74c3c":void 0}}),b.status==="not-achieved"&&!b.reason.trim()&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:"Reason is required when marking task as not achieved"})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("button",{onClick:()=>y(!1),className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:O,className:"btn btn-primary",disabled:p||b.status==="not-achieved"&&!b.reason.trim(),children:p?"Updating...":"Update Task"})]})]})}),e.jsx(Fe,{isOpen:w,onClose:()=>g(!1),title:f.title,content:f.content,type:f.type,employeeName:f.employeeName,date:f.date,time:f.time})]})},$t=({onNavigate:t})=>{const{userProfile:s}=Q(),[n,a]=x.useState({totalEmployees:0,totalAttendance:0,pendingLateCheckins:0,pendingReports:0,pendingHolidays:0,totalTasks:0}),[i,o]=x.useState([]),[r,l]=x.useState(!0);x.useEffect(()=>{(s==null?void 0:s.role)==="admin"&&(u(),d())},[s]);const u=async()=>{try{const[p,k,w,g,f]=await Promise.all([z.getAll(L.USERS),z.getAll(L.ATTENDANCE),z.getAll(L.PROGRESS_REPORTS),z.getAll(L.HOLIDAY_REQUESTS),z.getAll(L.TASKS)]),E=p.filter(N=>N.role==="employee"),D=w.filter(N=>N.status==="submitted"),m=g.filter(N=>N.status==="pending");a({totalEmployees:E.length,totalAttendance:k.length,pendingLateCheckins:0,pendingReports:D.length,pendingHolidays:m.length,totalTasks:f.length})}catch(p){console.error("Error loading admin stats:",p)}finally{l(!1)}},d=async()=>{try{const[p,k]=await Promise.all([z.getAll(L.PROGRESS_REPORTS),z.getAll(L.HOLIDAY_REQUESTS)]),w=[...p.map(g=>({...g,type:"progress-report",title:"Progress Report",description:`${g.employeeName} submitted progress report`})),...k.map(g=>({...g,type:"holiday-request",title:"Holiday Request",description:`${g.employeeName} requested ${g.days} days leave`}))].sort((g,f)=>{var E,D;return new Date(((E=f.createdAt)==null?void 0:E.seconds)*1e3)-new Date(((D=g.createdAt)==null?void 0:D.seconds)*1e3)}).slice(0,10);o(w)}catch(p){console.error("Error loading recent activity:",p)}},y=({icon:p,title:k,value:w,color:g="#3498db",trend:f})=>e.jsxs("div",{className:"stat-card",children:[e.jsxs("div",{className:"flex justify-between align-center mb-10",children:[e.jsx(p,{size:32,style:{color:g}}),f&&e.jsx(hn,{size:16,style:{color:"#27ae60"}})]}),e.jsx("div",{className:"stat-number",style:{color:g},children:r?"...":w}),e.jsx("div",{className:"stat-label",children:k})]}),b=p=>{switch(p){case"progress-report":return e.jsx(he,{size:16,style:{color:"#3498db"}});case"holiday-request":return e.jsx(se,{size:16,style:{color:"#9b59b6"}});default:return e.jsx(V,{size:16,style:{color:"#95a5a6"}})}},j=p=>{switch(p){case"pending":return"#f39c12";case"approved":return"#27ae60";case"rejected":return"#e74c3c";default:return"#95a5a6"}};return(s==null?void 0:s.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx(XCircle,{size:64,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{style:{color:"#e74c3c"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to access the admin panel."})]})}):e.jsxs("div",{className:"content",children:[e.jsx("h2",{style:{marginBottom:"30px",color:"#333"},children:"Admin Dashboard"}),e.jsxs("div",{className:"stats-grid",children:[e.jsx(y,{icon:ue,title:"Total Employees",value:n.totalEmployees,color:"#27ae60"}),e.jsx(y,{icon:V,title:"Total Attendance Records",value:n.totalAttendance,color:"#3498db"}),e.jsx(y,{icon:Ae,title:"Pending Late Check-ins",value:n.pendingLateCheckins,color:"#f39c12"}),e.jsx(y,{icon:he,title:"Pending Reports",value:n.pendingReports,color:"#9b59b6"}),e.jsx(y,{icon:se,title:"Pending Holidays",value:n.pendingHolidays,color:"#e74c3c"}),e.jsx(y,{icon:ke,title:"Total Tasks",value:n.totalTasks,color:"#34495e"})]}),e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Quick Actions"}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px"},children:[e.jsxs("button",{className:"btn btn-primary",style:{padding:"15px"},onClick:()=>t&&t("employees"),children:[e.jsx(ue,{size:18,style:{marginRight:"8px"}}),"Manage Employees"]}),e.jsxs("button",{className:"btn btn-info",style:{padding:"15px"},onClick:()=>t&&t("admin-timetable"),children:[e.jsx(se,{size:18,style:{marginRight:"8px"}}),"Manage Time Tables"]}),e.jsxs("button",{className:"btn btn-warning",style:{padding:"15px"},onClick:()=>t&&t("late-records"),children:[e.jsx(Ae,{size:18,style:{marginRight:"8px"}}),"Review Late Check-ins"]}),e.jsxs("button",{className:"btn btn-success",style:{padding:"15px"},onClick:()=>t&&t("holiday-management"),children:[e.jsx(se,{size:18,style:{marginRight:"8px"}}),"Approve Holidays"]}),e.jsxs("button",{className:"btn btn-info",style:{padding:"15px"},onClick:()=>t&&t("admin-progress-reports"),children:[e.jsx(he,{size:18,style:{marginRight:"8px"}}),"Progress Reports"]}),e.jsxs("button",{className:"btn btn-secondary",style:{padding:"15px"},onClick:()=>t&&t("task-management"),children:[e.jsx(ke,{size:18,style:{marginRight:"8px"}}),"Assign Tasks"]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Recent Activity"}),i.length>0?e.jsx("div",{style:{maxHeight:"400px",overflowY:"auto"},children:i.map((p,k)=>{var w;return e.jsxs("div",{style:{padding:"15px",borderBottom:"1px solid #eee",display:"flex",alignItems:"center",gap:"15px"},children:[e.jsx("div",{children:b(p.type)}),e.jsxs("div",{style:{flex:1},children:[e.jsx("div",{style:{fontWeight:"500",marginBottom:"4px"},children:p.title}),e.jsx("div",{style:{fontSize:"14px",color:"#666"},children:p.description})]}),e.jsxs("div",{style:{textAlign:"right"},children:[e.jsx("div",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:j(p.status),marginBottom:"4px"},children:(w=p.status)==null?void 0:w.toUpperCase()}),e.jsx("div",{style:{fontSize:"12px",color:"#999"},children:p.createdAt?new Date(p.createdAt.seconds*1e3).toLocaleDateString():"Recently"})]})]},k)})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No recent activity"})]})]})]})},$i=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState(!1),[o,r]=x.useState(!1),[l,u]=x.useState(""),[d,y]=x.useState({email:"",password:"",name:"",employeeId:"",role:"employee",isActive:!0}),[b,j]=x.useState(!1),[p,k]=x.useState({}),[w,g]=x.useState(null),[f,E]=x.useState({});x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&D()},[t]);const D=async()=>{try{const c=await z.getAll(L.USERS);n(c)}catch(c){console.error("Error loading employees:",c),R.error("Failed to load employees")}},m=c=>{y({...d,[c.target.name]:c.target.value}),p[c.target.name]&&k({...p,[c.target.name]:""})},N=()=>{const c={};return d.email?/\S+@\S+\.\S+/.test(d.email)||(c.email="Email is invalid"):c.email="Email is required",d.password?d.password.length<6&&(c.password="Password must be at least 6 characters"):c.password="Password is required",d.name||(c.name="Name is required"),d.employeeId||(c.employeeId="Employee ID is required"),k(c),Object.keys(c).length===0},v=async c=>{if(c.preventDefault(),!!N()){r(!0);try{console.log("Creating Firebase Auth user with secondary auth...");const A=(await Ct(Me,d.email,d.password)).user;console.log("Firebase Auth user created:",A.uid);const B={uid:A.uid,email:d.email,name:d.name,employeeId:d.employeeId,role:d.role,isActive:d.isActive,createdAt:new Date().toISOString(),createdBy:t.uid};console.log("Creating Firestore profile..."),await je.createUser(B),console.log("Firestore profile created successfully"),console.log("Signing out from secondary auth..."),await Me.signOut(),g({id:A.uid,name:d.name,email:d.email,password:d.password,employeeId:d.employeeId,role:d.role}),R.success(`Employee ${d.name} added successfully! They can now login with their credentials.`),y({email:"",password:"",name:"",employeeId:"",role:"employee",isActive:!0}),i(!1),D()}catch(I){console.error("Error adding employee:",I);let A="Failed to add employee";switch(I.code){case"auth/email-already-in-use":A="Email is already registered";break;case"auth/weak-password":A="Password must be at least 6 characters";break;case"auth/invalid-email":A="Invalid email address";break;default:A=I.message}R.error(A),k({submit:A})}finally{r(!1)}}},O=async(c,I)=>{try{await z.update(L.USERS,c,{isActive:!I}),R.success(`Employee ${I?"deactivated":"activated"} successfully`),D()}catch(A){console.error("Error updating employee status:",A),R.error("Failed to update employee status")}},S=async c=>{if(!c.password){R.error("Cannot migrate: Password not available. Please create a new account.");return}E(I=>({...I,[c.id]:"migrating"}));try{const A=(await Ct(Me,c.email,c.password)).user;await z.update(L.USERS,c.id,{uid:A.uid,migratedAt:new Date().toISOString(),migratedBy:t.uid});const B={...c,uid:A.uid,migratedAt:new Date().toISOString(),migratedBy:t.uid};await je.createUser(B),await Me.signOut(),E(F=>({...F,[c.id]:"success"})),R.success(`Employee ${c.name} migrated successfully!`),D()}catch(I){console.error("Migration error:",I),E(B=>({...B,[c.id]:"failed"}));let A="Migration failed";I.code==="auth/email-already-in-use"&&(A="Email already has a Firebase account"),R.error(A)}},_=c=>!c.uid||c.uid.startsWith("emp_"),P=c=>{if(w&&w.id===c.id){T(w);return}const I={name:c.name||"Employee",email:c.email,password:"[Contact Admin for Password]",employeeId:c.employeeId||"N/A",role:c.role||"employee"};T(I)},T=c=>{const I=encodeURIComponent("Your Employee Login Credentials - Employee Management System"),A=encodeURIComponent(`Dear ${c.name},

Welcome to the Employee Management System!

Your login credentials:

📧 Email: ${c.email}
🔑 Password: ${c.password}
👤 Employee ID: ${c.employeeId}
🎯 Role: ${c.role.charAt(0).toUpperCase()+c.role.slice(1)}

For security reasons:
1. Please change your password after your first login
2. Keep your credentials secure and do not share them
3. Contact the administrator if you face any login issues

Please use these credentials to log into the system at: ${window.location.origin}

Best regards,
Admin Team
Employee Management System`),B=`mailto:${c.email}?subject=${I}&body=${A}`,F=document.createElement("a");F.href=B,F.target="_blank",F.rel="noopener noreferrer",document.body.appendChild(F),F.click(),document.body.removeChild(F),R.success(`Email client opened with credentials for ${c.name}`)},h=s.filter(c=>{var I,A,B;return((I=c.name)==null?void 0:I.toLowerCase().includes(l.toLowerCase()))||((A=c.email)==null?void 0:A.toLowerCase().includes(l.toLowerCase()))||((B=c.employeeId)==null?void 0:B.toLowerCase().includes(l.toLowerCase()))});return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx(ue,{size:64,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{style:{color:"#e74c3c"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to manage employees."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Employee Management"}),e.jsxs("button",{onClick:()=>i(!a),className:"btn btn-primary",children:[e.jsx(ge,{size:16}),a?"Cancel":"Add Employee"]})]}),a&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Add New Employee"}),e.jsxs("form",{onSubmit:v,children:[e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Full Name *"}),e.jsx("input",{type:"text",name:"name",value:d.name,onChange:m,className:"form-input",placeholder:"Enter full name"}),p.name&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.name})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Employee ID *"}),e.jsx("input",{type:"text",name:"employeeId",value:d.employeeId,onChange:m,className:"form-input",placeholder:"e.g., EMP001"}),p.employeeId&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.employeeId})]})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Email Address *"}),e.jsx("input",{type:"email",name:"email",value:d.email,onChange:m,className:"form-input",placeholder:"Enter email address"}),p.email&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.email})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Password *"}),e.jsxs("div",{style:{position:"relative"},children:[e.jsx("input",{type:b?"text":"password",name:"password",value:d.password,onChange:m,className:"form-input",placeholder:"Enter password",style:{paddingRight:"40px"}}),e.jsx("button",{type:"button",onClick:()=>j(!b),style:{position:"absolute",right:"10px",top:"50%",transform:"translateY(-50%)",background:"none",border:"none",cursor:"pointer",color:"#666"},children:b?e.jsx(Zt,{size:16}):e.jsx(fe,{size:16})})]}),p.password&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"12px",marginTop:"5px"},children:p.password})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Role"}),e.jsxs("select",{name:"role",value:d.role,onChange:m,className:"form-select",style:{maxWidth:"200px"},children:[e.jsx("option",{value:"employee",children:"Employee"}),e.jsx("option",{value:"admin",children:"Administrator"})]})]}),p.submit&&e.jsx("div",{style:{color:"#e74c3c",fontSize:"14px",marginBottom:"15px",padding:"10px",background:"#fdf2f2",border:"1px solid #fecaca",borderRadius:"4px"},children:p.submit}),e.jsxs("div",{className:"flex justify-between align-center",children:[e.jsx("div",{style:{color:"#666",fontSize:"14px"},children:"* Required fields"}),e.jsx("button",{type:"submit",className:"btn btn-success",disabled:o,children:o?"Adding Employee...":"Add Employee"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsxs("h3",{style:{color:"#333"},children:["All Employees (",h.length,")"]}),e.jsxs("div",{style:{position:"relative",width:"300px"},children:[e.jsx(Qe,{size:16,style:{position:"absolute",left:"10px",top:"50%",transform:"translateY(-50%)",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search employees...",value:l,onChange:c=>u(c.target.value),className:"form-input",style:{paddingLeft:"35px"}})]})]}),h.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Employee ID"}),e.jsx("th",{children:"Name"}),e.jsx("th",{children:"Email"}),e.jsx("th",{children:"Role"}),e.jsx("th",{children:"Account Type"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:h.map(c=>{var I;return e.jsxs("tr",{children:[e.jsx("td",{style:{fontWeight:"bold",color:"#3498db"},children:c.employeeId||"N/A"}),e.jsx("td",{children:c.name||"N/A"}),e.jsx("td",{children:c.email}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:c.role==="admin"?"#e74c3c":"#3498db"},children:((I=c.role)==null?void 0:I.toUpperCase())||"EMPLOYEE"})}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:_(c)?"#f39c12":"#27ae60"},children:_(c)?"LEGACY":"FIREBASE"})}),e.jsx("td",{children:e.jsx("span",{className:`status-badge ${c.isActive!==!1?"status-approved":"status-rejected"}`,children:c.isActive!==!1?"Active":"Inactive"})}),e.jsx("td",{children:e.jsxs("div",{className:"flex gap-10",style:{flexWrap:"wrap"},children:[e.jsx("button",{onClick:()=>P(c),className:"btn btn-info",style:{padding:"5px 10px",fontSize:"12px"},title:"Send Credentials via Email",children:e.jsx(pn,{size:14})}),_(c)&&e.jsx("button",{onClick:()=>S(c),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},title:"Migrate to Firebase Auth",disabled:f[c.id]==="migrating",children:f[c.id]==="migrating"?"...":"🔄"}),e.jsx("button",{onClick:()=>O(c.id,c.isActive),className:`btn ${c.isActive!==!1?"btn-warning":"btn-success"}`,style:{padding:"5px 10px",fontSize:"12px"},title:c.isActive!==!1?"Deactivate":"Activate",children:c.isActive!==!1?e.jsx(mn,{size:14}):e.jsx(xn,{size:14})})]})})]},c.id)})})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(ue,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No employees found"})]})]})]})},qi=()=>{var v,O;const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState(""),[o,r]=x.useState({}),[l,u]=x.useState(!1),[d,y]=x.useState(!1),[b,j]=x.useState(!1),p=["Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"];x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&k()},[t]),x.useEffect(()=>{a?w():(r({}),y(!1))},[a]);const k=async()=>{try{u(!0);const _=(await z.getAll(L.USERS)).filter(P=>P.role==="employee");n(_)}catch(S){console.error("Error loading employees:",S),R.error("Failed to load employees")}finally{u(!1)}},w=async()=>{try{u(!0);const S=await z.getWhere(L.TIME_TABLES,"userId","==",a);if(S.length>0)r(S[0].schedule||{});else{const _={};p.forEach(P=>{_[P]=[]}),r(_)}}catch(S){console.error("Error loading employee timetable:",S),R.error("Failed to load employee timetable")}finally{u(!1)}},g=S=>{r(_=>({..._,[S]:[..._[S]||[],{checkIn:"",checkOut:""}]}))},f=(S,_)=>{r(P=>({...P,[S]:P[S].filter((T,h)=>h!==_)}))},E=(S,_,P,T)=>{r(h=>({...h,[S]:h[S].map((c,I)=>I===_?{...c,[P]:T}:c)}))},D=S=>{if(!o[S]||o[S].length===0)return"00:00:00";let _=0;o[S].forEach(c=>{if(c.checkIn&&c.checkOut){const I=new Date(`2000-01-01T${c.checkIn}`),A=new Date(`2000-01-01T${c.checkOut}`);if(A>I){const B=A-I;_+=Math.floor(B/1e3)}}});const P=Math.floor(_/3600),T=Math.floor(_%3600/60),h=_%60;return`${P.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}:${h.toString().padStart(2,"0")}`},m=()=>{let S=0;p.forEach(h=>{o[h]&&o[h].length>0&&o[h].forEach(c=>{if(c.checkIn&&c.checkOut){const I=new Date(`2000-01-01T${c.checkIn}`),A=new Date(`2000-01-01T${c.checkOut}`);if(A>I){const B=A-I;S+=Math.floor(B/1e3)}}})});const _=Math.floor(S/3600),P=Math.floor(S%3600/60),T=S%60;return`${_.toString().padStart(2,"0")}:${P.toString().padStart(2,"0")}:${T.toString().padStart(2,"0")}`},N=async()=>{if(!a){R.error("Please select an employee first");return}try{j(!0);const S=s.find(_=>_.uid===a);await be.saveAdminTimetable(a,{employeeId:(S==null?void 0:S.employeeId)||"Unknown",name:(S==null?void 0:S.name)||"Unknown"},o,t.uid),R.success("Time table saved successfully! Employee has been notified."),y(!1)}catch(S){console.error("Error saving timetable:",S),R.error("Failed to save time table")}finally{j(!1)}};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[e.jsx(ae,{size:48,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsxs("div",{children:[e.jsx("h2",{style:{margin:"0 0 5px 0",color:"#333"},children:"Employee Time Table Management"}),e.jsxs("p",{style:{color:"#666",fontSize:"14px",margin:0},children:["Current Week: ",be.getCurrentWeekId()," | Admin can modify any timetable"]})]}),a&&e.jsx("div",{style:{display:"flex",gap:"10px"},children:d?e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>y(!1),className:"btn btn-secondary",disabled:b,children:[e.jsx(re,{size:16,style:{marginRight:"5px"}}),"Cancel"]}),e.jsxs("button",{onClick:N,className:"btn btn-success",disabled:b,children:[e.jsx(gn,{size:16,style:{marginRight:"5px"}}),b?"Saving...":"Save Changes"]})]}):e.jsxs("button",{onClick:()=>y(!0),className:"btn btn-primary",disabled:l,children:[e.jsx(es,{size:16,style:{marginRight:"5px"}}),"Edit Time Table"]})})]}),e.jsxs("div",{className:"card",style:{marginBottom:"20px"},children:[e.jsx("h3",{style:{marginBottom:"15px",color:"#333"},children:"Select Employee"}),e.jsxs("div",{style:{display:"flex",gap:"20px",alignItems:"end"},children:[e.jsxs("div",{style:{minWidth:"300px"},children:[e.jsx("label",{style:{display:"block",marginBottom:"5px",fontWeight:"bold"},children:"Employee"}),e.jsxs("select",{value:a,onChange:S=>i(S.target.value),className:"form-select",style:{width:"100%"},disabled:l,children:[e.jsx("option",{value:"",children:"Select an employee..."}),s.map(S=>e.jsxs("option",{value:S.uid,children:[S.name," (",S.employeeId,")"]},S.uid))]})]}),a&&e.jsxs("div",{style:{padding:"10px 15px",backgroundColor:"#e8f5e8",borderRadius:"5px",border:"1px solid #c3e6c3"},children:[e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:"Total Weekly Hours"}),e.jsx("div",{style:{fontSize:"18px",fontWeight:"bold",color:"#27ae60",fontFamily:"monospace"},children:m()})]})]})]}),!a&&e.jsxs("div",{className:"card",style:{textAlign:"center",padding:"50px"},children:[e.jsx(ue,{size:48,style:{color:"#3498db",marginBottom:"20px",opacity:.5}}),e.jsx("h3",{style:{color:"#666",marginBottom:"10px"},children:"No Employee Selected"}),e.jsx("p",{style:{color:"#999",marginBottom:"0"},children:"Please select an employee from the dropdown above to view and manage their time table."})]}),a&&e.jsx("div",{className:"card",children:l?e.jsxs("div",{style:{textAlign:"center",padding:"40px"},children:[e.jsx(V,{size:48,style:{color:"#3498db",marginBottom:"20px"}}),e.jsx("h3",{children:"Loading Time Table"}),e.jsx("p",{children:"Loading employee time table..."})]}):e.jsxs(e.Fragment,{children:[e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f8f9fa",borderRadius:"5px"},children:[e.jsxs("h3",{style:{margin:"0 0 5px 0",color:"#333"},children:["Time Table for ",(v=s.find(S=>S.uid===a))==null?void 0:v.name]}),e.jsxs("p",{style:{margin:0,color:"#666",fontSize:"14px"},children:["Employee ID: ",(O=s.find(S=>S.uid===a))==null?void 0:O.employeeId]})]}),p.map(S=>e.jsxs("div",{style:{marginBottom:"30px",paddingBottom:"20px",borderBottom:"1px solid #eee"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"15px"},children:[e.jsx("h4",{style:{color:"#333",margin:0},children:S}),e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px"},children:[e.jsxs("span",{style:{color:"#666",fontSize:"14px",fontFamily:"monospace"},children:["Total: ",D(S)]}),d&&e.jsxs("button",{onClick:()=>g(S),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},children:[e.jsx(ge,{size:14,style:{marginRight:"3px"}}),"Add Slot"]})]})]}),o[S]&&o[S].length>0?e.jsx("div",{style:{display:"grid",gap:"10px"},children:o[S].map((_,P)=>e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px",padding:"15px",backgroundColor:d?"#fff3cd":"#f8f9fa",borderRadius:"6px",border:d?"1px solid #ffeaa7":"1px solid #e9ecef"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"15px",flex:1},children:[e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666",display:"block",marginBottom:"3px"},children:"Check In"}),e.jsx("input",{type:"time",value:_.checkIn,onChange:T=>E(S,P,"checkIn",T.target.value),className:"form-input",style:{width:"130px"},disabled:!d})]}),e.jsxs("div",{children:[e.jsx("label",{style:{fontSize:"12px",color:"#666",display:"block",marginBottom:"3px"},children:"Check Out"}),e.jsx("input",{type:"time",value:_.checkOut,onChange:T=>E(S,P,"checkOut",T.target.value),className:"form-input",style:{width:"130px"},disabled:!d})]}),_.checkIn&&_.checkOut&&e.jsxs("div",{style:{padding:"8px 12px",backgroundColor:"#e8f5e8",borderRadius:"4px"},children:[e.jsx("div",{style:{fontSize:"11px",color:"#666"},children:"Duration"}),e.jsx("div",{style:{fontSize:"13px",fontWeight:"bold",color:"#27ae60",fontFamily:"monospace"},children:(()=>{const T=new Date(`2000-01-01T${_.checkIn}`),h=new Date(`2000-01-01T${_.checkOut}`);if(h>T){const c=h-T,I=Math.floor(c/1e3),A=Math.floor(I/3600),B=Math.floor(I%3600/60),F=I%60;return`${A.toString().padStart(2,"0")}:${B.toString().padStart(2,"0")}:${F.toString().padStart(2,"0")}`}return"00:00:00"})()})]})]}),d&&e.jsx("button",{onClick:()=>f(S,P),className:"btn btn-danger",style:{padding:"8px",minWidth:"auto"},children:e.jsx(De,{size:16})})]},P))}):e.jsxs("div",{style:{textAlign:"center",padding:"30px",color:"#666",backgroundColor:"#f8f9fa",borderRadius:"6px",border:"2px dashed #dee2e6"},children:[e.jsx(se,{size:32,style:{opacity:.3,marginBottom:"10px"}}),e.jsxs("p",{style:{fontSize:"14px",margin:"0 0 10px 0"},children:["No time slots set for ",S]}),d&&e.jsxs("button",{onClick:()=>g(S),className:"btn btn-primary",style:{padding:"8px 15px",fontSize:"12px"},children:[e.jsx(ge,{size:14,style:{marginRight:"5px"}}),"Add First Time Slot"]})]})]},S))]})})]})},Vi="modulepreload",Yi=function(t,s){return new URL(t,s).href},qt={},Vt=function(s,n,a){let i=Promise.resolve();if(n&&n.length>0){const r=document.getElementsByTagName("link"),l=document.querySelector("meta[property=csp-nonce]"),u=(l==null?void 0:l.nonce)||(l==null?void 0:l.getAttribute("nonce"));i=Promise.allSettled(n.map(d=>{if(d=Yi(d,a),d in qt)return;qt[d]=!0;const y=d.endsWith(".css"),b=y?'[rel="stylesheet"]':"";if(!!a)for(let k=r.length-1;k>=0;k--){const w=r[k];if(w.href===d&&(!y||w.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${d}"]${b}`))return;const p=document.createElement("link");if(p.rel=y?"stylesheet":Vi,y||(p.as="script"),p.crossOrigin="",p.href=d,u&&p.setAttribute("nonce",u),document.head.appendChild(p),y)return new Promise((k,w)=>{p.addEventListener("load",k),p.addEventListener("error",()=>w(new Error(`Unable to preload CSS for ${d}`)))})}))}function o(r){const l=new Event("vite:preloadError",{cancelable:!0});if(l.payload=r,window.dispatchEvent(l),!l.defaultPrevented)throw r}return i.then(r=>{for(const l of r||[])l.status==="rejected"&&o(l.reason);return s().catch(o)})},Ki=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState(!1),[o,r]=x.useState(""),[l,u]=x.useState("all"),[d,y]=x.useState(null),[b,j]=x.useState(""),[p,k]=x.useState(!1),[w,g]=x.useState(!1),[f,E]=x.useState({});x.useEffect(()=>{t&&(console.log("AdminProgressReports - User Profile:",t),console.log("AdminProgressReports - User Role:",t.role),t.role==="admin"?D():console.log("User is not admin, skipping report loading"))},[t]);const D=async()=>{i(!0);try{console.log("Admin loading progress reports..."),console.log("Current user profile:",t),console.log("User role:",t==null?void 0:t.role),console.log("User UID:",t==null?void 0:t.uid);let T=[];try{console.log("Attempting to fetch with default ordering..."),T=await z.getAll(L.PROGRESS_REPORTS),console.log("Success with default ordering. Fetched reports:",T.length)}catch{console.log("Default ordering failed, trying without ordering...");try{const{collection:I,getDocs:A}=await Vt(async()=>{const{collection:H,getDocs:K}=await import("./firebase-BfSkx5kM.js").then(Z=>Z.K);return{collection:H,getDocs:K}},[],import.meta.url),{db:B}=await Vt(async()=>{const{db:H}=await Promise.resolve().then(()=>wi);return{db:H}},void 0,import.meta.url);T=(await A(I(B,L.PROGRESS_REPORTS))).docs.map(H=>({id:H.id,...H.data()})),console.log("Success without ordering. Fetched reports:",T.length)}catch(I){throw console.error("Simple query also failed:",I),I}}console.log("Raw fetched reports:",T);const h=T.sort((c,I)=>{try{const A=new Date(`${c.date} ${c.time}`);return new Date(`${I.date} ${I.time}`)-A}catch(A){return console.warn("Error sorting reports:",A),0}});n(h),h.length===0?(console.log("No progress reports found"),R.info("No progress reports found. Try submitting a test report first.")):(console.log(`Successfully loaded ${h.length} progress reports`),R.success(`Loaded ${h.length} progress reports`))}catch(T){console.error("Error loading progress reports:",T),console.error("Error details:",T.message),console.error("Error code:",T.code),T.message.includes("permission")||T.message.includes("insufficient")||T.code==="permission-denied"?(R.error("Permission denied. Firebase rules may need updating."),console.log("Permission denied - check Firebase rules for admin access")):T.message.includes("index")||T.code==="failed-precondition"?(R.error("Database index missing. Loading without sorting..."),console.log("Index error - trying alternative query method")):R.error("Failed to load progress reports: "+T.message)}finally{i(!1)}},m=T=>{y(T),j(T.adminRemarks||""),k(!0)},N=async()=>{if(d)try{const T={adminRemarks:b,status:"reviewed",reviewedBy:t.uid,reviewedAt:new Date().toISOString()};await z.update(L.PROGRESS_REPORTS,d.id,T),R.success("Remarks added successfully!"),k(!1),y(null),j(""),D()}catch(T){console.error("Error saving remarks:",T),R.error("Failed to save remarks")}},v=T=>{E({title:"Tasks Completed",content:T.tasksCompleted,type:"details",employeeName:T.employeeName,date:T.date,time:T.time}),g(!0)},O=s.filter(T=>{var I,A,B;const h=((I=T.employeeName)==null?void 0:I.toLowerCase().includes(o.toLowerCase()))||((A=T.employeeId)==null?void 0:A.toLowerCase().includes(o.toLowerCase()))||((B=T.tasksCompleted)==null?void 0:B.toLowerCase().includes(o.toLowerCase())),c=l==="all"||T.status===l;return h&&c}),S=T=>{const h={submitted:{class:"status-pending",icon:V,text:"Submitted"},reviewed:{class:"status-approved",icon:te,text:"Reviewed"},rejected:{class:"status-rejected",icon:Ae,text:"Rejected"}},c=h[T]||h.submitted,I=c.icon;return e.jsxs("span",{className:`status-badge ${c.class}`,style:{display:"flex",alignItems:"center",gap:"5px"},children:[e.jsx(I,{size:12}),c.text]})},P={total:s.length,submitted:s.filter(h=>h.status==="submitted").length,reviewed:s.filter(h=>h.status==="reviewed").length,rejected:s.filter(h=>h.status==="rejected").length};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card",children:[e.jsx("h2",{style:{color:"#e74c3c",marginBottom:"10px"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Employee Progress Reports"}),e.jsx("button",{onClick:D,className:"btn btn-primary",disabled:a,children:a?"Loading...":"Refresh"})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"15px",marginBottom:"20px"},children:[e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#3498db",margin:"0 0 5px 0"},children:P.total}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Total Reports"})]}),e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#f39c12",margin:"0 0 5px 0"},children:P.submitted}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Pending Review"})]}),e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#27ae60",margin:"0 0 5px 0"},children:P.reviewed}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Reviewed"})]}),e.jsxs("div",{className:"card",style:{padding:"15px",textAlign:"center"},children:[e.jsx("h3",{style:{color:"#e74c3c",margin:"0 0 5px 0"},children:P.rejected}),e.jsx("p",{style:{margin:0,color:"#666"},children:"Rejected"})]})]}),e.jsx("div",{className:"card mb-20",children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr auto",gap:"20px",alignItems:"center"},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Qe,{size:16,style:{position:"absolute",left:"10px",top:"50%",transform:"translateY(-50%)",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search by employee name, ID, or tasks...",value:o,onChange:T=>r(T.target.value),className:"form-input",style:{paddingLeft:"35px"}})]}),e.jsxs("select",{value:l,onChange:T=>u(T.target.value),className:"form-select",style:{minWidth:"150px"},children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"submitted",children:"Pending Review"}),e.jsx("option",{value:"reviewed",children:"Reviewed"}),e.jsx("option",{value:"rejected",children:"Rejected"})]})]})}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Progress Reports (",O.length,")"]}),O.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Date"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Employee"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"Tasks Completed"}),e.jsx("th",{style:{width:"150px",minWidth:"150px"},children:"File"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Actions"})]})}),e.jsx("tbody",{children:O.map(T=>e.jsxs("tr",{children:[e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(se,{size:14,style:{color:"#666"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px"},children:W(new Date(T.date),"MMM dd")}),e.jsx("div",{style:{fontSize:"11px",color:"#666"},children:T.time})]})]})}),e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(gt,{size:14,style:{color:"#3498db"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px",maxWidth:"120px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:T.employeeName}),e.jsxs("div",{style:{fontSize:"11px",color:"#666"},children:["ID: ",T.employeeId]})]})]})}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:e.jsxs("button",{onClick:()=>v(T),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{style:{padding:"12px 8px",textAlign:"center"},children:T.fileName?e.jsxs("div",{style:{display:"flex",flexDirection:"column",alignItems:"center",gap:"6px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"6px",marginBottom:"4px"},children:[e.jsx(he,{size:16,style:{color:"#3498db"}}),e.jsxs("div",{style:{textAlign:"left"},children:[e.jsx("div",{style:{fontSize:"12px",fontWeight:"bold",maxWidth:"120px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:T.fileName}),e.jsx("div",{style:{fontSize:"10px",color:"#666"},children:T.fileSize?`${(T.fileSize/1024/1024).toFixed(2)} MB`:""})]})]}),T.fileData?e.jsxs("button",{onClick:()=>{try{console.log("Admin downloading file:",T.fileName),Ye.createDownloadLink(T.fileName,T.fileData),R.success(`Downloaded: ${T.fileName}`)}catch(h){console.error("Admin download error:",h),R.error("Failed to download file: "+h.message)}},style:{background:"#27ae60",color:"white",border:"none",borderRadius:"4px",padding:"6px 12px",fontSize:"11px",cursor:"pointer",display:"flex",alignItems:"center",gap:"4px",transition:"background-color 0.2s"},onMouseOver:h=>h.target.style.backgroundColor="#219a52",onMouseOut:h=>h.target.style.backgroundColor="#27ae60",title:"Download file",children:[e.jsx(ft,{size:12}),"Download"]}):e.jsx("span",{style:{color:"#ccc",fontSize:"11px",fontStyle:"italic"},children:"File not available"})]}):e.jsx("span",{style:{color:"#999",fontSize:"12px",fontStyle:"italic"},children:"No file attached"})}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:S(T.status)}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:e.jsxs("button",{onClick:()=>m(T),className:"btn btn-secondary",style:{padding:"8px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"4px",margin:"0 auto"},title:"Add/Edit Remarks",children:[e.jsx(xe,{size:14}),"Remarks"]})})]},T.id))})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(he,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No progress reports found"})]})]}),p&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,background:"rgba(0,0,0,0.5)",display:"flex",alignItems:"center",justifyContent:"center",zIndex:1e3},children:e.jsxs("div",{style:{background:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"600px",maxHeight:"80vh",overflow:"auto"},children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Add Remarks - ",d==null?void 0:d.employeeName]}),e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",background:"#f8f9fa",borderRadius:"4px"},children:[e.jsx("h4",{style:{margin:"0 0 10px 0",color:"#555"},children:"Report Summary:"}),e.jsxs("p",{style:{margin:"5px 0",fontSize:"14px"},children:[e.jsx("strong",{children:"Date:"})," ",d==null?void 0:d.date," at ",d==null?void 0:d.time]}),e.jsxs("p",{style:{margin:"5px 0",fontSize:"14px"},children:[e.jsx("strong",{children:"Tasks Completed:"})," ",d==null?void 0:d.tasksCompleted]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Admin Remarks"}),e.jsx("textarea",{value:b,onChange:T=>j(T.target.value),className:"form-textarea",placeholder:"Add your remarks about this progress report...",style:{minHeight:"120px"}})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>k(!1),className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{onClick:N,className:"btn btn-success",children:"Save Remarks"})]})]})}),e.jsx(Fe,{isOpen:w,onClose:()=>g(!1),title:f.title,content:f.content,type:f.type,employeeName:f.employeeName,date:f.date,time:f.time})]})},Gi=()=>{const{user:t,userProfile:s}=Q(),[n,a]=x.useState([]),[i,o]=x.useState([]),[r,l]=x.useState(!0),[u,d]=x.useState("all"),[y,b]=x.useState("today"),[j,p]=x.useState(""),[k,w]=x.useState(""),[g,f]=x.useState({daily:{}}),[E,D]=x.useState(new Set),[m,N]=x.useState(!1),[v,O]=x.useState({reason:"",type:"",time:"",employee:""});x.useEffect(()=>{S()},[]),x.useEffect(()=>{i.length>0&&P()},[u,y,j,k,i]);const S=async()=>{try{const I=(await z.getAll(L.USERS)).filter(A=>A.role==="employee");o(I)}catch(c){console.error("Error loading employees:",c),R.error("Failed to load employees")}},_=()=>{const c=new Date;switch(y){case"today":return{start:We(c),end:Ue(c)};case"week":return{start:ct(c,{weekStartsOn:1}),end:Qt(c,{weekStartsOn:1})};case"month":return{start:yn(c),end:fn(c)};case"custom":return{start:j?We(new Date(j)):We(c),end:k?Ue(new Date(k)):Ue(c)};default:return{start:We(c),end:Ue(c)}}},P=async()=>{try{l(!0);const{start:c,end:I}=_();console.log("Loading attendance data with filters:",{selectedEmployee:u,dateRange:y,start:c.toISOString(),end:I.toISOString()});let A=[];u==="all"?A=await z.getAll(L.ATTENDANCE):A=await z.getWhere(L.ATTENDANCE,"userId","==",u),console.log("Raw attendance records from Firebase:",A.length);const B=A.filter(F=>{if(!F.date)return!1;let H;typeof F.date=="string"?H=new Date(F.date+"T00:00:00"):F.date.toDate?H=F.date.toDate():H=new Date(F.date);const K=new Date(H.getFullYear(),H.getMonth(),H.getDate()),Z=new Date(c.getFullYear(),c.getMonth(),c.getDate()),oe=new Date(I.getFullYear(),I.getMonth(),I.getDate(),23,59,59);return K>=Z&&K<=oe});console.log("Filtered attendance records:",B.length),a(B),T(B)}catch(c){console.error("Error loading attendance data:",c),R.error("Failed to load attendance data: "+c.message)}finally{l(!1)}},T=c=>{try{const I={};c.forEach(A=>{if(!A||typeof A!="object"||!A.userId||!A.date){console.warn("Invalid record found:",A);return}if(!A.type||A.type!=="checkin"&&A.type!=="checkout"){console.warn("Record missing type or invalid type:",A);return}const B=`${A.userId}_${A.date}`;if(!I[B]){const F=i.find(H=>H.uid===A.userId);I[B]={userId:A.userId,date:A.date,employeeName:(F==null?void 0:F.name)||"Unknown Employee",employee:F||{employeeId:"Unknown",name:"Unknown Employee"},records:[]}}I[B].records.push(A)}),Object.values(I).forEach(A=>{try{A.workingHours=Ei.calculateDayWorkingHours(A.records)}catch(B){console.error("Error calculating working hours for day:",A.date,B),A.workingHours={regularHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},lateHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},totalHours:{hours:0,minutes:0,totalMinutes:0,display:"0h 0m"},status:"ERROR"}}}),f({daily:I})}catch(I){console.error("Error processing summary data:",I),f({daily:{}})}},h=()=>{if(n.length===0){R.error("No data to export");return}const c=n.map(H=>{const K=i.find(Z=>Z.uid===H.userId);return{"Employee Name":(K==null?void 0:K.name)||"Unknown","Employee ID":(K==null?void 0:K.employeeId)||"Unknown",Date:H.date,Day:W(He(H.date),"EEEE"),Type:H.type,Time:H.type==="checkin"?H.checkInTime:H.checkOutTime,"Scheduled Time":H.type==="checkin"?H.scheduledCheckIn:H.scheduledCheckOut,"Late Reason":H.lateReason||"",Status:H.lateReason?"Late":"On Time"}}),I=[Object.keys(c[0]).join(","),...c.map(H=>Object.values(H).map(K=>`"${K}"`).join(","))].join(`
`),A=new Blob([I],{type:"text/csv"}),B=window.URL.createObjectURL(A),F=document.createElement("a");F.href=B,F.download=`attendance_report_${W(new Date,"yyyy-MM-dd")}.csv`,F.click(),window.URL.revokeObjectURL(B),R.success("Attendance report exported successfully!")};return r&&i.length===0?e.jsx("div",{className:"content",children:e.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[e.jsx(V,{size:48,style:{color:"#3498db",marginBottom:"20px"}}),e.jsx("h3",{children:"Loading Attendance Management"}),e.jsx("p",{children:"Loading attendance data..."})]})}):(s==null?void 0:s.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{style:{textAlign:"center",padding:"50px"},children:[e.jsx(ae,{size:48,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsx("h2",{style:{margin:0,color:"#333"},children:"Attendance Management"}),e.jsxs("button",{onClick:h,className:"btn btn-primary",style:{display:"flex",alignItems:"center",gap:"8px"},disabled:n.length===0,children:[e.jsx(ft,{size:20}),"Export CSV"]})]}),i.length===0&&e.jsx("div",{className:"card",style:{marginBottom:"20px",backgroundColor:"#fff3cd",border:"1px solid #ffeaa7"},children:e.jsxs("div",{style:{padding:"15px",textAlign:"center"},children:[e.jsx(ae,{size:32,style:{color:"#f39c12",marginBottom:"10px"}}),e.jsx("h4",{style:{color:"#856404",margin:"0 0 10px 0"},children:"No Employees Found"}),e.jsx("p",{style:{color:"#856404",margin:0},children:"No employees are registered in the system. Please add employees first to view attendance records."})]})}),i.length>0&&e.jsxs("div",{className:"card",style:{marginBottom:"20px"},children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333",fontSize:"18px"},children:[e.jsx(dt,{size:20,style:{marginRight:"8px",verticalAlign:"middle"}}),"Filter Attendance Records"]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",alignItems:"end"},children:[e.jsxs("div",{children:[e.jsxs("label",{className:"form-label",children:[e.jsx(ue,{size:16,style:{marginRight:"6px",verticalAlign:"middle"}}),"Employee"]}),e.jsxs("select",{value:u,onChange:c=>d(c.target.value),className:"form-select",children:[e.jsx("option",{value:"all",children:"All Employees"}),i.map(c=>e.jsxs("option",{value:c.uid,children:[c.name," (",c.employeeId,")"]},c.uid))]})]}),e.jsxs("div",{children:[e.jsxs("label",{className:"form-label",children:[e.jsx(V,{size:16,style:{marginRight:"6px",verticalAlign:"middle"}}),"Date Range"]}),e.jsxs("select",{value:y,onChange:c=>b(c.target.value),className:"form-select",children:[e.jsx("option",{value:"today",children:"Today"}),e.jsx("option",{value:"week",children:"This Week"}),e.jsx("option",{value:"month",children:"This Month"}),e.jsx("option",{value:"custom",children:"Custom Range"})]})]}),y==="custom"&&e.jsxs(e.Fragment,{children:[e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Start Date"}),e.jsx("input",{type:"date",value:j,onChange:c=>p(c.target.value),className:"form-input"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"End Date"}),e.jsx("input",{type:"date",value:k,onChange:c=>w(c.target.value),className:"form-input"})]})]}),e.jsxs("button",{onClick:P,className:"btn btn-secondary",style:{display:"flex",alignItems:"center",gap:"8px",justifySelf:"start"},disabled:r,children:[e.jsx(dt,{size:18}),r?"Loading...":"Apply Filters"]})]})]}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333",fontSize:"18px"},children:[e.jsx(V,{size:20,style:{marginRight:"8px",verticalAlign:"middle"}}),"Attendance Records (",Object.keys(g.daily).length,")"]}),n.length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No attendance records found for the selected criteria"}),i.length===0&&e.jsx("p",{style:{fontSize:"14px",marginTop:"10px"},children:"No employees found. Please ensure employees are registered in the system."})]}):Object.keys(g.daily).length===0?e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"Processing attendance data..."})]}):e.jsx("div",{className:"table-container",style:{overflowX:"auto",width:"100%"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%",minWidth:"700px"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Employee"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Date"}),e.jsx("th",{style:{width:"80px",minWidth:"80px"},children:"Day"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Regular Check In/Out"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Late Check In/Out"}),e.jsx("th",{style:{width:"160px",minWidth:"160px"},children:"Working Hours"})]})}),e.jsx("tbody",{children:Object.values(g.daily).map(c=>{var oe;const I=`${c.userId}_${c.date}`,A=c.records.filter(C=>C.type==="checkin"),B=c.records.filter(C=>C.type==="checkout"),F=A.filter(C=>!C.isLate&&!C.lateReason),H=B.filter(C=>!C.isLate&&!C.lateReason),K=A.filter(C=>C.isLate||C.lateReason),Z=B.filter(C=>C.isLate||C.lateReason);return e.jsxs("tr",{style:{backgroundColor:"white"},children:[e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(ue,{size:14,style:{color:"#3498db",flexShrink:0}}),e.jsxs("div",{style:{minWidth:0},children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:c.employeeName}),e.jsxs("div",{style:{fontSize:"11px",color:"#666",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:["ID: ",(oe=c.employee)==null?void 0:oe.employeeId]})]})]})}),e.jsxs("td",{style:{padding:"12px 8px",fontSize:"12px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:[e.jsx("div",{style:{fontWeight:"bold",marginBottom:"2px"},children:W(He(c.date),"MMM dd")}),e.jsx("div",{style:{fontSize:"11px",color:"#666"},children:W(He(c.date),"yyyy")})]}),e.jsx("td",{style:{padding:"12px 8px",fontSize:"11px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:W(He(c.date),"EEE")}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:F.length===0&&H.length===0?e.jsx("span",{style:{color:"#999",fontSize:"11px",fontStyle:"italic"},children:"No regular records"}):e.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[F.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(te,{size:12,style:{color:"#27ae60"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#27ae60",fontWeight:"bold"},children:["In: ",F.length]})]}),H.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(re,{size:12,style:{color:"#2980b9"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#2980b9",fontWeight:"bold"},children:["Out: ",H.length]})]}),(F.length>0||H.length>0)&&e.jsxs("button",{onClick:()=>{O({reason:`Regular Check-ins: ${F.length}, Check-outs: ${H.length}`,type:"Regular Records",time:c.date,employee:c.employeeName,records:[...F,...H]}),N(!0)},className:"btn btn-secondary",style:{padding:"2px 6px",fontSize:"9px",display:"flex",alignItems:"center",gap:"2px",margin:"0 auto",minWidth:"50px"},title:"View regular check-in/out details",children:[e.jsx(V,{size:10}),"Details"]})]})}),e.jsx("td",{style:{textAlign:"center",padding:"12px 8px"},children:K.length===0&&Z.length===0?e.jsx("span",{style:{color:"#999",fontSize:"11px",fontStyle:"italic"},children:"No late records"}):e.jsxs("div",{style:{display:"flex",flexDirection:"column",gap:"4px"},children:[K.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(ae,{size:12,style:{color:"#f39c12"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#f39c12",fontWeight:"bold"},children:["Late In: ",K.length]})]}),Z.length>0&&e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"4px",justifyContent:"center"},children:[e.jsx(ae,{size:12,style:{color:"#e74c3c"}}),e.jsxs("span",{style:{fontSize:"11px",color:"#e74c3c",fontWeight:"bold"},children:["Late Out: ",Z.length]})]}),(K.length>0||Z.length>0)&&e.jsxs("button",{onClick:()=>{O({reason:`Late Check-ins: ${K.length}, Late Check-outs: ${Z.length}`,type:"Late Records",time:c.date,employee:c.employeeName,records:[...K,...Z]}),N(!0)},className:"btn btn-warning",style:{padding:"2px 6px",fontSize:"9px",display:"flex",alignItems:"center",gap:"2px",margin:"0 auto",minWidth:"50px"},title:"View late check-in/out details and reasons",children:[e.jsx(ae,{size:10}),"Details"]})]})}),e.jsx("td",{style:{padding:"12px 8px"},children:e.jsxs("div",{style:{fontSize:"11px",textAlign:"center"},children:[c.workingHours.regularHours.totalMinutes>0&&e.jsxs("div",{style:{color:"#27ae60",marginBottom:"2px",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[e.jsx(te,{size:10}),e.jsxs("span",{style:{fontSize:"10px"},children:["R: ",c.workingHours.regularHours.display]})]}),c.workingHours.lateHours.totalMinutes>0&&e.jsxs("div",{style:{color:"#f39c12",marginBottom:"2px",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[e.jsx(ae,{size:10}),e.jsxs("span",{style:{fontSize:"10px"},children:["L: ",c.workingHours.lateHours.display]})]}),e.jsxs("div",{style:{fontWeight:"bold",borderTop:"1px solid #eee",paddingTop:"4px",marginTop:"4px",display:"flex",alignItems:"center",justifyContent:"center",gap:"4px"},children:[e.jsx(V,{size:12,style:{color:"#333"}}),e.jsx("span",{style:{fontSize:"11px"},children:c.workingHours.totalHours.display})]}),c.workingHours.status==="INCOMPLETE"&&e.jsxs("div",{style:{color:"#e74c3c",fontSize:"9px",marginTop:"2px",display:"flex",alignItems:"center",justifyContent:"center",gap:"2px"},children:[e.jsx(re,{size:8}),"Incomplete"]})]})})]},I)})})]})})]}),m&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e4},children:e.jsxs("div",{style:{backgroundColor:"white",borderRadius:"12px",padding:"30px",maxWidth:"600px",width:"95%",maxHeight:"85vh",overflowY:"auto",boxShadow:"0 20px 60px rgba(0, 0, 0, 0.3)"},children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"25px"},children:[e.jsxs("h3",{style:{margin:0,color:"#333",fontSize:"20px",display:"flex",alignItems:"center",gap:"10px"},children:[e.jsx(V,{size:24,style:{color:"#3498db"}}),v.type," Details"]}),e.jsx("button",{onClick:()=>N(!1),style:{background:"none",border:"none",fontSize:"24px",cursor:"pointer",color:"#666",padding:"0",width:"30px",height:"30px",display:"flex",alignItems:"center",justifyContent:"center",borderRadius:"50%",transition:"background 0.2s"},onMouseEnter:c=>c.target.style.background="#f8f9fa",onMouseLeave:c=>c.target.style.background="none",children:"×"})]}),e.jsx("div",{style:{marginBottom:"25px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"20px",padding:"20px",backgroundColor:"#f8f9fa",borderRadius:"8px"},children:[e.jsxs("div",{children:[e.jsxs("strong",{style:{color:"#666",fontSize:"14px",display:"flex",alignItems:"center",gap:"6px"},children:[e.jsx(ue,{size:16}),"Employee:"]}),e.jsx("div",{style:{color:"#333",fontSize:"16px",fontWeight:"bold"},children:v.employee})]}),e.jsxs("div",{children:[e.jsxs("strong",{style:{color:"#666",fontSize:"14px",display:"flex",alignItems:"center",gap:"6px"},children:[e.jsx(V,{size:16}),"Date:"]}),e.jsx("div",{style:{color:"#333",fontSize:"16px",fontWeight:"bold"},children:v.time})]})]})}),v.records&&v.records.length>0?e.jsxs("div",{style:{marginBottom:"25px"},children:[e.jsx("strong",{style:{color:"#666",fontSize:"16px",display:"block",marginBottom:"15px"},children:"Detailed Records:"}),e.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"8px",overflow:"hidden"},children:v.records.map((c,I)=>e.jsxs("div",{style:{padding:"15px",borderBottom:I<v.records.length-1?"1px solid #e9ecef":"none",display:"flex",justifyContent:"space-between",alignItems:"center",flexWrap:"wrap",gap:"10px"},children:[e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"10px"},children:[c.type==="checkin"?e.jsx(te,{size:16,style:{color:c.isLate?"#f39c12":"#27ae60"}}):e.jsx(re,{size:16,style:{color:c.isLate?"#e74c3c":"#2980b9"}}),e.jsxs("div",{children:[e.jsxs("div",{style:{fontWeight:"bold",fontSize:"14px"},children:[c.type==="checkin"?"Check In":"Check Out",c.isLate&&e.jsx("span",{style:{color:"#f39c12",marginLeft:"8px"},children:"(Late)"})]}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["Time: ",c.type==="checkin"?c.checkInTime:c.checkOutTime]})]})]}),c.lateReason&&e.jsxs("div",{style:{backgroundColor:"#fff3cd",border:"1px solid #ffeaa7",borderRadius:"4px",padding:"8px 12px",fontSize:"12px",color:"#856404",maxWidth:"300px"},children:[e.jsx("strong",{children:"Reason:"})," ",c.lateReason]})]},I))})]}):e.jsxs("div",{style:{marginBottom:"25px"},children:[e.jsx("strong",{style:{color:"#666",fontSize:"14px",display:"block",marginBottom:"8px"},children:"Summary:"}),e.jsx("div",{style:{backgroundColor:"#f8f9fa",border:"1px solid #e9ecef",borderRadius:"6px",padding:"15px",color:"#333",fontSize:"15px",lineHeight:"1.5"},children:v.reason})]}),e.jsx("div",{style:{textAlign:"right"},children:e.jsxs("button",{onClick:()=>N(!1),className:"btn btn-primary",style:{padding:"12px 24px",display:"flex",alignItems:"center",gap:"8px",margin:"0 0 0 auto"},children:[e.jsx(te,{size:16}),"Close"]})})]})})]})},Xi=()=>{const{userProfile:t}=Q(),[s,n]=x.useState([]),[a,i]=x.useState(!1),[o,r]=x.useState(""),[l,u]=x.useState("all"),[d,y]=x.useState(null),[b,j]=x.useState(""),[p,k]=x.useState(!1),[w,g]=x.useState(!1),[f,E]=x.useState({});x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&D()},[t]);const D=async()=>{i(!0);try{console.log("Admin loading holiday requests...");const h=await z.getAll(L.HOLIDAY_REQUESTS);console.log("Fetched holiday requests:",h);const c=h.sort((I,A)=>new Date(A.requestDate)-new Date(I.requestDate));n(c),c.length===0?R.info("No holiday requests found"):R.success(`Loaded ${c.length} holiday requests`)}catch(h){console.error("Error loading holiday requests:",h),R.error("Failed to load holiday requests: "+h.message)}finally{i(!1)}},m=async(h,c)=>{try{await pt.updateRequestStatus(h,c,b);const I=s.find(A=>A.id===h);I&&I.userId&&(c==="approved"?await Mi(I.userId,I.startDate,I.endDate):c==="rejected"&&await Li(I.userId,I.startDate,I.endDate,b||"No reason provided")),R.success(`Holiday request ${c} successfully`),n(A=>A.map(B=>B.id===h?{...B,status:c,adminRemarks:b}:B)),k(!1),j(""),y(null)}catch(I){console.error("Error updating request status:",I),R.error("Failed to update request status")}},N=(h,c)=>{y({...h,action:c}),j(h.adminRemarks||""),k(!0)},v=h=>{E({title:"Holiday Request Reason",content:h.reason,type:"details",employeeName:h.employeeName,date:`${h.startDate} to ${h.endDate}`,time:`${h.days} days`}),g(!0)},O=h=>{E({title:"Admin Remarks",content:h.adminRemarks||"",type:"remarks",employeeName:h.employeeName,date:`${h.startDate} to ${h.endDate}`,time:`${h.days} days`}),g(!0)},S=s.filter(h=>{var A,B,F;const c=((A=h.employeeName)==null?void 0:A.toLowerCase().includes(o.toLowerCase()))||((B=h.employeeId)==null?void 0:B.toLowerCase().includes(o.toLowerCase()))||((F=h.reason)==null?void 0:F.toLowerCase().includes(o.toLowerCase())),I=l==="all"||h.status===l;return c&&I}),_=h=>{const c={pending:{color:"#f39c12",bg:"#fef9e7",text:"Pending"},approved:{color:"#27ae60",bg:"#eafaf1",text:"Approved"},rejected:{color:"#e74c3c",bg:"#fdedec",text:"Rejected"}},I=c[h]||c.pending;return e.jsx("span",{style:{padding:"4px 12px",borderRadius:"12px",fontSize:"12px",fontWeight:"bold",color:I.color,backgroundColor:I.bg,border:`1px solid ${I.color}20`},children:I.text})},T={total:s.length,pending:s.filter(c=>c.status==="pending").length,approved:s.filter(c=>c.status==="approved").length,rejected:s.filter(c=>c.status==="rejected").length};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card",children:[e.jsx("h2",{style:{color:"#e74c3c",marginBottom:"10px"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsx("h2",{style:{color:"#333",margin:0},children:"Holiday Request Management"}),e.jsx("button",{onClick:D,className:"btn btn-primary",disabled:a,children:a?"Loading...":"Refresh"})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"30px"},children:[e.jsxs("div",{className:"stat-card",children:[e.jsx(se,{size:32,style:{color:"#3498db",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#3498db"},children:T.total}),e.jsx("div",{className:"stat-label",children:"Total Requests"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(V,{size:32,style:{color:"#f39c12",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#f39c12"},children:T.pending}),e.jsx("div",{className:"stat-label",children:"Pending"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(te,{size:32,style:{color:"#27ae60",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#27ae60"},children:T.approved}),e.jsx("div",{className:"stat-label",children:"Approved"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(re,{size:32,style:{color:"#e74c3c",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#e74c3c"},children:T.rejected}),e.jsx("div",{className:"stat-label",children:"Rejected"})]})]}),e.jsx("div",{className:"card",style:{marginBottom:"20px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr auto",gap:"20px",alignItems:"center","@media (max-width: 768px)":{gridTemplateColumns:"1fr",gap:"15px"}},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx(Qe,{size:16,style:{position:"absolute",left:"10px",top:"50%",transform:"translateY(-50%)",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search by employee name, ID, or reason...",value:o,onChange:h=>r(h.target.value),className:"form-input",style:{paddingLeft:"35px"}})]}),e.jsxs("select",{value:l,onChange:h=>u(h.target.value),className:"form-select",style:{minWidth:"150px"},children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"approved",children:"Approved"}),e.jsx("option",{value:"rejected",children:"Rejected"})]})]})}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Holiday Requests (",S.length,")"]}),a?e.jsx("div",{style:{textAlign:"center",padding:"40px"},children:e.jsx("div",{style:{color:"#666"},children:"Loading holiday requests..."})}):S.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",style:{tableLayout:"fixed",width:"100%"},children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Employee"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Start Date"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"End Date"}),e.jsx("th",{style:{width:"70px",minWidth:"70px"},children:"Days"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Reason"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{width:"120px",minWidth:"120px"},children:"Admin Remarks"}),e.jsx("th",{style:{width:"100px",minWidth:"100px"},children:"Request Date"}),e.jsx("th",{style:{width:"180px",minWidth:"180px"},children:"Actions"})]})}),e.jsx("tbody",{children:S.map(h=>e.jsxs("tr",{children:[e.jsx("td",{style:{padding:"12px 8px",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"},children:e.jsxs("div",{style:{display:"flex",alignItems:"center",gap:"8px"},children:[e.jsx(gt,{size:14,style:{color:"#3498db"}}),e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold",fontSize:"13px",maxWidth:"100px",overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap"},children:h.employeeName}),e.jsxs("div",{style:{fontSize:"11px",color:"#666"},children:["ID: ",h.employeeId]})]})]})}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"},children:h.startDate}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"},children:h.endDate}),e.jsx("td",{style:{textAlign:"center"},children:e.jsx("span",{style:{fontWeight:"bold",color:"#3498db",padding:"4px 8px",background:"#e3f2fd",borderRadius:"12px",fontSize:"12px",display:"inline-block"},children:h.days})}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>v(h),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View holiday reason",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:_(h.status)}),e.jsx("td",{style:{textAlign:"center",padding:"8px"},children:e.jsxs("button",{onClick:()=>O(h),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:h.adminRemarks?1:.6},title:h.adminRemarks?"View admin remarks":"No remarks available",children:[e.jsx(xe,{size:14}),h.adminRemarks?"View Remarks":"No Remarks"]})}),e.jsx("td",{style:{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis",fontSize:"13px"},children:h.requestDate}),e.jsx("td",{style:{padding:"8px"},children:e.jsxs("div",{style:{display:"flex",gap:"4px",flexWrap:"wrap",justifyContent:"center"},children:[h.status==="pending"&&e.jsxs(e.Fragment,{children:[e.jsxs("button",{onClick:()=>N(h,"approve"),className:"btn btn-success",style:{fontSize:"11px",padding:"4px 8px",display:"flex",alignItems:"center",gap:"4px",minWidth:"70px"},title:"Approve request",children:[e.jsx(te,{size:12}),"Approve"]}),e.jsxs("button",{onClick:()=>N(h,"reject"),className:"btn btn-danger",style:{fontSize:"11px",padding:"4px 8px",display:"flex",alignItems:"center",gap:"4px",minWidth:"70px"},title:"Reject request",children:[e.jsx(re,{size:12}),"Reject"]})]}),e.jsxs("button",{onClick:()=>N(h,"remark"),className:"btn btn-secondary",style:{fontSize:"11px",padding:"4px 8px",display:"flex",alignItems:"center",gap:"4px",minWidth:"70px"},title:"Add/Edit remarks",children:[e.jsx(xe,{size:12}),"Remarks"]})]})})]},h.id))})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(se,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No holiday requests found"}),o&&e.jsx("p",{children:"Try adjusting your search criteria"})]})]}),p&&d&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:e.jsxs("div",{style:{backgroundColor:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"500px",maxHeight:"80vh",overflowY:"auto"},children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:d.action==="approve"?"Approve Request":d.action==="reject"?"Reject Request":"Add/Edit Remarks"}),e.jsxs("div",{style:{marginBottom:"20px",padding:"15px",backgroundColor:"#f8f9fa",borderRadius:"4px"},children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Employee:"})," ",d.employeeName]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Dates:"})," ",d.startDate," to ",d.endDate]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Days:"})," ",d.days]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Reason:"})," ",d.reason]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Admin Remarks"}),e.jsx("textarea",{value:b,onChange:h=>j(h.target.value),className:"form-textarea",placeholder:"Add your remarks here...",rows:4})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>{k(!1),j(""),y(null)},className:"btn btn-secondary",children:"Cancel"}),d.action==="approve"&&e.jsxs("button",{onClick:()=>m(d.id,"approved"),className:"btn btn-success",children:[e.jsx(te,{size:16}),"Approve Request"]}),d.action==="reject"&&e.jsxs("button",{onClick:()=>m(d.id,"rejected"),className:"btn btn-danger",children:[e.jsx(re,{size:16}),"Reject Request"]}),d.action==="remark"&&e.jsxs("button",{onClick:()=>m(d.id,d.status),className:"btn btn-primary",children:[e.jsx(xe,{size:16}),"Save Remarks"]})]})]})}),e.jsx(Fe,{isOpen:w,onClose:()=>g(!1),title:f.title,content:f.content,type:f.type,employeeName:f.employeeName,date:f.date,time:f.time})]})},Ji=()=>{const{userProfile:t,loading:s}=Q(),[n,a]=x.useState([]),[i,o]=x.useState([]),[r,l]=x.useState(!1),[u,d]=x.useState(""),[y,b]=x.useState("all"),[j,p]=x.useState("all"),[k,w]=x.useState(!1),[g,f]=x.useState(null),[E,D]=x.useState(!1),[m,N]=x.useState({}),[v,O]=x.useState({title:"",description:"",details:"",assignedTo:"",assignedToName:"",priority:"medium",dueDate:"",status:"pending"});x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&(S(),_())},[t]);const S=async()=>{l(!0);try{console.log("Admin loading tasks...");const C=await Te.getAllTasks();console.log("Fetched tasks:",C);const X=C.sort(($,M)=>{var G,ne;const U=(G=$.createdAt)!=null&&G.seconds?new Date($.createdAt.seconds*1e3):new Date($.createdAt||0);return((ne=M.createdAt)!=null&&ne.seconds?new Date(M.createdAt.seconds*1e3):new Date(M.createdAt||0))-U});a(X),X.length===0?console.log("No tasks found"):console.log(`Loaded ${X.length} tasks`)}catch(C){console.error("Error loading tasks:",C),R.error("Failed to load tasks: "+C.message)}finally{l(!1)}},_=async()=>{try{const C=await je.getAllEmployees();console.log("Loaded employees:",C),o(C||[])}catch(C){console.error("Error loading employees:",C),R.error("Failed to load employees"),o([])}},P=C=>{const{name:X,value:$}=C.target;if(O(M=>({...M,[X]:$})),X==="assignedTo"){const M=i.find(U=>U.uid===$);O(U=>({...U,assignedToName:M?M.name:""}))}},T=async C=>{var X,$,M;C.preventDefault(),l(!0);try{if(s){R.error("Please wait while user profile loads..."),l(!1);return}if(!((X=v.title)!=null&&X.trim())){R.error("Task title is required"),l(!1);return}if(!(($=v.description)!=null&&$.trim())){R.error("Task description is required"),l(!1);return}if(!v.assignedTo){R.error("Please select an employee to assign the task"),l(!1);return}if(!v.dueDate){R.error("Due date is required"),l(!1);return}if(!t){R.error("User profile not loaded. Please refresh the page."),l(!1);return}const U=t.uid||t.id;if(!U){console.error("No user ID found in profile:",t),R.error("User ID not found. Please refresh the page."),l(!1);return}const q=t.name||t.displayName||t.email;if(!q){console.error("No user name found in profile:",t),R.error("User name not found. Please refresh the page."),l(!1);return}const G={title:v.title.trim(),description:v.description.trim(),details:((M=v.details)==null?void 0:M.trim())||"",assignedTo:v.assignedTo,assignedToName:v.assignedToName||"",assignedBy:U,assignedByName:q,priority:v.priority||"medium",dueDate:v.dueDate,date:v.dueDate,day:W(new Date(v.dueDate),"EEEE"),status:v.status||"pending",employeeRemarks:"",reason:""};console.log("Submitting task data:",G),g?(await Te.updateTask(g.id,G),R.success("Task updated successfully!")):(await Te.createTask(G),v.assignedTo&&v.assignedToName&&await Ai(v.assignedTo,v.title,t.name),R.success("Task assigned successfully!")),I(),S()}catch(U){console.error("Error saving task:",U),R.error("Failed to save task: "+(U.message||"Unknown error"))}finally{l(!1)}},h=C=>{f(C),O({title:C.title||"",description:C.description||"",details:C.details||"",assignedTo:C.assignedTo||"",assignedToName:C.assignedToName||"",priority:C.priority||"medium",dueDate:C.dueDate||C.date||"",status:C.status||"pending"}),w(!0)},c=async C=>{if(window.confirm("Are you sure you want to delete this task?"))try{await Te.deleteTask(C),R.success("Task deleted successfully!"),S()}catch(X){console.error("Error deleting task:",X),R.error("Failed to delete task")}},I=()=>{O({title:"",description:"",details:"",assignedTo:"",assignedToName:"",priority:"medium",dueDate:"",status:"pending"}),f(null),w(!1)},A=(n||[]).filter(C=>{if(!C)return!1;const X=(C.title||"").toLowerCase().includes(u.toLowerCase())||(C.description||"").toLowerCase().includes(u.toLowerCase())||(C.assignedToName||"").toLowerCase().includes(u.toLowerCase()),$=y==="all"||C.status===y,M=j==="all"||C.assignedTo===j;return X&&$&&M}),B=C=>{const X={pending:{color:"#f39c12",bg:"#fef9e7",text:"Pending",icon:V},completed:{color:"#27ae60",bg:"#eafaf1",text:"Completed",icon:te},"not-achieved":{color:"#e74c3c",bg:"#fdedec",text:"Not Achieved",icon:re}},$=X[C]||X.pending,M=$.icon;return e.jsxs("span",{style:{padding:"4px 12px",borderRadius:"12px",fontSize:"12px",fontWeight:"bold",color:$.color,backgroundColor:$.bg,border:`1px solid ${$.color}20`,display:"inline-flex",alignItems:"center",gap:"4px"},children:[e.jsx(M,{size:12}),$.text]})},F=C=>{switch(C){case"high":return"#e74c3c";case"medium":return"#f39c12";case"low":return"#27ae60";default:return"#95a5a6"}},H=()=>{const C=n||[];return{total:C.length,pending:C.filter($=>$&&$.status==="pending").length,completed:C.filter($=>$&&$.status==="completed").length,notAchieved:C.filter($=>$&&$.status==="not-achieved").length}},K=C=>{N({title:"Task Description & Details",content:`${C.description||"No description"}

${C.details||""}`.trim(),type:"details",employeeName:`Assigned to: ${C.assignedToName||"Unknown Employee"}`,date:C.dueDate||C.date,time:C.day||""}),D(!0)},Z=C=>{N({title:"Employee Remarks",content:C.employeeRemarks||C.reason||"",type:"remarks",employeeName:`From: ${C.assignedToName||"Unknown Employee"}`,date:C.dueDate||C.date,time:C.day||""}),D(!0)},oe=H();return s?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx("h3",{children:"Loading..."}),e.jsx("p",{children:"Please wait while we load your profile."})]})}):!t||t.role!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card",children:[e.jsx("h2",{style:{color:"#e74c3c",marginBottom:"10px"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to view this page."})]})}):e.jsxs("div",{className:"content",children:[e.jsxs("div",{style:{display:"flex",justifyContent:"space-between",alignItems:"center",marginBottom:"30px"},children:[e.jsx("h2",{style:{color:"#333",margin:0},children:"Task Management"}),e.jsxs("div",{style:{display:"flex",gap:"10px"},children:[e.jsx("button",{onClick:S,className:"btn btn-secondary",disabled:r,children:r?"Loading...":"Refresh"}),e.jsxs("button",{onClick:()=>w(!0),className:"btn btn-primary",disabled:s||!t,children:[e.jsx(ge,{size:16}),"Assign New Task"]})]})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"repeat(auto-fit, minmax(200px, 1fr))",gap:"20px",marginBottom:"30px"},children:[e.jsxs("div",{className:"stat-card",children:[e.jsx(ke,{size:32,style:{color:"#3498db",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#3498db"},children:oe.total}),e.jsx("div",{className:"stat-label",children:"Total Tasks"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(V,{size:32,style:{color:"#f39c12",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#f39c12"},children:oe.pending}),e.jsx("div",{className:"stat-label",children:"Pending"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(te,{size:32,style:{color:"#27ae60",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#27ae60"},children:oe.completed}),e.jsx("div",{className:"stat-label",children:"Completed"})]}),e.jsxs("div",{className:"stat-card",children:[e.jsx(re,{size:32,style:{color:"#e74c3c",marginBottom:"10px"}}),e.jsx("div",{className:"stat-number",style:{color:"#e74c3c"},children:oe.notAchieved}),e.jsx("div",{className:"stat-label",children:"Not Achieved"})]})]}),e.jsx("div",{className:"card",style:{marginBottom:"20px"},children:e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"2fr 1fr 1fr",gap:"20px",alignItems:"end"},children:[e.jsxs("div",{style:{position:"relative"},children:[e.jsx("label",{className:"form-label",children:"Search Tasks"}),e.jsx(Qe,{size:20,style:{position:"absolute",left:"12px",bottom:"12px",color:"#666"}}),e.jsx("input",{type:"text",placeholder:"Search by title, description, or employee...",value:u,onChange:C=>d(C.target.value),className:"form-input",style:{paddingLeft:"40px"}})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Status Filter"}),e.jsxs("select",{value:y,onChange:C=>b(C.target.value),className:"form-input",children:[e.jsx("option",{value:"all",children:"All Status"}),e.jsx("option",{value:"pending",children:"Pending"}),e.jsx("option",{value:"completed",children:"Completed"}),e.jsx("option",{value:"not-achieved",children:"Not Achieved"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"form-label",children:"Employee Filter"}),e.jsxs("select",{value:j,onChange:C=>p(C.target.value),className:"form-input",children:[e.jsx("option",{value:"all",children:"All Employees"}),i.map(C=>e.jsxs("option",{value:C.uid,children:[C.name," (",C.employeeId,")"]},C.uid))]})]})]})}),e.jsxs("div",{className:"card",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:["Tasks (",A.length,")"]}),r?e.jsx("div",{style:{textAlign:"center",padding:"40px"},children:e.jsx("div",{style:{color:"#666"},children:"Loading tasks..."})}):A.length>0?e.jsx("div",{style:{overflowX:"auto"},children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{style:{minWidth:"150px"},children:"Title"}),e.jsx("th",{style:{minWidth:"150px"},children:"Description"}),e.jsx("th",{style:{minWidth:"120px"},children:"Assigned To"}),e.jsx("th",{style:{minWidth:"80px"},children:"Priority"}),e.jsx("th",{style:{minWidth:"100px"},children:"Due Date"}),e.jsx("th",{style:{minWidth:"100px"},children:"Status"}),e.jsx("th",{style:{minWidth:"150px"},children:"Employee Remarks"}),e.jsx("th",{style:{minWidth:"150px"},children:"Actions"})]})}),e.jsx("tbody",{children:A.map(C=>{var X,$;return e.jsxs("tr",{children:[e.jsx("td",{style:{fontWeight:"500"},children:C.title||C.description||"Untitled Task"}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>K(C),className:"btn btn-secondary",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto"},title:"View task description and details",children:[e.jsx(fe,{size:14}),"View Details"]})}),e.jsxs("td",{children:[e.jsx("div",{style:{fontSize:"14px",fontWeight:"500"},children:C.assignedToName||"Unknown Employee"}),e.jsx("div",{style:{fontSize:"12px",color:"#666"},children:((X=i.find(M=>M.uid===C.assignedTo))==null?void 0:X.employeeId)||"N/A"})]}),e.jsx("td",{children:e.jsx("span",{style:{padding:"2px 8px",borderRadius:"12px",fontSize:"11px",fontWeight:"bold",color:"white",background:F(C.priority)},children:(($=C.priority)==null?void 0:$.toUpperCase())||"MEDIUM"})}),e.jsx("td",{children:(()=>{try{const M=C.dueDate||C.date;return M?new Date(M).toLocaleDateString():"No due date"}catch{return"Invalid date"}})()}),e.jsx("td",{children:B(C.status)}),e.jsx("td",{style:{textAlign:"center"},children:e.jsxs("button",{onClick:()=>Z(C),className:"btn btn-info",style:{padding:"6px 12px",fontSize:"12px",display:"flex",alignItems:"center",gap:"6px",margin:"0 auto",opacity:C.employeeRemarks||C.reason?1:.6},title:C.employeeRemarks||C.reason?"View employee remarks":"No remarks available",children:[e.jsx(xe,{size:14}),C.employeeRemarks||C.reason?"View Remarks":"No Remarks"]})}),e.jsx("td",{children:e.jsxs("div",{style:{display:"flex",gap:"8px",flexWrap:"wrap"},children:[e.jsxs("button",{onClick:()=>h(C),className:"btn btn-secondary",style:{fontSize:"12px",padding:"4px 8px"},children:[e.jsx(es,{size:14}),"Edit"]}),e.jsxs("button",{onClick:()=>c(C.id),className:"btn btn-danger",style:{fontSize:"12px",padding:"4px 8px"},children:[e.jsx(jn,{size:14}),"Delete"]})]})})]},C.id)})})]})}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(ke,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No tasks found"}),u&&e.jsx("p",{children:"Try adjusting your search criteria"})]})]}),k&&e.jsx("div",{style:{position:"fixed",top:0,left:0,right:0,bottom:0,backgroundColor:"rgba(0, 0, 0, 0.5)",display:"flex",justifyContent:"center",alignItems:"center",zIndex:1e3},children:e.jsxs("div",{style:{backgroundColor:"white",padding:"30px",borderRadius:"8px",width:"90%",maxWidth:"600px",maxHeight:"80vh",overflowY:"auto"},children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:g?"Edit Task":"Assign New Task"}),e.jsxs("form",{onSubmit:T,children:[e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Task Title *"}),e.jsx("input",{type:"text",name:"title",value:v.title,onChange:P,className:"form-input",placeholder:"Enter task title...",required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Assign To *"}),e.jsxs("select",{name:"assignedTo",value:v.assignedTo,onChange:P,className:"form-input",required:!0,children:[e.jsx("option",{value:"",children:"Select Employee"}),i.map(C=>e.jsxs("option",{value:C.uid,children:[C.name," (",C.employeeId,")"]},C.uid))]})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Task Description *"}),e.jsx("textarea",{name:"description",value:v.description,onChange:P,className:"form-textarea",placeholder:"Enter task description...",rows:3,required:!0})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Additional Details"}),e.jsx("textarea",{name:"details",value:v.details,onChange:P,className:"form-textarea",placeholder:"Enter additional task details...",rows:2})]}),e.jsxs("div",{style:{display:"grid",gridTemplateColumns:"1fr 1fr",gap:"20px"},children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Priority"}),e.jsxs("select",{name:"priority",value:v.priority,onChange:P,className:"form-input",children:[e.jsx("option",{value:"low",children:"Low"}),e.jsx("option",{value:"medium",children:"Medium"}),e.jsx("option",{value:"high",children:"High"})]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Due Date *"}),e.jsx("input",{type:"date",name:"dueDate",value:v.dueDate,onChange:P,className:"form-input",min:W(new Date,"yyyy-MM-dd"),required:!0})]})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end",marginTop:"20px"},children:[e.jsx("button",{type:"button",onClick:I,className:"btn btn-secondary",children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:r,children:r?"Saving...":g?"Update Task":"Assign Task"})]})]})]})}),e.jsx(Fe,{isOpen:E,onClose:()=>D(!1),title:m.title,content:m.content,type:m.type,employeeName:m.employeeName,date:m.date,time:m.time})]})},Qi=()=>{const{userProfile:t}=Q(),[s,n]=x.useState(!1),[a,i]=x.useState([]),[o,r]=x.useState(!1),[l,u]=x.useState([]),[d,y]=x.useState({date:"",hours:"",reason:""});x.useEffect(()=>{t&&(b(),j())},[t]);const b=async()=>{try{const f=await Ne.getUserCompensation(t.uid);i(f)}catch(f){console.error("Error loading compensations:",f),R.error("Failed to load compensation records")}},j=async()=>{try{const f=await Ne.checkMissedCheckIns(t.uid);u(f)}catch(f){console.error("Error checking missed check-ins:",f)}},p=f=>{const{name:E,value:D}=f.target;y(m=>({...m,[E]:D}))},k=async f=>{f.preventDefault(),n(!0);try{const E=parseFloat(d.hours);if(isNaN(E)||E<=0||E>24){R.error("Please enter valid hours (1-24)");return}await Ne.createCompensation({employeeId:t.uid,employeeName:t.name,date:d.date,hours:E,reason:d.reason,status:"pending"}),R.success("Compensation request submitted successfully"),r(!1),y({date:"",hours:"",reason:""}),b()}catch(E){console.error("Error submitting compensation:",E),R.error("Failed to submit compensation request")}finally{n(!1)}},w=async f=>{n(!0);try{await Ne.createCompensation({employeeId:t.uid,employeeName:t.name,date:f.date,hours:f.hours,reason:f.reason,status:"pending"}),R.success("Compensation request submitted successfully"),u(E=>E.filter(D=>D.date!==f.date)),b()}catch(E){console.error("Error submitting auto compensation:",E),R.error("Failed to submit compensation request")}finally{n(!1)}},g=f=>{const E={pending:{background:"#fff3cd",color:"#856404"},approved:{background:"#d4edda",color:"#155724"},rejected:{background:"#f8d7da",color:"#721c24"}};return e.jsx("span",{style:{padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500",...E[f]},children:f.charAt(0).toUpperCase()+f.slice(1)})};return e.jsxs("div",{className:"content",children:[e.jsxs("div",{className:"flex justify-between align-center mb-20",children:[e.jsx("h2",{style:{color:"#333"},children:"Compensation Hours"}),e.jsxs("button",{onClick:()=>r(!0),className:"btn btn-primary",disabled:s,children:[e.jsx(ge,{size:16,style:{marginRight:"8px"}}),"Request Compensation"]})]}),l.length>0&&e.jsxs("div",{className:"card mb-20",children:[e.jsxs("h3",{style:{marginBottom:"20px",color:"#333"},children:[e.jsx(Ae,{size:20,style:{marginRight:"8px",color:"#e74c3c"}}),"Missed Check-ins"]}),e.jsx("div",{className:"table-responsive",children:e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Time Slot"}),e.jsx("th",{children:"Hours"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Action"})]})}),e.jsx("tbody",{children:l.map((f,E)=>e.jsxs("tr",{children:[e.jsx("td",{children:f.date?W(f.date.toDate?f.date.toDate():new Date(f.date),"MMM dd, yyyy"):"-"}),e.jsx("td",{children:f.startTime&&f.endTime?`${W(f.startTime.toDate?f.startTime.toDate():new Date(f.startTime),"HH:mm")} - ${W(f.endTime.toDate?f.endTime.toDate():new Date(f.endTime),"HH:mm")}`:"-"}),e.jsx("td",{children:f.hours.toFixed(1)}),e.jsx("td",{children:f.reason}),e.jsx("td",{children:e.jsx("button",{onClick:()=>w(f),className:"btn btn-primary",style:{padding:"5px 10px",fontSize:"12px"},disabled:s,children:"Request Compensation"})})]},E))})]})})]}),o&&e.jsxs("div",{className:"card mb-20",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"Request Compensation Hours"}),e.jsxs("form",{onSubmit:k,children:[e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Date"}),e.jsx("input",{type:"date",name:"date",value:d.date,onChange:p,className:"form-input",required:!0,max:W(new Date,"yyyy-MM-dd")})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Hours"}),e.jsx("input",{type:"number",name:"hours",value:d.hours,onChange:p,className:"form-input",required:!0,min:"0.5",max:"24",step:"0.5",placeholder:"Enter hours (0.5-24)"})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Reason"}),e.jsx("textarea",{name:"reason",value:d.reason,onChange:p,className:"form-textarea",required:!0,placeholder:"Enter reason for compensation",rows:"4"})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{type:"button",onClick:()=>{r(!1),y({date:"",hours:"",reason:""})},className:"btn btn-secondary",disabled:s,children:"Cancel"}),e.jsx("button",{type:"submit",className:"btn btn-primary",disabled:s,children:s?"Submitting...":"Submit Request"})]})]})]}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"My Compensation Requests"}),a.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Hours"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Created At"}),e.jsx("th",{children:"Admin Remarks"})]})}),e.jsx("tbody",{children:a.map(f=>e.jsxs("tr",{children:[e.jsx("td",{children:f.date}),e.jsx("td",{children:f.hours}),e.jsx("td",{children:f.reason}),e.jsx("td",{children:g(f.status)}),e.jsx("td",{children:f.createdAt?W(f.createdAt.toDate?f.createdAt.toDate():new Date(f.createdAt),"MMM dd, yyyy HH:mm"):"-"}),e.jsx("td",{children:f.adminRemarks||"-"})]},f.id))})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No compensation requests found"})]})]})]})},Zi=()=>{const{userProfile:t}=Q(),[s,n]=x.useState(!1),[a,i]=x.useState([]),[o,r]=x.useState(!1),[l,u]=x.useState(null),[d,y]=x.useState("");x.useEffect(()=>{(t==null?void 0:t.role)==="admin"&&b()},[t]);const b=async()=>{try{const w=await Ne.getAllCompensation();i(w)}catch(w){console.error("Error loading compensations:",w),R.error("Failed to load compensation records")}},j=async(w,g)=>{n(!0);try{await Ne.updateCompensationStatus(w,g,d),R.success(`Compensation request ${g} successfully`),r(!1),y(""),u(null),b()}catch(f){console.error("Error updating compensation status:",f),R.error("Failed to update compensation status")}finally{n(!1)}},p=w=>{u(w),y(""),r(!0)},k=w=>{const g={pending:{background:"#fff3cd",color:"#856404"},approved:{background:"#d4edda",color:"#155724"},rejected:{background:"#f8d7da",color:"#721c24"}};return e.jsx("span",{style:{padding:"4px 8px",borderRadius:"12px",fontSize:"12px",fontWeight:"500",...g[w]},children:w.charAt(0).toUpperCase()+w.slice(1)})};return(t==null?void 0:t.role)!=="admin"?e.jsx("div",{className:"content",children:e.jsxs("div",{className:"card text-center",children:[e.jsx(V,{size:64,style:{color:"#e74c3c",marginBottom:"20px"}}),e.jsx("h3",{style:{color:"#e74c3c"},children:"Access Denied"}),e.jsx("p",{children:"You don't have permission to manage compensation requests."})]})}):e.jsxs("div",{className:"content",children:[e.jsx("div",{className:"flex justify-between align-center mb-20",children:e.jsx("h2",{style:{color:"#333"},children:"Compensation Management"})}),e.jsxs("div",{className:"card",children:[e.jsx("h3",{style:{marginBottom:"20px",color:"#333"},children:"All Compensation Requests"}),a.length>0?e.jsxs("table",{className:"table",children:[e.jsx("thead",{children:e.jsxs("tr",{children:[e.jsx("th",{children:"Employee"}),e.jsx("th",{children:"Date"}),e.jsx("th",{children:"Hours"}),e.jsx("th",{children:"Reason"}),e.jsx("th",{children:"Status"}),e.jsx("th",{children:"Created At"}),e.jsx("th",{children:"Actions"})]})}),e.jsx("tbody",{children:a.map(w=>e.jsxs("tr",{children:[e.jsx("td",{children:e.jsxs("div",{children:[e.jsx("div",{style:{fontWeight:"bold"},children:w.employeeName}),e.jsxs("div",{style:{fontSize:"12px",color:"#666"},children:["ID: ",w.employeeId]})]})}),e.jsx("td",{children:w.date}),e.jsx("td",{children:w.hours}),e.jsx("td",{children:w.reason}),e.jsx("td",{children:k(w.status)}),e.jsx("td",{children:w.createdAt?W(w.createdAt.toDate?w.createdAt.toDate():new Date(w.createdAt),"MMM dd, yyyy HH:mm"):"-"}),e.jsx("td",{children:w.status==="pending"&&e.jsxs("div",{className:"flex gap-10",children:[e.jsx("button",{onClick:()=>p(w),className:"btn btn-success",style:{padding:"5px 10px",fontSize:"12px"},title:"Approve Request",children:e.jsx(te,{size:14})}),e.jsx("button",{onClick:()=>p(w),className:"btn btn-danger",style:{padding:"5px 10px",fontSize:"12px"},title:"Reject Request",children:e.jsx(re,{size:14})})]})})]},w.id))})]}):e.jsxs("div",{style:{textAlign:"center",padding:"40px",color:"#666"},children:[e.jsx(V,{size:48,style:{opacity:.3,marginBottom:"10px"}}),e.jsx("p",{children:"No compensation requests found"})]})]}),o&&l&&e.jsx("div",{className:"modal-overlay",onClick:()=>{r(!1),y(""),u(null)},children:e.jsxs("div",{className:"modal-content",onClick:w=>w.stopPropagation(),children:[e.jsxs("div",{className:"modal-header",children:[e.jsx("h3",{children:"Update Compensation Request"}),e.jsx("button",{className:"modal-close",onClick:()=>{r(!1),y(""),u(null)},children:"×"})]}),e.jsxs("div",{className:"modal-body",children:[e.jsxs("div",{style:{marginBottom:"20px"},children:[e.jsxs("p",{children:[e.jsx("strong",{children:"Employee:"})," ",l.employeeName]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Date:"})," ",l.date]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Hours:"})," ",l.hours]}),e.jsxs("p",{children:[e.jsx("strong",{children:"Reason:"})," ",l.reason]})]}),e.jsxs("div",{className:"form-group",children:[e.jsx("label",{className:"form-label",children:"Admin Remarks"}),e.jsx("textarea",{value:d,onChange:w=>y(w.target.value),className:"form-textarea",placeholder:"Enter your remarks...",rows:"4"})]}),e.jsxs("div",{style:{display:"flex",gap:"10px",justifyContent:"flex-end"},children:[e.jsx("button",{onClick:()=>{r(!1),y(""),u(null)},className:"btn btn-secondary",disabled:s,children:"Cancel"}),e.jsx("button",{onClick:()=>j(l.id,"approved"),className:"btn btn-success",disabled:s,children:s?"Processing...":"Approve"}),e.jsx("button",{onClick:()=>j(l.id,"rejected"),className:"btn btn-danger",disabled:s,children:s?"Processing...":"Reject"})]})]})]})})]})},eo=()=>{const{currentUser:t,userProfile:s}=Q(),[n,a]=x.useState(""),[i,o]=x.useState(!1);x.useEffect(()=>{s&&be.checkAndPerformWeeklyReset().catch(d=>{console.error("Error in weekly timetable reset:",d)})},[s]);const r=()=>{o(!i)},l=d=>{a(d),o(!1)};Yt.useEffect(()=>{s&&(s.role==="admin"?l("admin-dashboard"):l("dashboard"))},[s]);const u=()=>{switch(n){case"dashboard":return e.jsx(Wt,{});case"checkin":return e.jsx(Pi,{});case"timetable":return e.jsx(Bi,{});case"progress":return e.jsx(Hi,{});case"holidays":return e.jsx(Ui,{});case"tasks":return e.jsx(Wi,{});case"compensation":return e.jsx(Qi,{});case"admin-dashboard":return e.jsx($t,{onNavigate:a});case"employees":return e.jsx($i,{});case"admin-timetable":return e.jsx(qi,{});case"admin-progress-reports":return e.jsx(Ki,{});case"attendance-records":return e.jsx(Gi,{});case"holiday-management":return e.jsx(Xi,{});case"task-management":return e.jsx(Ji,{});case"compensation-records":return e.jsx(Zi,{});default:return(s==null?void 0:s.role)==="admin"?e.jsx($t,{}):e.jsx(Wt,{})}};return t?s?e.jsxs("div",{className:"app-layout",children:[e.jsx(Ti,{activeTab:n,setActiveTab:l,isMobileOpen:i,onMobileClose:()=>o(!1)}),e.jsxs("div",{className:"main-content",children:[e.jsx(Ii,{onMobileMenuToggle:r}),u()]}),i&&e.jsx("div",{className:"mobile-sidebar-overlay",onClick:()=>o(!1)})]}):e.jsxs("div",{style:{display:"flex",justifyContent:"center",alignItems:"center",height:"100vh",flexDirection:"column",gap:"20px"},children:[e.jsx("div",{style:{width:"40px",height:"40px",border:"4px solid #f3f3f3",borderTop:"4px solid #3498db",borderRadius:"50%",animation:"spin 1s linear infinite"}}),e.jsx("p",{style:{color:"#666"},children:"Loading your profile..."})]}):e.jsx(Si,{})},to=()=>e.jsxs(vi,{children:[e.jsx(eo,{}),e.jsx(bn,{position:"top-right",toastOptions:{duration:4e3,style:{background:"#363636",color:"#fff"},success:{duration:3e3,theme:{primary:"#4aed88"}}}})]});ut.createRoot(document.getElementById("root")).render(e.jsx(Yt.StrictMode,{children:e.jsx(to,{})}));
