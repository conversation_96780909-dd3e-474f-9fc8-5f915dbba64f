/* Chatbot Styles */

/* Floating <PERSON><PERSON> */
.chatbot-float-button {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background-color: #3498db;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  transition: all 0.3s ease;
}

.chatbot-float-button:hover {
  transform: scale(1.05);
  background-color: #2980b9;
}

/* Pulse Animation */
.chatbot-pulse {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 50%;
  background-color: #3498db;
  opacity: 0.6;
  z-index: -1;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.6;
  }
  70% {
    transform: scale(1.1);
    opacity: 0;
  }
  100% {
    transform: scale(1.1);
    opacity: 0;
  }
}

/* Chat Container */
.chatbot-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 350px;
  height: 500px;
  border-radius: 12px;
  background-color: #fff;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.2);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  z-index: 1000;
  transition: all 0.3s ease;
}

.chatbot-container.minimized {
  height: 60px;
}

/* Header */
.chatbot-header {
  background-color: #3498db;
  color: white;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top-left-radius: 12px;
  border-top-right-radius: 12px;
}

.chatbot-header-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.chatbot-header-info h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
}

.chatbot-status {
  font-size: 12px;
  opacity: 0.8;
}

.chatbot-header-actions {
  display: flex;
  gap: 8px;
}

.chatbot-action-btn {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.chatbot-action-btn:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

/* Messages Area */
.chatbot-messages {
  flex: 1;
  padding: 15px;
  overflow-y: auto;
  background-color: #f8f9fa;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.chatbot-message {
  display: flex;
  gap: 8px;
  max-width: 85%;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.chatbot-message.user {
  align-self: flex-end;
  flex-direction: row-reverse;
}

.message-avatar {
  width: 28px;
  height: 28px;
  border-radius: 50%;
  background-color: #e9ecef;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.chatbot-message.bot .message-avatar {
  background-color: #3498db;
  color: white;
}

.chatbot-message.user .message-avatar {
  background-color: #6c757d;
  color: white;
}

.message-content {
  background-color: white;
  padding: 10px 12px;
  border-radius: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.chatbot-message.bot .message-content {
  border-top-left-radius: 4px;
  background-color: #e9f5ff;
}

.chatbot-message.user .message-content {
  border-top-right-radius: 4px;
  background-color: #3498db;
  color: white;
}

.message-text {
  font-size: 14px;
  line-height: 1.4;
  white-space: pre-wrap;
}

.message-time {
  font-size: 10px;
  opacity: 0.7;
  margin-top: 4px;
  text-align: right;
}

/* Typing Indicator */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 5px 0;
}

.typing-indicator span {
  width: 8px;
  height: 8px;
  background-color: #3498db;
  border-radius: 50%;
  display: inline-block;
  animation: typing 1.4s infinite ease-in-out both;
}

.typing-indicator span:nth-child(1) {
  animation-delay: 0s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: 0.2s;
}

.typing-indicator span:nth-child(3) {
  animation-delay: 0.4s;
}

@keyframes typing {
  0%, 80%, 100% { transform: scale(0.6); opacity: 0.6; }
  40% { transform: scale(1); opacity: 1; }
}

/* Input Area */
.chatbot-input-area {
  padding: 10px 15px 15px;
  border-top: 1px solid #e9ecef;
  background-color: white;
}

.chatbot-input-container {
  display: flex;
  gap: 8px;
  background-color: #f1f3f5;
  border-radius: 24px;
  padding: 8px 16px;
  align-items: center;
}

.chatbot-input {
  flex: 1;
  border: none;
  background: none;
  font-size: 14px;
  resize: none;
  max-height: 100px;
  outline: none;
  font-family: inherit;
  padding: 4px 0;
}

.chatbot-send-btn {
  background: none;
  border: none;
  color: #3498db;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.chatbot-send-btn:hover {
  background-color: rgba(52, 152, 219, 0.1);
}

.chatbot-send-btn:disabled {
  color: #adb5bd;
  cursor: not-allowed;
}

/* Suggestions */
.chatbot-suggestions {
  padding: 10px;
  background-color: white;
  border-radius: 12px;
  margin: 10px 0;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.chatbot-suggestions p {
  font-size: 12px;
  color: #6c757d;
  margin: 0 0 8px 0;
}

.suggestion-btn {
  background-color: #e9f5ff;
  border: none;
  border-radius: 16px;
  padding: 6px 12px;
  margin: 4px 4px 4px 0;
  font-size: 12px;
  color: #3498db;
  cursor: pointer;
  transition: background-color 0.2s;
}

.suggestion-btn:hover {
  background-color: #d0e8ff;
}

/* Clear Chat Button */
.clear-chat-btn {
  background: none;
  border: none;
  color: #6c757d;
  font-size: 12px;
  cursor: pointer;
  padding: 4px 0;
  margin-bottom: 8px;
  text-align: right;
  width: 100%;
  transition: color 0.2s;
}

.clear-chat-btn:hover {
  color: #343a40;
  text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 576px) {
  .chatbot-container {
    width: calc(100% - 40px);
    height: 60vh;
    bottom: 10px;
    right: 20px;
  }
  
  .chatbot-float-button {
    bottom: 10px;
    right: 10px;
  }
}
